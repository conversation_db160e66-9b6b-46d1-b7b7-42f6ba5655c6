package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/cognitoidentityprovider"
	"github.com/aws/aws-sdk-go/service/ivs"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/gin-contrib/static"
	"github.com/gin-gonic/gin"
	"github.com/go-sql-driver/mysql"
	"github.com/golang-jwt/jwt/v4"
	"github.com/gorilla/websocket"
	"github.com/joho/godotenv"
	"golang.org/x/crypto/bcrypt"
)

// Database models
type Category struct {
	ID          int       `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	PhotoURL    string    `json:"photo_url" db:"photo_url"`
	IsEnabled   bool      `json:"is_enabled" db:"is_enabled"`
	Viewers     int       `json:"viewers" db:"viewers"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

type Stream struct {
	ID          string    `json:"id" db:"id"`
	Title       string    `json:"title" db:"title"`
	Description string    `json:"description" db:"description"`
	StreamerID  string    `json:"streamer_id" db:"streamer_id"`
	StreamerName string   `json:"streamer_name" db:"streamer_name"`
	CategoryID  int       `json:"category_id" db:"category_id"`
	CategoryName string   `json:"category_name" db:"category_name"`
	Viewers     int       `json:"viewers" db:"viewers"`
	IsLive      bool      `json:"is_live" db:"is_live"`
	StreamKey   string    `json:"stream_key" db:"stream_key"`
	ThumbnailURL string   `json:"thumbnail_url" db:"thumbnail_url"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

type User struct {
	ID          string    `json:"id" db:"id"`
	Username    string    `json:"username" db:"username"`
	Email       string    `json:"email" db:"email"`
	DisplayName string    `json:"display_name" db:"display_name"`
	AvatarURL   string    `json:"avatar_url" db:"avatar_url"`
	Status      string    `json:"status" db:"status"` // active, banned, suspended
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// getCurrentUser gets the current user from JWT cookie (same as other routes)
func getCurrentUser(c *gin.Context) *User {
	var userID, username string

	log.Printf("🔍 getCurrentUser: Checking JWT authentication...")

	// Get JWT token from cookie (same as other routes)
	tokenString, err := c.Cookie("auth_token")
	log.Printf("🔍 JWT Cookie: err=%v, tokenLength=%d", err, len(tokenString))

	if err == nil && tokenString != "" {
		log.Printf("🔍 JWT token found, validating...")
		// Validate JWT token
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			return jwtSecret, nil
		})

		log.Printf("🔍 JWT Parse: err=%v, valid=%v", err, token != nil && token.Valid)

		if err == nil && token.Valid {
			if claims, ok := token.Claims.(jwt.MapClaims); ok {
				log.Printf("🔍 JWT Claims: %v", claims)
				if uid, exists := claims["user_id"]; exists {
					userID = uid.(string)
				}
				if uname, exists := claims["username"]; exists {
					username = uname.(string)
				}
				log.Printf("✅ User authenticated via JWT: %s (ID: %s)", username, userID)
			}
		}
	} else {
		log.Printf("❌ No JWT cookie found or empty")
	}

	if userID == "" {
		log.Printf("🔍 No authenticated user found - returning nil")
		return nil
	}

	log.Printf("🔍 Final authenticated user - ID: '%s', Username: '%s'", userID, username)
	return &User{
		ID:       userID,
		Username: username,
		Email:    username + "@example.com",
	}
}

// Simple JWT token decoder (for development/testing)
// In production, this should properly validate the JWT signature
func decodeJWTToken(token string) *User {
	// Split the JWT token into parts
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		log.Printf("DEBUG: Invalid JWT token format")
		return nil
	}

	// Decode the payload (second part)
	payload := parts[1]
	// Add padding if needed
	for len(payload)%4 != 0 {
		payload += "="
	}

	decoded, err := base64.URLEncoding.DecodeString(payload)
	if err != nil {
		log.Printf("DEBUG: Failed to decode JWT payload: %v", err)
		return nil
	}

	// Parse the JSON payload
	var claims map[string]interface{}
	if err := json.Unmarshal(decoded, &claims); err != nil {
		log.Printf("DEBUG: Failed to parse JWT claims: %v", err)
		return nil
	}

	log.Printf("DEBUG: JWT claims: %+v", claims)

	// Extract user information from claims
	var userID, username string

	// Try different claim names that might contain user info
	if sub, ok := claims["sub"].(string); ok {
		username = sub
		userID = sub
	}
	if cognito_username, ok := claims["cognito:username"].(string); ok {
		username = cognito_username
		userID = cognito_username
	}
	if email, ok := claims["email"].(string); ok && username == "" {
		username = email
		userID = email
	}

	if userID != "" {
		return &User{
			ID:       userID,
			Username: username,
			Email:    username + "@example.com",
		}
	}

	return nil
}

type Video struct {
	ID          int       `json:"id" db:"id"`
	VideoID     string    `json:"video_id" db:"video_id"` // Unique video identifier
	Title       string    `json:"title" db:"title"`
	Description string    `json:"description" db:"description"`
	CreatorID   string    `json:"creator_id" db:"creator_id"`
	CreatorName string    `json:"creator_name" db:"creator_name"`
	CategoryID  int       `json:"category_id" db:"category_id"`
	CategoryName string   `json:"category_name" db:"category_name"`
	Duration    int       `json:"duration" db:"duration"` // in seconds
	Views       int       `json:"views" db:"views"`
	VideoURL    string    `json:"video_url" db:"video_url"`
	ThumbnailURL string   `json:"thumbnail_url" db:"thumbnail_url"`
	Status      string    `json:"status" db:"status"` // processing, completed, failed
	ProcessingStage string `json:"processing_stage" db:"processing_stage"` // upload, encoding, uploading_s3, completed
	OriginalFilename string `json:"original_filename" db:"original_filename"`
	FileSize    int64     `json:"file_size" db:"file_size"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

type VOD struct {
	ID              int       `json:"id" db:"id"`
	VodID           string    `json:"vod_id" db:"vod_id"`
	StreamID        string    `json:"stream_id" db:"stream_id"`
	Title           string    `json:"title" db:"title"`
	Description     string    `json:"description" db:"description"`
	StreamerID      string    `json:"streamer_id" db:"streamer_id"`
	StreamerName    string    `json:"streamer_name" db:"streamer_name"`
	CategoryID      int       `json:"category_id" db:"category_id"`
	CategoryName    string    `json:"category_name" db:"category_name"`
	Duration        int       `json:"duration" db:"duration"` // in seconds
	Views           int       `json:"views" db:"views"`
	VideoURL        string    `json:"video_url" db:"video_url"`
	ThumbnailURL    string    `json:"thumbnail_url" db:"thumbnail_url"`
	S3RecordingKey  string    `json:"s3_recording_key" db:"s3_recording_key"`
	RecordingStatus string    `json:"recording_status" db:"recording_status"`
	StreamStartedAt time.Time `json:"stream_started_at" db:"stream_started_at"`
	StreamEndedAt   time.Time `json:"stream_ended_at" db:"stream_ended_at"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time `json:"updated_at" db:"updated_at"`
}

type VideoComment struct {
	ID        int       `json:"id" db:"id"`
	VideoID   string    `json:"video_id" db:"video_id"`
	UserID    string    `json:"user_id" db:"user_id"`
	Username  string    `json:"username" db:"username"`
	Comment   string    `json:"comment" db:"comment"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// Admin user structure
type AdminUser struct {
	Username     string `json:"username"`
	PasswordHash string `json:"password_hash"`
	Email        string `json:"email"`
	Role         string `json:"role"`
}

// WebSocket Chat structures
type ChatMessage struct {
	ID        int       `json:"id"`
	StreamID  string    `json:"stream_id"`
	UserID    string    `json:"user_id"`
	Username  string    `json:"username"`
	Message   string    `json:"message"`
	CreatedAt time.Time `json:"created_at"`
	Type      string    `json:"type"` // "message", "join", "leave"
}

type ChatClient struct {
	conn     *websocket.Conn
	send     chan ChatMessage
	room     *ChatRoom
	userID   string
	username string
}

type ChatRoom struct {
	streamID string
	clients  map[*ChatClient]bool
	broadcast chan ChatMessage
	register chan *ChatClient
	unregister chan *ChatClient
}

// Global variables for configuration
var (
	cognitoClient      *cognitoidentityprovider.CognitoIdentityProvider
	ivsClient          *ivs.IVS
	s3Client           *s3.S3
	db                 *sql.DB
	userPoolID         string
	clientID           string
	clientSecret       string
	siteName           string
	enableGoogleLogin  bool
	enableFacebookLogin bool
	enableAppleLogin   bool
	adminUsers         = make(map[string]AdminUser) // In-memory admin users store
	jwtSecret          = []byte("your-super-secret-jwt-key-change-in-production") // Change this in production!

	// WebSocket configuration
	upgrader     = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // Allow all origins for development
		},
	}
	chatRooms = make(map[string]*ChatRoom) // streamID -> ChatRoom

	// Database configuration
	dbHost     string
	dbPort     string
	dbUser     string
	dbPassword string
	dbName     string

	// S3 configuration
	s3Bucket string
	s3Region string
)

// WebSocket Chat Room methods
func newChatRoom(streamID string) *ChatRoom {
	return &ChatRoom{
		streamID:   streamID,
		clients:    make(map[*ChatClient]bool),
		broadcast:  make(chan ChatMessage),
		register:   make(chan *ChatClient),
		unregister: make(chan *ChatClient),
	}
}

func (room *ChatRoom) run() {
	for {
		select {
		case client := <-room.register:
			room.clients[client] = true
			log.Printf("Client connected to room %s. Total clients: %d", room.streamID, len(room.clients))

			// Send join message
			joinMsg := ChatMessage{
				StreamID:  room.streamID,
				Username:  client.username,
				Message:   "joined the chat",
				Type:      "join",
				CreatedAt: time.Now(),
			}
			room.broadcast <- joinMsg

		case client := <-room.unregister:
			if _, ok := room.clients[client]; ok {
				delete(room.clients, client)
				close(client.send)
				log.Printf("Client disconnected from room %s. Total clients: %d", room.streamID, len(room.clients))

				// Send leave message
				leaveMsg := ChatMessage{
					StreamID:  room.streamID,
					Username:  client.username,
					Message:   "left the chat",
					Type:      "leave",
					CreatedAt: time.Now(),
				}
				room.broadcast <- leaveMsg
			}

		case message := <-room.broadcast:
			// Save message to database if it's a regular message
			if message.Type == "message" {
				_, err := db.Exec(`
					INSERT INTO chat_messages (stream_id, user_id, username, message)
					VALUES (?, ?, ?, ?)
				`, message.StreamID, message.UserID, message.Username, message.Message)
				if err != nil {
					log.Printf("Error saving chat message: %v", err)
				}
			}

			// Broadcast to all clients in the room
			for client := range room.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(room.clients, client)
				}
			}
		}
	}
}

func (client *ChatClient) readPump() {
	defer func() {
		client.room.unregister <- client
		client.conn.Close()
	}()

	client.conn.SetReadLimit(512)
	client.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	client.conn.SetPongHandler(func(string) error {
		client.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		var msg ChatMessage
		err := client.conn.ReadJSON(&msg)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		// Set message metadata
		msg.StreamID = client.room.streamID
		msg.UserID = client.userID
		msg.Username = client.username
		msg.CreatedAt = time.Now()
		msg.Type = "message"

		client.room.broadcast <- msg
	}
}

func (client *ChatClient) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		client.conn.Close()
	}()

	for {
		select {
		case message, ok := <-client.send:
			client.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				client.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := client.conn.WriteJSON(message); err != nil {
				log.Printf("WebSocket write error: %v", err)
				return
			}

		case <-ticker.C:
			client.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := client.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// AWS WebSocket read pump (similar to AWS Lambda handleMessage)
func (client *ChatClient) readAWSPump() {
	log.Printf("AWS WebSocket read pump started for user: %s", client.username)

	defer func() {
		log.Printf("AWS WebSocket read pump ending for user: %s", client.username)
		client.room.unregister <- client
		client.conn.Close()
	}()

	client.conn.SetReadLimit(512)
	client.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	client.conn.SetPongHandler(func(string) error {
		client.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	log.Printf("AWS WebSocket read pump configured for user: %s, starting message loop", client.username)

	for {
		// AWS WebSocket message format
		var awsMessage struct {
			Action    string `json:"action"`
			Message   string `json:"message"`
			Username  string `json:"username"`
			StreamID  string `json:"streamId"`
			Timestamp string `json:"timestamp"`
		}

		err := client.conn.ReadJSON(&awsMessage)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("AWS WebSocket error: %v", err)
			}
			break
		}

		log.Printf("AWS WebSocket received action: %s, message: '%s' from user: %s", awsMessage.Action, awsMessage.Message, client.username)
		log.Printf("AWS WebSocket full message data: %+v", awsMessage)

		// Handle different actions (similar to AWS Lambda switch statement)
		switch awsMessage.Action {
		case "connect":
			log.Printf("AWS WebSocket connect action from user: %s", client.username)
			// Connection established - send welcome message
			welcomeMsg := ChatMessage{
				StreamID:  client.room.streamID,
				UserID:    "system",
				Username:  "System",
				Message:   fmt.Sprintf("%s joined the chat", client.username),
				CreatedAt: time.Now(),
				Type:      "system",
			}
			client.room.broadcast <- welcomeMsg

		case "sendmessage":
			if awsMessage.Message == "" {
				continue
			}

			// Create chat message (similar to AWS Lambda BroadcastMessage)
			msg := ChatMessage{
				StreamID:  client.room.streamID,
				UserID:    client.userID,
				Username:  client.username,
				Message:   awsMessage.Message,
				CreatedAt: time.Now(),
				Type:      "message",
			}

			// Save to database
			if db != nil {
				_, err := db.Exec(`
					INSERT INTO chat_messages (stream_id, user_id, username, message, created_at)
					VALUES (?, ?, ?, ?, ?)
				`, msg.StreamID, msg.UserID, msg.Username, msg.Message, msg.CreatedAt)
				if err != nil {
					log.Printf("Error saving AWS chat message: %v", err)
				}
			}

			// Broadcast to all clients in room
			select {
			case client.room.broadcast <- msg:
				log.Printf("AWS WebSocket broadcasting message from %s: %s to %d clients", client.username, awsMessage.Message, len(client.room.clients))
			default:
				log.Printf("AWS WebSocket broadcast channel full, dropping message from %s", client.username)
			}

		default:
			log.Printf("Unknown AWS WebSocket action: %s", awsMessage.Action)
		}
	}
}

// AWS WebSocket write pump (similar to AWS Lambda PostToConnection)
func (client *ChatClient) writeAWSPump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		client.conn.Close()
	}()

	for {
		select {
		case message, ok := <-client.send:
			client.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				client.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			// Convert to AWS WebSocket format (similar to BroadcastMessage struct)
			awsMessage := map[string]interface{}{
				"message":    message.Message,
				"sender":     message.UserID,
				"senderName": message.Username,
				"timestamp":  message.CreatedAt.Format(time.RFC3339),
				"action":     "broadcast",
			}

			if err := client.conn.WriteJSON(awsMessage); err != nil {
				log.Printf("AWS WebSocket write error: %v", err)
				return
			}
			log.Printf("AWS WebSocket sent message to client %s: %s", client.username, message.Message)

		case <-ticker.C:
			client.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := client.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// Create Amazon IVS channel
func createIVSChannel(streamID, title string) (playbackUrl, ingestEndpoint, streamKey, channelArn string, err error) {
	// Production mode - Create real Amazon IVS channel
	log.Printf("Creating real Amazon IVS channel for stream: %s", streamID)

	// Sanitize channel name for IVS (only alphanumeric, hyphens, underscores)
	re := regexp.MustCompile(`[^a-zA-Z0-9-_]`)
	sanitizedName := re.ReplaceAllString(title, "_")
	if sanitizedName == "" {
		sanitizedName = streamID // Fallback to stream ID if title is empty after sanitization
	}

	log.Printf("Sanitized channel name: %s", sanitizedName)

	// Ensure recording config is ready and attach at creation to avoid race
	log.Printf("🎬 Preparing recording configuration before channel creation...")
	recordingConfigArn, rcErr := getOrCreateGlobalRecordingConfiguration()
	if rcErr != nil {
		return "", "", "", "", fmt.Errorf("failed to prepare recording configuration: %v", rcErr)
	}
	log.Printf("✅ Using recording configuration: %s", recordingConfigArn)

	// Verify recording configuration is ACTIVE before attaching; otherwise create without it
	attachArn := aws.String(recordingConfigArn)
	rcState := "UNKNOWN"
	if recordingConfigArn != "" {
		getRCRes, rcGetErr := ivsClient.GetRecordingConfiguration(&ivs.GetRecordingConfigurationInput{Arn: aws.String(recordingConfigArn)})
		if rcGetErr != nil {
			log.Printf("⚠️ Could not get recording configuration status: %v", rcGetErr)
		} else if getRCRes != nil && getRCRes.RecordingConfiguration != nil && getRCRes.RecordingConfiguration.State != nil {
			rcState = *getRCRes.RecordingConfiguration.State
		}
	}
	if rcState != "ACTIVE" {
		log.Printf("⚠️ Recording config state is %s; creating channel without it to avoid failure", rcState)
		attachArn = nil
	}

	// Create IVS channel (attach recording config only if ACTIVE)
	createChannelInput := &ivs.CreateChannelInput{
		Name: &sanitizedName,
		Type: aws.String("STANDARD"), // STANDARD supports recording
		RecordingConfigurationArn: attachArn,
		Tags: map[string]*string{
			"StreamID": &streamID,
			"Service":  aws.String("MoneyBags"),
			"Title":    &title, // Store original title in tags
		},
	}

	// Try to create channel with recording configuration, with retries if config is still CREATING
	var result *ivs.CreateChannelOutput
	for attempt := 1; attempt <= 3; attempt++ {
		result, err = ivsClient.CreateChannel(createChannelInput)
		if err == nil {
			break
		}
		// If recording config not ACTIVE yet, wait and retry
		if awsErr, ok := err.(awserr.Error); ok && strings.Contains(strings.ToLower(awsErr.Message()), "invalid state: creating") {
			log.Printf("⚠️ Recording config not ACTIVE yet (attempt %d/3). Waiting 5s and retrying...", attempt)
			time.Sleep(5 * time.Second)
			continue
		}
		break
	}
	if err != nil {
		log.Printf("❌ CreateChannel with recording config failed: %v", err)
		log.Printf("↩️ Falling back to creating channel WITHOUT recording; will enable after creation")
		createChannelInput.RecordingConfigurationArn = nil
		result, err = ivsClient.CreateChannel(createChannelInput)
		if err != nil {
			return "", "", "", "", fmt.Errorf("failed to create IVS channel: %v", err)
		}
		// Background enablement will be triggered by caller after DB insert if needed
	}

	// Extract channel information
	channel := result.Channel
	streamKeyObj := result.StreamKey

	playbackUrl = *channel.PlaybackUrl
	ingestEndpoint = *channel.IngestEndpoint
	streamKey = *streamKeyObj.Value
	channelArn = *channel.Arn

	log.Printf("Created real IVS channel: %s", channelArn)
	log.Printf("Playback URL: %s", playbackUrl)
	log.Printf("Ingest Endpoint: %s", ingestEndpoint)

	return playbackUrl, ingestEndpoint, streamKey, channelArn, nil
}

// Get or create a reusable IVS channel for a user
func getOrCreateUserChannel(userID, username, streamID, title string) (playbackUrl, ingestEndpoint, streamKey, channelArn string, err error) {
	// First, check if user already has a channel
	var existingChannelArn, existingPlaybackUrl, existingIngestEndpoint, existingStreamKey string

	err = db.QueryRow(`
		SELECT channel_arn, playback_url, ingest_endpoint, stream_key
		FROM user_channels
		WHERE user_id = ? AND is_active = TRUE
		LIMIT 1
	`, userID).Scan(&existingChannelArn, &existingPlaybackUrl, &existingIngestEndpoint, &existingStreamKey)

	if err == nil {
		// User has an existing channel, verify it's still valid in AWS
		log.Printf("Found existing channel for user %s: %s", username, existingChannelArn)

		// Verify channel still exists in AWS IVS
		if verifyIVSChannel(existingChannelArn) {
			log.Printf("✅ Reusing existing IVS channel for user %s", username)
			return existingPlaybackUrl, existingIngestEndpoint, existingStreamKey, existingChannelArn, nil
		} else {
			// Channel no longer exists, mark as inactive
			log.Printf("⚠️ Existing channel no longer valid, creating new one")
			db.Exec("UPDATE user_channels SET is_active = FALSE WHERE user_id = ?", userID)
		}
	}

	// Create new channel
	log.Printf("Creating new IVS channel for user %s", username)
	playbackUrl, ingestEndpoint, streamKey, channelArn, err = createIVSChannel(streamID, title)
	if err != nil {
		return "", "", "", "", err
	}

	// Store the new channel for future reuse
	_, dbErr := db.Exec(`
		INSERT INTO user_channels (user_id, username, channel_arn, playback_url, ingest_endpoint, stream_key, is_active, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, TRUE, NOW(), NOW())
		ON DUPLICATE KEY UPDATE
		channel_arn = VALUES(channel_arn),
		playback_url = VALUES(playback_url),
		ingest_endpoint = VALUES(ingest_endpoint),
		stream_key = VALUES(stream_key),
		is_active = TRUE,
		updated_at = NOW()
	`, userID, username, channelArn, playbackUrl, ingestEndpoint, streamKey)

	if dbErr != nil {
		log.Printf("Warning: Could not store user channel for reuse: %v", dbErr)
		// Don't fail the stream creation, just log the warning
	}

	log.Printf("✅ Created and stored new IVS channel for user %s", username)
	return playbackUrl, ingestEndpoint, streamKey, channelArn, nil
}

// Verify if an IVS channel still exists and is valid
func verifyIVSChannel(channelArn string) bool {
	if ivsClient == nil {
		return false
	}

	input := &ivs.GetChannelInput{
		Arn: aws.String(channelArn),
	}

	_, err := ivsClient.GetChannel(input)
	return err == nil
}

func main() {
	// Load environment variables
	err := godotenv.Load()
	if err != nil {
		log.Println("Warning: .env file not found, using system environment variables")
	}

	// Load AWS WebSocket configuration
	awsWebSocketURL := os.Getenv("AWS_WEBSOCKET_API_URL")
	awsWebSocketStage := os.Getenv("AWS_WEBSOCKET_STAGE")
	if awsWebSocketStage == "" {
		awsWebSocketStage = "production"
	}
	log.Printf("AWS WebSocket URL: %s", awsWebSocketURL)
	log.Printf("AWS WebSocket Stage: %s", awsWebSocketStage)

	// Initialize configuration
	initConfig()

	// Initialize AWS services
	initCognito()
	initS3()
	initDatabase()

	// Initialize admin users
	initAdminUsers()

	// Create Gin router
	r := gin.Default()

	// Serve static files (CSS, JS, images)
	r.Use(static.Serve("/static", static.LocalFile("./static", false)))

	// Serve favicon
	r.GET("/favicon.ico", func(c *gin.Context) {
		c.File("./static/favicon.ico")
	})



	// Load HTML templates
	r.LoadHTMLGlob("templates/*.html")

	// Routes
	r.GET("/", func(c *gin.Context) {
		// Check if user is authenticated by validating JWT cookie
		var userID, username, email string
		var isAuthenticated bool

		log.Printf("🔍 Main page: Checking authentication...")

		// Get JWT token from cookie
		tokenString, err := c.Cookie("auth_token")
		log.Printf("🔍 JWT Cookie: err=%v, tokenLength=%d", err, len(tokenString))

		if err == nil && tokenString != "" {
			log.Printf("🔍 JWT token found, validating...")
			// Validate JWT token
			token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
				if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
					return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
				}
				return jwtSecret, nil
			})

			log.Printf("🔍 JWT Parse: err=%v, valid=%v", err, token != nil && token.Valid)

			if err == nil && token.Valid {
				if claims, ok := token.Claims.(jwt.MapClaims); ok {
					log.Printf("🔍 JWT Claims: %+v", claims)
					if userIDFloat, ok := claims["user_id"].(float64); ok {
						userID = fmt.Sprintf("%.0f", userIDFloat)
					}
					if usernameStr, ok := claims["username"].(string); ok {
						username = usernameStr
					}
					if emailStr, ok := claims["email"].(string); ok {
						email = emailStr
					}
					isAuthenticated = true
					log.Printf("✅ User authenticated: %s (%s) - ID: %s", username, email, userID)
				} else {
					log.Printf("❌ Failed to parse JWT claims")
				}
			} else {
				log.Printf("❌ JWT token invalid or parse error: %v", err)
			}
		} else {
			log.Printf("❌ No JWT cookie found or empty")
		}

		log.Printf("🔍 Final auth state: isAuthenticated=%v, username=%s", isAuthenticated, username)

		// Render template with authentication state
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title":               "Live Streaming Platform",
			"siteName":            siteName,
			"enableGoogleLogin":   enableGoogleLogin,
			"enableFacebookLogin": enableFacebookLogin,
			"enableAppleLogin":    enableAppleLogin,
			"isAuthenticated":     isAuthenticated,
			"userID":              userID,
			"username":            username,
			"email":               email,
		})
	})

	// Admin routes
	r.GET("/admin", func(c *gin.Context) {
		c.HTML(http.StatusOK, "admin-login.html", gin.H{
			"title":    "Admin Login",
			"siteName": siteName,
		})
	})

	r.GET("/admin/dashboard", func(c *gin.Context) {
		c.HTML(http.StatusOK, "admin-dashboard.html", gin.H{
			"title":    "Admin Dashboard",
			"siteName": siteName,
		})
	})

	// Login page route
	r.GET("/login", func(c *gin.Context) {
		// Check if user is already authenticated
		tokenString, err := c.Cookie("auth_token")
		if err == nil {
			// Validate JWT token
			_, err := validateJWTToken(tokenString)
			if err == nil {
				// User is already authenticated, redirect to dashboard or home
				redirectUrl := c.Query("redirect")
				if redirectUrl == "" {
					redirectUrl = "/"
				}
				c.Redirect(http.StatusFound, redirectUrl)
				return
			}
		}

		// User is not authenticated, show login page
		c.HTML(http.StatusOK, "login.html", gin.H{
			"title":    "Login",
			"siteName": siteName,
		})
	})

	// Following page route
	r.GET("/following", func(c *gin.Context) {
		// Check for JWT token in cookie
		tokenString, err := c.Cookie("auth_token")
		if err != nil {
			// No token found, redirect to login page with redirect parameter
			c.Redirect(http.StatusFound, "/login?redirect=/following")
			return
		}

		// Validate JWT token
		claims, err := validateJWTToken(tokenString)
		if err != nil {
			// Invalid token, redirect to login page with redirect parameter
			c.Redirect(http.StatusFound, "/login?redirect=/following")
			return
		}

		// Get user information from database
		var userID, username, email string
		err = db.QueryRow("SELECT id, username, email FROM users WHERE username = ?", claims.Username).Scan(&userID, &username, &email)
		if err != nil {
			// User not found in database, redirect to login page
			c.Redirect(http.StatusFound, "/login?redirect=/following")
			return
		}

		// User is authenticated, show following page
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title":           "Following - Live Streaming Platform",
			"siteName":        siteName,
			"userID":          userID,
			"username":        username,
			"email":           email,
			"isAuthenticated": true,
			"pageType":        "following", // Add page type to differentiate
		})
	})

	// Browse page route - shows all VODs using home page template
	r.GET("/browse", func(c *gin.Context) {
		// Check if user is authenticated by validating JWT cookie
		var userID, username, email string
		var isAuthenticated bool

		log.Printf("🔍 Browse page: Checking authentication...")

		// Get JWT token from cookie
		tokenString, err := c.Cookie("auth_token")
		log.Printf("🔍 JWT Cookie: err=%v, tokenLength=%d", err, len(tokenString))

		if err == nil && tokenString != "" {
			log.Printf("🔍 JWT token found, validating...")
			// Validate JWT token
			token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
				if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
					return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
				}
				return jwtSecret, nil
			})

			log.Printf("🔍 JWT Parse: err=%v, valid=%v", err, token != nil && token.Valid)

			if err == nil && token.Valid {
				if claims, ok := token.Claims.(jwt.MapClaims); ok {
					log.Printf("🔍 JWT Claims: %+v", claims)
					if userIDFloat, ok := claims["user_id"].(float64); ok {
						userID = fmt.Sprintf("%.0f", userIDFloat)
					}
					if usernameStr, ok := claims["username"].(string); ok {
						username = usernameStr
					}
					if emailStr, ok := claims["email"].(string); ok {
						email = emailStr
					}
					isAuthenticated = true
					log.Printf("✅ User authenticated: %s (%s) - ID: %s", username, email, userID)
				} else {
					log.Printf("❌ Failed to parse JWT claims")
				}
			} else {
				log.Printf("❌ JWT token invalid or parse error: %v", err)
			}
		} else {
			log.Printf("❌ No JWT cookie found or empty")
		}

		log.Printf("🔍 Final auth state: isAuthenticated=%v, username=%s", isAuthenticated, username)

		// Get category filter from query parameter
		categoryFilter := c.Query("category")

		// Determine page title based on category filter
		pageTitle := "Browse VODs - Live Streaming Platform"
		if categoryFilter != "" {
			pageTitle = fmt.Sprintf("Browse %s VODs - Live Streaming Platform", categoryFilter)
		}

		// Render template with authentication state and browse page type
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title":               pageTitle,
			"siteName":            siteName,
			"enableGoogleLogin":   enableGoogleLogin,
			"enableFacebookLogin": enableFacebookLogin,
			"enableAppleLogin":    enableAppleLogin,
			"isAuthenticated":     isAuthenticated,
			"userID":              userID,
			"username":            username,
			"email":               email,
			"pageType":            "browse", // Add page type to differentiate for VODs
			"categoryFilter":      categoryFilter, // Pass category filter to template
		})
	})

	// Creator routes
	r.GET("/creator/dashboard", func(c *gin.Context) {
		// Check for JWT token in cookie
		tokenString, err := c.Cookie("auth_token")
		if err != nil {
			// No token found, redirect to login page with redirect parameter
			c.Redirect(http.StatusFound, "/login?redirect=/creator/dashboard")
			return
		}

		// Validate JWT token
		claims, err := validateJWTToken(tokenString)
		if err != nil {
			// Invalid token, redirect to login page with redirect parameter
			c.Redirect(http.StatusFound, "/login?redirect=/creator/dashboard")
			return
		}

		// Get user information from database
		var userID, username, email string
		err = db.QueryRow("SELECT id, username, email FROM users WHERE username = ?", claims.Username).Scan(&userID, &username, &email)
		if err != nil {
			// User not found in database, redirect to login page
			c.Redirect(http.StatusFound, "/login?redirect=/creator/dashboard")
			return
		}

		// User is authenticated, show creator dashboard
		c.HTML(http.StatusOK, "creator-dashboard.html", gin.H{
			"title":         "Creator Dashboard",
			"siteName":      siteName,
			"userID":        userID,
			"username":      username,
			"email":         email,
			"isAuthenticated": true,
		})
	})

	// Demo stream route for testing the new design
	r.GET("/stream/demo", func(c *gin.Context) {
		// Check if user is authenticated by validating JWT cookie (same as main page)
		var userID, username, email string
		var isAuthenticated bool

		log.Printf("🔍 Stream demo page: Checking authentication...")

		// Get JWT token from cookie
		tokenString, err := c.Cookie("auth_token")
		log.Printf("🔍 JWT Cookie: err=%v, tokenLength=%d", err, len(tokenString))

		if err == nil && tokenString != "" {
			log.Printf("🔍 JWT token found, validating...")
			// Validate JWT token
			token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
				if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
					return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
				}
				return jwtSecret, nil
			})

			log.Printf("🔍 JWT Parse: err=%v, valid=%v", err, token != nil && token.Valid)

			if err == nil && token.Valid {
				if claims, ok := token.Claims.(jwt.MapClaims); ok {
					log.Printf("🔍 JWT Claims: %v", claims)
					if uid, exists := claims["user_id"]; exists {
						userID = uid.(string)
					}
					if uname, exists := claims["username"]; exists {
						username = uname.(string)
					}
					if em, exists := claims["email"]; exists {
						email = em.(string)
					}
					isAuthenticated = true
					log.Printf("✅ User authenticated: %s (%s) - ID: %s", username, email, userID)
				}
			} else {
				log.Printf("❌ JWT token invalid or parse error: %v", err)
			}
		} else {
			log.Printf("❌ No JWT cookie found or empty")
		}

		log.Printf("🔍 Final auth state: isAuthenticated=%v, username=%s", isAuthenticated, username)

		// Demo stream data for testing the new design
		// For demo, try to get PokerPro_Daniel's avatar from database, otherwise use default
		var demoAvatar string = "/static/images/default-avatar.png"
		err = db.QueryRow("SELECT avatar_url FROM users WHERE username = ?", "PokerPro_Daniel").Scan(&demoAvatar)
		if err != nil {
			log.Printf("Demo stream: Could not find PokerPro_Daniel's avatar, using default: %v", err)
			demoAvatar = "/static/images/default-avatar.png"
		}

		streamData := gin.H{
			"title":             "Demo Live Stream - BetCenter.Live",
			"siteName":          siteName,
			"streamId":          "demo-stream",
			"streamTitle":       "🎰 High Stakes Poker Tournament - Final Table Action!",
			"streamerName":      "PokerPro_Daniel",
			"streamerAvatar":    demoAvatar,
			"categoryName":      "Poker Tournaments",
			"streamDescription": "Join me for an exciting high-stakes poker tournament! We're at the final table with $50,000 on the line. Professional commentary and strategy discussion throughout the game.",
			"viewerCount":       1247,
			"playbackUrl":       "https://demo-stream-url.com/playlist.m3u8",
			"isStreamer":        false, // Set to true to test streamer controls
			"streamKey":         "demo-stream-key",
			"ingestEndpoint":    "rtmps://demo-ingest.com/live",
			"isAuthenticated":   isAuthenticated,
			"username":          username,
		}

		c.HTML(http.StatusOK, "live-stream.html", streamData)
	})

	// Live stream routes
	r.GET("/stream/:id", func(c *gin.Context) {
		streamID := c.Param("id")

		// Get stream data from database
		var stream struct {
			ID              string    `json:"id"`
			Title           string    `json:"title"`
			Description     *string   `json:"description"`
			StreamerID      string    `json:"streamer_id"`
			StreamerName    string    `json:"streamer_name"`
			CategoryName    string    `json:"category_name"`
			Viewers         int       `json:"viewers"`
			PlaybackURL     string    `json:"playback_url"`
			IngestEndpoint  string    `json:"ingest_endpoint"`
			StreamKey       string    `json:"stream_key"`
			IsLive          bool      `json:"is_live"`
			StartedAt       time.Time `json:"started_at"`
			StreamerAvatar  *string   `json:"streamer_avatar"`
		}

		err := db.QueryRow(`
			SELECT s.id, s.title, s.description, s.streamer_id, s.streamer_name, s.category_name, s.viewers,
			       s.playback_url, s.ingest_endpoint, s.stream_key, s.is_live, s.started_at, u.avatar_url
			FROM streams s
			LEFT JOIN users u ON s.streamer_name = u.username
			WHERE s.id = ? AND s.is_live = TRUE
		`, streamID).Scan(&stream.ID, &stream.Title, &stream.Description, &stream.StreamerID, &stream.StreamerName,
			&stream.CategoryName, &stream.Viewers, &stream.PlaybackURL, &stream.IngestEndpoint,
			&stream.StreamKey, &stream.IsLive, &stream.StartedAt, &stream.StreamerAvatar)

		if err != nil {
			log.Printf("Error fetching stream %s: %v", streamID, err)
			c.HTML(http.StatusNotFound, "index.html", gin.H{
				"title":    "Stream Not Found",
				"siteName": siteName,
				"error":    "Stream not found or no longer live",
			})
			return
		}

		description := ""
		if stream.Description != nil {
			description = *stream.Description
		}

		// Check if user is authenticated by validating JWT cookie (same as main page)
		var userID, username, email string
		var isAuthenticated bool

		log.Printf("🔍 Stream page: Checking authentication...")

		// Get JWT token from cookie
		tokenString, err := c.Cookie("auth_token")
		log.Printf("🔍 JWT Cookie: err=%v, tokenLength=%d", err, len(tokenString))

		if err == nil && tokenString != "" {
			log.Printf("🔍 JWT token found, validating...")
			// Validate JWT token
			token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
				if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
					return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
				}
				return jwtSecret, nil
			})

			log.Printf("🔍 JWT Parse: err=%v, valid=%v", err, token != nil && token.Valid)

			if err == nil && token.Valid {
				if claims, ok := token.Claims.(jwt.MapClaims); ok {
					log.Printf("🔍 JWT Claims: %v", claims)
					if uid, exists := claims["user_id"]; exists {
						userID = uid.(string)
					}
					if uname, exists := claims["username"]; exists {
						username = uname.(string)
					}
					if em, exists := claims["email"]; exists {
						email = em.(string)
					}
					isAuthenticated = true
					log.Printf("✅ User authenticated: %s (%s) - ID: %s", username, email, userID)
				}
			} else {
				log.Printf("❌ JWT token invalid or parse error: %v", err)
			}
		} else {
			log.Printf("❌ No JWT cookie found or empty")
		}

		log.Printf("🔍 Final auth state: isAuthenticated=%v, username=%s", isAuthenticated, username)

		// Only the stream creator can broadcast
		isStreamer := (userID != "" && userID == stream.StreamerID)

		// Check if user wants to view as player (for RTMP streamers)
		viewMode := c.Query("viewMode")
		if viewMode == "player" {
			isStreamer = false // Force viewer mode to show video player
		}

		// Set streamer avatar - use uploaded avatar if available, otherwise default
		streamerAvatarURL := "/static/images/default-avatar.png"
		if stream.StreamerAvatar != nil && *stream.StreamerAvatar != "" {
			streamerAvatarURL = *stream.StreamerAvatar
		}

		streamData := gin.H{
			"title":             stream.Title + " - Live Stream - BetCenter.Live",
			"siteName":          siteName,
			"streamId":          stream.ID,
			"streamTitle":       stream.Title,
			"streamerName":      stream.StreamerName,
			"streamerAvatar":    streamerAvatarURL,
			"categoryName":      stream.CategoryName,
			"streamDescription": description,
			"viewerCount":       stream.Viewers,
			"playbackUrl":       stream.PlaybackURL,
			"isStreamer":        isStreamer,
			"streamKey":         stream.StreamKey,
			"ingestEndpoint":    stream.IngestEndpoint,
			"isAuthenticated":   isAuthenticated,
			"username":          username,
		}

		c.HTML(http.StatusOK, "live-stream.html", streamData)
	})

	// Video player route
	r.GET("/video/:id", videoPlayerPage)

	// VOD player route
	r.GET("/vod/:id", vodPlayerPage)

	// Debug route to list all videos
	r.GET("/debug/videos", func(c *gin.Context) {
		rows, err := db.Query("SELECT video_id, title, status, video_url, category_id, category_name FROM videos ORDER BY created_at DESC LIMIT 10")
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
			return
		}
		defer rows.Close()

		var videos []map[string]interface{}
		for rows.Next() {
			var videoID, title, status, videoURL string
			var categoryID sql.NullInt64
			var categoryName sql.NullString
			rows.Scan(&videoID, &title, &status, &videoURL, &categoryID, &categoryName)
			videos = append(videos, map[string]interface{}{
				"video_id": videoID,
				"title": title,
				"status": status,
				"video_url": videoURL,
				"category_id": categoryID.Int64,
				"category_name": categoryName.String,
			})
		}
		c.JSON(200, videos)
	})

	// Debug route to fix videos with missing category names
	r.GET("/debug/fix-categories", func(c *gin.Context) {
		if db == nil {
			c.JSON(500, gin.H{"error": "Database not available"})
			return
		}

		// Find videos with NULL category_name but valid category_id
		rows, err := db.Query(`
			SELECT video_id, category_id
			FROM videos
			WHERE category_id IS NOT NULL AND (category_name IS NULL OR category_name = '')
		`)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
			return
		}
		defer rows.Close()

		var fixed []string
		for rows.Next() {
			var videoID string
			var categoryID int
			if err := rows.Scan(&videoID, &categoryID); err != nil {
				continue
			}

			// Get category name
			var categoryName string
			err := db.QueryRow("SELECT name FROM categories WHERE id = ?", categoryID).Scan(&categoryName)
			if err != nil {
				log.Printf("Could not find category name for ID %d: %v", categoryID, err)
				continue
			}

			// Update video with category name
			_, err = db.Exec("UPDATE videos SET category_name = ? WHERE video_id = ?", categoryName, videoID)
			if err != nil {
				log.Printf("Could not update video %s: %v", videoID, err)
				continue
			}

			fixed = append(fixed, fmt.Sprintf("%s -> %s", videoID, categoryName))
		}

		c.JSON(200, gin.H{
			"message": "Category names fixed",
			"fixed_videos": fixed,
			"count": len(fixed),
		})
	})

	// Debug route to check VODs
	r.GET("/debug/vods", func(c *gin.Context) {
		if db == nil {
			c.JSON(500, gin.H{"error": "Database not available"})
			return
		}

		// First check if vods table exists and has data
		countRows, err := db.Query("SELECT COUNT(*) as total FROM vods")
		if err != nil {
			c.JSON(500, gin.H{"error": "Count query failed: " + err.Error()})
			return
		}
		defer countRows.Close()

		var totalCount int
		if countRows.Next() {
			countRows.Scan(&totalCount)
		}

		// Try a simpler query first to see what columns exist
		rows, err := db.Query(`
			SELECT vod_id, title, recording_status,
			       COALESCE(video_url, '') as video_url,
			       COALESCE(streamer_name, '') as streamer_name,
			       COALESCE(category_name, '') as category_name,
			       created_at
			FROM vods
			ORDER BY created_at DESC LIMIT 20
		`)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
			return
		}
		defer rows.Close()

		var vods []map[string]interface{}
		for rows.Next() {
			var vodID, title, status, videoURL, streamerName, categoryName, createdAt string
			err := rows.Scan(&vodID, &title, &status, &videoURL, &streamerName, &categoryName, &createdAt)
			if err != nil {
				log.Printf("Error scanning VOD row: %v", err)
				continue
			}
			vods = append(vods, map[string]interface{}{
				"vod_id":          vodID,
				"title":           title,
				"recording_status": status,
				"video_url":       videoURL,
				"streamer_name":   streamerName,
				"category_name":   categoryName,
				"created_at":      createdAt,
			})
		}
		c.JSON(200, gin.H{
			"vods": vods,
			"count": len(vods),
			"total_in_db": totalCount,
		})
	})

	// Debug route to fix VODs stuck in processing
	r.GET("/debug/fix-processing-vods", func(c *gin.Context) {
		if db == nil {
			c.JSON(500, gin.H{"error": "Database not available"})
			return
		}

		// Find VODs that have empty status or empty video_url and fix them
		rows, err := db.Query(`
			SELECT vod_id, COALESCE(video_url, '') as video_url, COALESCE(recording_status, '') as recording_status
			FROM vods
			WHERE COALESCE(recording_status, '') = '' OR COALESCE(video_url, '') = ''
		`)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
			return
		}
		defer rows.Close()

		var fixedVODs []string
		for rows.Next() {
			var vodID, videoURL, status string
			err := rows.Scan(&vodID, &videoURL, &status)
			if err != nil {
				continue
			}

			// Generate the expected video URL based on VOD ID
			expectedVideoURL := fmt.Sprintf("https://%s.s3.%s.amazonaws.com/videos/%s.mp4", s3Bucket, s3Region, vodID)

			// Update status to completed and set video URL
			_, err = db.Exec(`
				UPDATE vods
				SET recording_status = 'completed',
				    video_url = ?,
				    updated_at = NOW()
				WHERE vod_id = ?
			`, expectedVideoURL, vodID)
			if err != nil {
				log.Printf("Error fixing VOD %s: %v", vodID, err)
				continue
			}

			fixedVODs = append(fixedVODs, vodID)
			log.Printf("Fixed VOD %s: set status to completed and video_url to %s", vodID, expectedVideoURL)
		}

		c.JSON(200, gin.H{
			"message": "Fixed processing VODs",
			"fixed_vods": fixedVODs,
			"count": len(fixedVODs),
		})
	})

	// Debug route to create a test video
	r.GET("/debug/create-test-video", func(c *gin.Context) {
		testVideoID := "test_video_" + fmt.Sprintf("%d", time.Now().Unix())
		testVideoURL := "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
		testThumbnailURL := "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg"

		log.Printf("🎬 Creating test video with ID: %s", testVideoID)

		// Get current user for creator info
		user := getCurrentUser(c)
		creatorID := "admin"
		creatorName := "Admin User"
		if user != nil {
			creatorID = user.ID
			creatorName = user.Username
		}

		_, err := db.Exec(`
			INSERT INTO videos (video_id, title, description, creator_id, creator_name, category_id, category_name,
			                   duration, views, video_url, thumbnail_url, status, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
		`, testVideoID, "Test Video - Big Buck Bunny", "A test video for debugging video player functionality",
		   creatorID, creatorName, 1, "Sports Betting", 596, 0, testVideoURL, testThumbnailURL, "completed")

		if err != nil {
			log.Printf("❌ Error creating test video: %v", err)
			c.JSON(500, gin.H{"error": err.Error()})
			return
		}

		log.Printf("✅ Test video created successfully: %s", testVideoID)

		c.JSON(200, gin.H{
			"message": "Test video created successfully",
			"video_id": testVideoID,
			"video_url": testVideoURL,
			"player_url": "/video/" + testVideoID,
		})
	})

	// API routes for future implementation
	api := r.Group("/api")
            api.GET("/vod/hls/:vodID/manifest", vodHLSManifest)
            api.GET("/vod/hls/:vodID/segment", vodHLSSegment)
            // Video HLS endpoints (for regular uploaded videos)
            api.GET("/video/hls/:videoID/manifest", videoHLSManifest)
            api.GET("/video/hls/:videoID/segment", videoHLSSegment)

	{
		api.GET("/categories", getCategories)
		api.GET("/channels", getChannels)
		api.GET("/recommended-users", getRecommendedUsers)
		api.POST("/auth/login", login)
		api.POST("/auth/signup", signup)
		api.POST("/auth/confirm", confirmSignup)
		api.POST("/auth/resend", resendConfirmationCode)
		api.POST("/auth/social", socialLogin)
		api.POST("/auth/refresh", refreshToken)
		api.POST("/auth/logout", logout)

		// Creator API routes
		creatorAPI := api.Group("/creator")
		{
			creatorAPI.GET("/stats", getCreatorStats)
		}

		// Streaming API routes
		streamAPI := api.Group("/streams")
		{
			streamAPI.GET("/live", getLiveStreams)
			streamAPI.GET("/my", getMyStreams) // Get current user's streams
			streamAPI.POST("/start", startStream)
			streamAPI.POST("/stop/:id", stopStream)
			streamAPI.GET("/status/:id", getStreamStatus)
		}

		// Video API routes
		videoAPI := api.Group("/videos")
		{
			videoAPI.POST("/upload", uploadVideo)
			videoAPI.DELETE("/:id", deleteUserVideo)
			videoAPI.GET("/status/:id", getVideoStatus)
			videoAPI.GET("/:videoId/comments", getVideoComments)
			videoAPI.POST("/:videoId/comments", addVideoComment)
		}

		// VOD API routes
		vodAPI := api.Group("/vods")
		{
			vodAPI.GET("", getVODs)
			vodAPI.POST("/:id/complete", completeVODProcessing) // Manual completion for testing
		}

		// Content API routes (combined live streams and VODs)
		api.GET("/content", getContent)
		api.GET("/browse-content", getBrowseContent)
		api.GET("/vod-categories", getVODCategories) // VODs only for browse page

		// Social API routes
		socialAPI := api.Group("/social")
		{
			socialAPI.POST("/follow", followUser)
			socialAPI.POST("/unfollow", unfollowUser)
			socialAPI.GET("/following/:userId", getFollowing)
			socialAPI.GET("/followers/:userId", getFollowers)
			socialAPI.POST("/like", likeVideo)
			socialAPI.POST("/unlike", unlikeVideo)
			socialAPI.GET("/likes/:videoId", getVideoLikes)
		}

            // Settings API routes
            settingsAPI := api.Group("/settings")
            {
                settingsAPI.GET("/profile", getUserProfile)
                settingsAPI.POST("/profile", saveProfileSettings)
                settingsAPI.POST("/avatar", uploadAvatar)
                settingsAPI.POST("/banner", uploadBanner)
                settingsAPI.POST("/change-password", changePassword)
            }


		// Chat API routes
		chatAPI := api.Group("/chat")
		{
			chatAPI.GET("/:streamId/messages", getChatMessages)
			chatAPI.POST("/:streamId/send", sendChatMessage)
			chatAPI.GET("/:streamId/ws", handleAWSWebSocket)
			chatAPI.GET("/config", getChatConfig)
		}

		// Admin API routes
		adminAPI := api.Group("/admin")
		{
			adminAPI.POST("/login", adminLogin)

			// Protected admin routes
			protected := adminAPI.Group("/", adminAuthMiddleware())
			{
				protected.GET("/users", getUsers)
				protected.GET("/users/:id", getUserDetails)
				protected.POST("/users", createUser)
				protected.PUT("/users/:id", updateUser)
				protected.POST("/users/:id/ban", banUser)
				protected.POST("/users/:id/unban", unbanUser)
				protected.POST("/users/:id/avatar", adminUploadUserAvatar)
				protected.POST("/users/:id/banner", adminUploadUserBanner)
				protected.POST("/users/:id/profile", adminUpdateUserProfile)
				protected.POST("/upload-vod/:username", uploadVODForUser)
				protected.GET("/categories", getAdminCategories)
				protected.GET("/categories/:id", getAdminCategory)
				protected.POST("/categories", createCategory)
				protected.PUT("/categories/:id", updateCategory)
				protected.POST("/categories/:id/toggle", toggleCategory)
				protected.DELETE("/categories/:id", deleteCategory)
				protected.POST("/upload", uploadFile)
				protected.GET("/streams", getAdminStreams)
				protected.DELETE("/streams/:id", deleteStream)
				protected.GET("/videos", getAdminVideos)
				protected.DELETE("/videos/:id", deleteAdminVideo)
			}
		}
	}

	// Verification page route
	r.GET("/verify", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title":               "Email Verification",
			"siteName":            siteName,
			"enableGoogleLogin":   enableGoogleLogin,
			"enableFacebookLogin": enableFacebookLogin,
			"enableAppleLogin":    enableAppleLogin,
			"showVerification":    true,
		})
	})

	// Settings page route
	r.GET("/settings", func(c *gin.Context) {
		// Check for JWT token in cookie
		tokenString, err := c.Cookie("auth_token")
		if err != nil {
			// No token found, redirect to login page with redirect parameter
			c.Redirect(http.StatusFound, "/login?redirect=/settings")
			return
		}

		// Validate JWT token
		claims, err := validateJWTToken(tokenString)
		if err != nil {
			// Invalid token, redirect to login page with redirect parameter
			c.Redirect(http.StatusFound, "/login?redirect=/settings")
			return
		}

		// Get user information from database
		var userID, username, email string
		err = db.QueryRow("SELECT id, username, email FROM users WHERE username = ?", claims.Username).Scan(&userID, &username, &email)
		if err != nil {
			// User not found in database, redirect to login page
			c.Redirect(http.StatusFound, "/login?redirect=/settings")
			return
		}

		// User is authenticated, show settings page
		c.HTML(http.StatusOK, "settings.html", gin.H{
			"title":           "Settings - Live Streaming Platform",
			"siteName":        siteName,
			"userID":          userID,
			"username":        username,
			"email":           email,
			"isAuthenticated": true,
		})
	})

	// User profile page route
	r.GET("/user/:username", userProfilePage)

	// Search page route
	r.GET("/search", searchPage)

	// Test S3 bucket permissions for IVS
	testS3BucketPermissions()

	// Clean up failed recording configurations
	cleanupFailedRecordingConfigurations()

	// Check recording configuration status on startup
	checkRecordingConfigurationStatus()

	// Start server on configured port
	port := os.Getenv("PORT")
	if port == "" {
		port = "3000"
	}
	r.Run(":" + port)
}

// Social API handlers

// Follow a user
func followUser(c *gin.Context) {
	// Get current user from JWT
	tokenString, err := c.Cookie("auth_token")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	claims, err := validateJWTToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	// Get current user ID from database
	var currentUserID string
	err = db.QueryRow("SELECT id FROM users WHERE username = ?", claims.Username).Scan(&currentUserID)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	// Get target user ID from request
	var req struct {
		UserID   string `json:"user_id"`
		Username string `json:"username"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// Get target user ID if username provided
	var targetUserID string
	if req.Username != "" {
		err = db.QueryRow("SELECT id FROM users WHERE username = ?", req.Username).Scan(&targetUserID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
			return
		}
	} else {
		targetUserID = req.UserID
	}

	// Check if user is trying to follow themselves
	if currentUserID == targetUserID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot follow yourself"})
		return
	}

	// Insert follow relationship (ON DUPLICATE KEY UPDATE handles already following)
	_, err = db.Exec(`
		INSERT INTO user_follows (follower_id, following_id, created_at)
		VALUES (?, ?, NOW())
		ON DUPLICATE KEY UPDATE created_at = created_at
	`, currentUserID, targetUserID)

	if err != nil {
		log.Printf("Error following user: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to follow user"})
		return
	}

	// Get updated follower count
	var followerCount int
	err = db.QueryRow("SELECT COUNT(*) FROM user_follows WHERE following_id = ?", targetUserID).Scan(&followerCount)
	if err != nil {
		followerCount = 0
	}

	c.JSON(http.StatusOK, gin.H{
		"success":        true,
		"message":        "User followed successfully",
		"follower_count": followerCount,
		"is_following":   true,
	})
}

// Unfollow a user
func unfollowUser(c *gin.Context) {
	// Get current user from JWT
	tokenString, err := c.Cookie("auth_token")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	claims, err := validateJWTToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	// Get current user ID from database
	var currentUserID string
	err = db.QueryRow("SELECT id FROM users WHERE username = ?", claims.Username).Scan(&currentUserID)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	// Get target user ID from request
	var req struct {
		UserID   string `json:"user_id"`
		Username string `json:"username"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// Get target user ID if username provided
	var targetUserID string
	if req.Username != "" {
		err = db.QueryRow("SELECT id FROM users WHERE username = ?", req.Username).Scan(&targetUserID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
			return
		}
	} else {
		targetUserID = req.UserID
	}

	// Delete follow relationship
	result, err := db.Exec("DELETE FROM user_follows WHERE follower_id = ? AND following_id = ?", currentUserID, targetUserID)
	if err != nil {
		log.Printf("Error unfollowing user: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unfollow user"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "You are not following this user"})
		return
	}

	// Get updated follower count
	var followerCount int
	err = db.QueryRow("SELECT COUNT(*) FROM user_follows WHERE following_id = ?", targetUserID).Scan(&followerCount)
	if err != nil {
		followerCount = 0
	}

	c.JSON(http.StatusOK, gin.H{
		"success":        true,
		"message":        "User unfollowed successfully",
		"follower_count": followerCount,
		"is_following":   false,
	})
}

// Get following list for a user
func getFollowing(c *gin.Context) {
	userID := c.Param("userId")

	rows, err := db.Query(`
		SELECT u.id, u.username, u.display_name, u.avatar_url, uf.created_at
		FROM user_follows uf
		JOIN users u ON uf.following_id = u.id
		WHERE uf.follower_id = ?
		ORDER BY uf.created_at DESC
	`, userID)

	if err != nil {
		log.Printf("Error getting following list: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get following list"})
		return
	}
	defer rows.Close()

	var following []gin.H
	for rows.Next() {
		var id, username, displayName, avatarURL string
		var createdAt time.Time

		err := rows.Scan(&id, &username, &displayName, &avatarURL, &createdAt)
		if err != nil {
			continue
		}

		following = append(following, gin.H{
			"id":           id,
			"username":     username,
			"display_name": displayName,
			"avatar_url":   avatarURL,
			"followed_at":  createdAt,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"following": following,
		"count":     len(following),
	})
}

// Get followers list for a user
func getFollowers(c *gin.Context) {
	userID := c.Param("userId")

	rows, err := db.Query(`
		SELECT u.id, u.username, u.display_name, u.avatar_url, uf.created_at
		FROM user_follows uf
		JOIN users u ON uf.follower_id = u.id
		WHERE uf.following_id = ?
		ORDER BY uf.created_at DESC
	`, userID)

	if err != nil {
		log.Printf("Error getting followers list: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get followers list"})
		return
	}
	defer rows.Close()

	var followers []gin.H
	for rows.Next() {
		var id, username, displayName, avatarURL string
		var createdAt time.Time

		err := rows.Scan(&id, &username, &displayName, &avatarURL, &createdAt)
		if err != nil {
			continue
		}

		followers = append(followers, gin.H{
			"id":           id,
			"username":     username,
			"display_name": displayName,
			"avatar_url":   avatarURL,
			"followed_at":  createdAt,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"followers": followers,
		"count":     len(followers),
	})
}

// Like a video
func likeVideo(c *gin.Context) {
	// Get current user from JWT
	tokenString, err := c.Cookie("auth_token")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	claims, err := validateJWTToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	// Get current user ID from database
	var currentUserID string
	err = db.QueryRow("SELECT id FROM users WHERE username = ?", claims.Username).Scan(&currentUserID)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	// Get video ID from request
	var req struct {
		VideoID string `json:"video_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// Check if video exists (check both videos and vods tables)
	var videoExists bool
	err = db.QueryRow(`
		SELECT EXISTS(
			SELECT 1 FROM videos WHERE video_id = ?
			UNION
			SELECT 1 FROM vods WHERE vod_id = ?
		)
	`, req.VideoID, req.VideoID).Scan(&videoExists)

	log.Printf("🔍 Like Video - VideoID: %s, VideoExists: %t, Error: %v", req.VideoID, videoExists, err)

	if err != nil || !videoExists {
		log.Printf("❌ Video not found - VideoID: %s, Error: %v", req.VideoID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Video not found"})
		return
	}

	// Insert like (ON DUPLICATE KEY UPDATE handles already liked)
	_, err = db.Exec(`
		INSERT INTO video_likes (video_id, user_id, created_at)
		VALUES (?, ?, NOW())
		ON DUPLICATE KEY UPDATE created_at = created_at
	`, req.VideoID, currentUserID)

	if err != nil {
		log.Printf("Error liking video: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to like video"})
		return
	}

	// Get updated like count
	var likeCount int
	err = db.QueryRow("SELECT COUNT(*) FROM video_likes WHERE video_id = ?", req.VideoID).Scan(&likeCount)
	if err != nil {
		likeCount = 0
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"message":    "Video liked successfully",
		"like_count": likeCount,
		"is_liked":   true,
	})
}

// Unlike a video
func unlikeVideo(c *gin.Context) {
	// Get current user from JWT
	tokenString, err := c.Cookie("auth_token")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	claims, err := validateJWTToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	// Get current user ID from database
	var currentUserID string
	err = db.QueryRow("SELECT id FROM users WHERE username = ?", claims.Username).Scan(&currentUserID)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	// Get video ID from request
	var req struct {
		VideoID string `json:"video_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// Check if video exists (check both videos and vods tables)
	var videoExists bool
	err = db.QueryRow(`
		SELECT EXISTS(
			SELECT 1 FROM videos WHERE video_id = ?
			UNION
			SELECT 1 FROM vods WHERE vod_id = ?
		)
	`, req.VideoID, req.VideoID).Scan(&videoExists)
	if err != nil || !videoExists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Video not found"})
		return
	}

	// Delete like
	result, err := db.Exec("DELETE FROM video_likes WHERE video_id = ? AND user_id = ?", req.VideoID, currentUserID)
	if err != nil {
		log.Printf("Error unliking video: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unlike video"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "You haven't liked this video"})
		return
	}

	// Get updated like count
	var likeCount int
	err = db.QueryRow("SELECT COUNT(*) FROM video_likes WHERE video_id = ?", req.VideoID).Scan(&likeCount)
	if err != nil {
		likeCount = 0
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"message":    "Video unliked successfully",
		"like_count": likeCount,
		"is_liked":   false,
	})
}

// Get video likes
func getVideoLikes(c *gin.Context) {
	videoID := c.Param("videoId")

	// Get like count
	var likeCount int
	err := db.QueryRow("SELECT COUNT(*) FROM video_likes WHERE video_id = ?", videoID).Scan(&likeCount)
	if err != nil {
		likeCount = 0
	}

	// Check if current user has liked this video (if authenticated)
	var isLiked bool = false
	tokenString, err := c.Cookie("auth_token")
	if err == nil {
		claims, err := validateJWTToken(tokenString)
		if err == nil {
			var currentUserID string
			err = db.QueryRow("SELECT id FROM users WHERE username = ?", claims.Username).Scan(&currentUserID)
			if err == nil {
				var likeExists int
				err = db.QueryRow("SELECT COUNT(*) FROM video_likes WHERE video_id = ? AND user_id = ?", videoID, currentUserID).Scan(&likeExists)
				if err == nil && likeExists > 0 {
					isLiked = true
				}
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"video_id":   videoID,
		"like_count": likeCount,
		"is_liked":   isLiked,
	})
}

// Creator API handlers
func getCreatorStats(c *gin.Context) {
	// Check for JWT token in cookie
	tokenString, err := c.Cookie("auth_token")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	// Validate JWT token
	claims, err := validateJWTToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	// Get user information from database
	var userID, username, email string
	err = db.QueryRow("SELECT id, username, email FROM users WHERE username = ?", claims.Username).Scan(&userID, &username, &email)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	// Mock stats for now - in production, get from database
	stats := gin.H{
		"totalViews":      12500,
		"totalFollowers":  850,
		"liveViewers":     0,
		"totalStreamTime": 1440, // in minutes
	}
	c.JSON(http.StatusOK, stats)
}

// Get live streams
func getLiveStreams(c *gin.Context) {
	rows, err := db.Query(`
		SELECT id, title, description, streamer_name, category_name, viewers,
		       thumbnail_url, playback_url, started_at
		FROM streams
		WHERE is_live = TRUE
		ORDER BY viewers DESC, started_at DESC
	`)
	if err != nil {
		log.Printf("Error fetching live streams: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch streams"})
		return
	}
	defer rows.Close()

	var streams []gin.H
	for rows.Next() {
		var stream struct {
			ID           string    `json:"id"`
			Title        string    `json:"title"`
			Description  *string   `json:"description"`
			StreamerName string    `json:"streamer_name"`
			CategoryName string    `json:"category_name"`
			Viewers      int       `json:"viewers"`
			ThumbnailURL *string   `json:"thumbnail_url"`
			PlaybackURL  string    `json:"playback_url"`
			StartedAt    time.Time `json:"started_at"`
		}

		err := rows.Scan(&stream.ID, &stream.Title, &stream.Description, &stream.StreamerName,
			&stream.CategoryName, &stream.Viewers, &stream.ThumbnailURL, &stream.PlaybackURL, &stream.StartedAt)
		if err != nil {
			log.Printf("Error scanning stream row: %v", err)
			continue
		}

		streams = append(streams, gin.H{
			"id":            stream.ID,
			"title":         stream.Title,
			"description":   stream.Description,
			"streamer_name": stream.StreamerName,
			"category_name": stream.CategoryName,
			"viewers":       stream.Viewers,
			"thumbnail_url": stream.ThumbnailURL,
			"playback_url":  stream.PlaybackURL,
			"started_at":    stream.StartedAt,
		})
	}

	c.JSON(http.StatusOK, streams)
}

// Get current user's streams (both live and ended)
func getMyStreams(c *gin.Context) {
	// Get JWT token from cookie
	tokenString, err := c.Cookie("auth_token")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	// Validate JWT token
	claims, err := validateJWTToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	userID := claims.UserID

	rows, err := db.Query(`
		SELECT id, title, description, streamer_name, category_name, viewers,
		       thumbnail_url, playback_url, is_live, started_at, ended_at
		FROM streams
		WHERE streamer_id = ?
		ORDER BY started_at DESC
		LIMIT 20
	`, userID)
	if err != nil {
		log.Printf("Error fetching user streams: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch streams"})
		return
	}
	defer rows.Close()

	var streams []gin.H
	for rows.Next() {
		var stream struct {
			ID           string         `db:"id"`
			Title        string         `db:"title"`
			Description  string         `db:"description"`
			StreamerName string         `db:"streamer_name"`
			CategoryName string         `db:"category_name"`
			Viewers      int            `db:"viewers"`
			ThumbnailURL sql.NullString `db:"thumbnail_url"`
			PlaybackURL  string         `db:"playback_url"`
			IsLive       bool           `db:"is_live"`
			StartedAt    time.Time      `db:"started_at"`
			EndedAt      sql.NullTime   `db:"ended_at"`
		}

		err := rows.Scan(&stream.ID, &stream.Title, &stream.Description, &stream.StreamerName,
			&stream.CategoryName, &stream.Viewers, &stream.ThumbnailURL, &stream.PlaybackURL,
			&stream.IsLive, &stream.StartedAt, &stream.EndedAt)
		if err != nil {
			log.Printf("Error scanning stream row: %v", err)
			continue
		}

		thumbnailURL := ""
		if stream.ThumbnailURL.Valid {
			thumbnailURL = stream.ThumbnailURL.String
		}

		var endedAt *time.Time
		if stream.EndedAt.Valid {
			endedAt = &stream.EndedAt.Time
		}

		streams = append(streams, gin.H{
			"id":            stream.ID,
			"title":         stream.Title,
			"description":   stream.Description,
			"streamer_name": stream.StreamerName,
			"category_name": stream.CategoryName,
			"viewers":       stream.Viewers,
			"thumbnail_url": thumbnailURL,
			"playback_url":  stream.PlaybackURL,
			"is_live":       stream.IsLive,
			"started_at":    stream.StartedAt,
			"ended_at":      endedAt,
		})
	}

	c.JSON(http.StatusOK, streams)
}

// Stream API handlers
func startStream(c *gin.Context) {
	type StartStreamRequest struct {
		Title       string `json:"title" binding:"required"`
		Category    string `json:"category" binding:"required"`
		Description string `json:"description"`
		Method      string `json:"method" binding:"required"` // "webcam" or "rtmp"
		StreamKey   string `json:"stream_key"`
	}

	var req StartStreamRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get current user info
	currentUser := getCurrentUser(c)
	var userID, username string

	if currentUser != nil {
		userID = currentUser.ID
		username = currentUser.Username
	} else {
		// Fallback for anonymous users
		userID = "anonymous_" + fmt.Sprintf("%d", time.Now().Unix())
		username = "Anonymous Streamer"
	}

	// Generate stream ID
	streamID := fmt.Sprintf("stream_%d", time.Now().Unix())

	// Get or create Amazon IVS channel for this user
	playbackUrl, ingestEndpoint, streamKey, channelArn, err := getOrCreateUserChannel(userID, username, streamID, req.Title)
	if err != nil {
		log.Printf("Error getting/creating IVS channel: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create streaming channel"})
		return
	}

	// Get category ID and name from category name
	var categoryID int
	var categoryName string
	log.Printf("Looking up category: '%s'", req.Category)

	err = db.QueryRow("SELECT id, name FROM categories WHERE name = ?", req.Category).Scan(&categoryID, &categoryName)
	if err != nil {
		log.Printf("Error finding category '%s': %v", req.Category, err)
		// List all available categories for debugging
		rows, listErr := db.Query("SELECT id, name FROM categories")
		if listErr == nil {
			log.Printf("Available categories:")
			for rows.Next() {
				var id int
				var name string
				if rows.Scan(&id, &name) == nil {
					log.Printf("  - ID: %d, Name: '%s'", id, name)
				}
			}
			rows.Close()
		}

		// Default to first category if not found
		err = db.QueryRow("SELECT id, name FROM categories ORDER BY id LIMIT 1").Scan(&categoryID, &categoryName)
		if err != nil {
			log.Printf("Error getting default category: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "No valid categories found"})
			return
		}
		log.Printf("Using default category: ID=%d, Name='%s'", categoryID, categoryName)
	} else {
		log.Printf("Found category: ID=%d, Name='%s'", categoryID, categoryName)
	}

	// Check if user already has a stream with the same stream key (regardless of live status)
	var existingStreamID string
	var existingIsLive bool
	log.Printf("🔍 Checking for existing stream with stream_key: %s", streamKey)

	err = db.QueryRow(`
		SELECT id, is_live FROM streams
		WHERE stream_key = ?
		LIMIT 1
	`, streamKey).Scan(&existingStreamID, &existingIsLive)

	log.Printf("🔍 Query result: err=%v, existingStreamID=%s, existingIsLive=%v", err, existingStreamID, existingIsLive)

	if err == nil {
		// User already has a stream with this stream key, reuse it
		log.Printf("✅ Reusing existing stream: %s for user %s with stream_key %s (was live: %v)", existingStreamID, username, streamKey, existingIsLive)

		// Update the existing stream with new title/description and set it to live
		_, updateErr := db.Exec(`
			UPDATE streams
			SET title = ?, description = ?, category_id = ?, category_name = ?, started_at = NOW(), is_live = TRUE
			WHERE id = ?
		`, req.Title, req.Description, categoryID, categoryName, existingStreamID)

		if updateErr != nil {
			log.Printf("Warning: Could not update existing stream: %v", updateErr)
		}

		streamID = existingStreamID
	} else {
		// No existing stream found, create new stream entry
		log.Printf("🆕 Creating new stream entry with stream_key: %s", streamKey)
		_, dbErr := db.Exec(`
			INSERT INTO streams (id, title, description, streamer_id, streamer_name, category_id, category_name,
			                    stream_key, playback_url, ingest_endpoint, ivs_channel_arn, started_at, is_live)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), TRUE)`,
			streamID, req.Title, req.Description, userID, username, categoryID, categoryName,
			streamKey, playbackUrl, ingestEndpoint, channelArn)

		if dbErr != nil {
			log.Printf("Error saving stream to database: %v", dbErr)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create stream"})
			return
		}
		log.Printf("✅ New stream created successfully: %s", streamID)
	}

	log.Printf("Stream created: %s by %s", streamID, username)


	// Return stream data
	stream := gin.H{
		"id":              streamID,
		"title":           req.Title,
		"description":     req.Description,
		"category":        req.Category,
		"category_name":   categoryName,
		"method":          req.Method,
		"stream_key":      streamKey,
		"status":          "live",
		"viewers":         0,
		"playback_url":    playbackUrl,
		"ingest_endpoint": ingestEndpoint,
		"streamer_name":   username,
		"created_at":      time.Now(),
	}

	c.JSON(http.StatusOK, stream)
}

func stopStream(c *gin.Context) {
	streamID := c.Param("id")

	// Get JWT token from cookie
	tokenString, err := c.Cookie("auth_token")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	// Validate JWT token
	claims, err := validateJWTToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	userID := claims.UserID
	username := claims.Username

	// Get stream information before stopping
	var stream struct {
		ID           string    `db:"id"`
		Title        string    `db:"title"`
		Description  string    `db:"description"`
		StreamerID   string    `db:"streamer_id"`
		StreamerName string    `db:"streamer_name"`
		CategoryID   int       `db:"category_id"`
		CategoryName string    `db:"category_name"`
		StartedAt    time.Time `db:"started_at"`
		ChannelArn   string    `db:"ivs_channel_arn"`
	}

	err = db.QueryRow(`
		SELECT id, title, description, streamer_id, streamer_name, category_id, category_name, started_at, ivs_channel_arn
		FROM streams
		WHERE id = ? AND streamer_id = ? AND is_live = TRUE
	`, streamID, userID).Scan(
		&stream.ID, &stream.Title, &stream.Description, &stream.StreamerID, &stream.StreamerName,
		&stream.CategoryID, &stream.CategoryName, &stream.StartedAt, &stream.ChannelArn)

	if err != nil {
		log.Printf("Error fetching stream %s: %v", streamID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Stream not found or not authorized"})
		return
	}

	// Stop the IVS stream if channel ARN exists
	if stream.ChannelArn != "" && ivsClient != nil {
		_, err = ivsClient.StopStream(&ivs.StopStreamInput{
			ChannelArn: aws.String(stream.ChannelArn),
		})
		if err != nil {
			log.Printf("Error stopping IVS stream: %v", err)
			// Continue with database update even if IVS stop fails
		}
	}

	// Update stream status in database
	_, err = db.Exec(`
		UPDATE streams
		SET is_live = FALSE, ended_at = NOW()
		WHERE id = ? AND streamer_id = ?
	`, streamID, userID)

	if err != nil {
		log.Printf("Error updating stream status: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to stop stream"})
		return
	}

	// Create VOD record for the stopped stream
	go createVODFromStream(stream, time.Now())

	log.Printf("Stream stopped: %s by %s", streamID, username)

	c.JSON(http.StatusOK, gin.H{
		"message":   "Stream stopped successfully",
		"stream_id": streamID,
	})
}

func getStreamStatus(c *gin.Context) {
	streamID := c.Param("id")

	// Mock stream status - in production, get from database
	status := gin.H{
		"id":      streamID,
		"status":  "live",
		"viewers": 125,
		"uptime":  "00:15:30",
	}

	c.JSON(http.StatusOK, status)
}

// Create VOD from stream recording
func createVODFromStream(stream struct {
	ID           string    `db:"id"`
	Title        string    `db:"title"`
	Description  string    `db:"description"`
	StreamerID   string    `db:"streamer_id"`
	StreamerName string    `db:"streamer_name"`
	CategoryID   int       `db:"category_id"`
	CategoryName string    `db:"category_name"`
	StartedAt    time.Time `db:"started_at"`
	ChannelArn   string    `db:"ivs_channel_arn"`
}, endedAt time.Time) {

	// Generate unique VOD ID
	vodID := fmt.Sprintf("vod_%s_%d", stream.ID, time.Now().Unix())

	// Calculate stream duration
	duration := int(endedAt.Sub(stream.StartedAt).Seconds())

	// Generate S3 recording key
	s3RecordingKey := fmt.Sprintf("recordings/%s/%s.mp4", stream.StreamerID, vodID)

	log.Printf("Creating VOD for stream %s, duration: %d seconds", stream.ID, duration)

	// Insert VOD record into database
	_, err := db.Exec(`
		INSERT INTO vods (vod_id, stream_id, title, description, streamer_id, streamer_name,
		                 category_id, category_name, duration, s3_recording_key, recording_status,
		                 stream_started_at, stream_ended_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'processing', ?, ?)
	`, vodID, stream.ID, stream.Title, stream.Description, stream.StreamerID, stream.StreamerName,
		stream.CategoryID, stream.CategoryName, duration, s3RecordingKey, stream.StartedAt, endedAt)

	if err != nil {
		log.Printf("Error creating VOD record: %v", err)
		return
	}

	// Start background processing to get the recording from IVS and upload to S3
	go processStreamRecording(vodID, stream.ChannelArn, s3RecordingKey)

	log.Printf("VOD created successfully: %s", vodID)
}

// Process stream recording and upload to S3
func processStreamRecording(vodID, channelArn, s3RecordingKey string) {
	log.Printf("🎬 Processing stream recording for VOD: %s", vodID)

	// Real AWS IVS recording implementation
	log.Printf("🎥 Starting real IVS recording processing for channel: %s", channelArn)

	// Step 1: Wait for IVS recording to be available (IVS takes time to process)
	log.Printf("⏳ Waiting for IVS recording to be processed...")
	time.Sleep(30 * time.Second) // Give IVS time to process the recording

	// Step 2: Try to get the actual IVS recording (HLS playlist)
	playlistKey, err := downloadRealIVSRecording(channelArn, vodID)
	if err != nil {
		log.Printf("❌ Error processing IVS recording: %v", err)
		// Mark as failed
		db.Exec(`UPDATE vods SET recording_status = 'failed', updated_at = NOW() WHERE vod_id = ?`, vodID)
		return
	}

	if playlistKey == "" {
		log.Printf("⚠️ IVS recording not found yet.")
		log.Printf("   1. Ensure recording is enabled on the channel before going live")
		log.Printf("   2. Stream at least 60–90 seconds; IVS needs time to finalize VOD")
		log.Printf("   3. IVS processing can take 5–10 minutes; will keep VOD in processing state")

		// Do NOT upload any placeholder/sample file. Leave the VOD as processing.
		// A background poller (or manual retry) should call processStreamRecording again later.
		return
	}

	// Use the HLS playlist as the video URL
	videoURL := fmt.Sprintf("https://%s.s3.us-west-2.amazonaws.com/%s", s3Bucket, playlistKey)

	// Generate thumbnail from HLS stream
	thumbnailURL := generateThumbnailFromHLS(vodID, playlistKey)
	if thumbnailURL == "" {
		// Fallback to default thumbnail
		thumbnailURL = fmt.Sprintf("https://%s.s3.us-west-2.amazonaws.com/thumbnails/default-vod-thumbnail.jpg", s3Bucket)
	}

	log.Printf("✅ Real IVS recording (HLS) processed successfully!")
	log.Printf("🎥 HLS Playlist URL: %s", videoURL)
	log.Printf("🖼️ Thumbnail URL: %s", thumbnailURL)

	// Update VOD record with processed URLs
	_, updateErr := db.Exec(`
		UPDATE vods
		SET video_url = ?, thumbnail_url = ?, recording_status = 'completed', updated_at = NOW()
		WHERE vod_id = ?
	`, videoURL, thumbnailURL, vodID)

	if updateErr != nil {
		log.Printf("❌ Error updating VOD record: %v", updateErr)
		// Mark as failed
		db.Exec(`UPDATE vods SET recording_status = 'failed', updated_at = NOW() WHERE vod_id = ?`, vodID)
		return
	}

	log.Printf("🎉 VOD processing completed with real recording: %s", vodID)
}

// Download real IVS recording from S3 (handles HLS format), returns playlist key when found
func downloadRealIVSRecording(channelArn, vodID string) (string, error) {
	log.Printf("🔍 Looking for real IVS recording for channel: %s", channelArn)

	// Extract channel ID from ARN
	channelID := extractChannelNameFromArn(channelArn)

	// IVS records in HLS format (.m3u8 playlists and .ts segments)
	// Try multiple common IVS recording paths, including user-specific paths
	possiblePaths := []string{
		fmt.Sprintf("ivs/%s/", channelID),
		"ivs/", // IVS default folder structure (e.g., ivs/v1/<acct>/channel/<channelId>/...)
		fmt.Sprintf("recordings/%s/", channelID),
		fmt.Sprintf("live-recordings/%s/", channelID),
		fmt.Sprintf("%s/", channelID),
		"recordings/danielcheung98/", // User-specific recordings path
		"recordings/", // General recordings folder
		"", // Sometimes recordings are at root level
	}

	// Look for recent recordings (within the last 24 hours to find existing recordings)
	cutoffTime := time.Now().Add(-24 * time.Hour)

	// List objects in S3 bucket to find IVS recordings
	for _, pathPrefix := range possiblePaths {
		log.Printf("🔍 Checking S3 path: %s", pathPrefix)

		listInput := &s3.ListObjectsV2Input{
			Bucket: aws.String(s3Bucket),
			Prefix: aws.String(pathPrefix),
		}

		result, err := s3Client.ListObjectsV2(listInput)
		if err != nil {
			log.Printf("❌ Error listing S3 objects for path %s: %v", pathPrefix, err)
			continue
		}

		// Log all files found for debugging
		log.Printf("📁 Found %d objects in path %s:", len(result.Contents), pathPrefix)
		for _, obj := range result.Contents {
			log.Printf("   📄 %s (modified: %s, size: %d bytes)", *obj.Key, obj.LastModified.Format("2006-01-02 15:04:05"), *obj.Size)
		}

		// Look for HLS playlist files (.m3u8), segments (.ts), and existing MP4 files
		var playlistKey string
		var tsSegments []string

		for _, obj := range result.Contents {
			if obj.LastModified.After(cutoffTime) {
				if strings.HasSuffix(*obj.Key, ".m3u8") && strings.Contains(*obj.Key, "/"+channelID+"/") {
					playlistKey = *obj.Key
					log.Printf("✅ Found HLS playlist: %s", playlistKey)
				} else if strings.HasSuffix(*obj.Key, ".ts") {
					tsSegments = append(tsSegments, *obj.Key)
				}
			}
		}

		// If we found HLS files, return the playlist key
		if playlistKey != "" && len(tsSegments) > 0 {
			log.Printf("✅ Found IVS HLS recording with %d segments", len(tsSegments))
			return playlistKey, nil
		}
	}

	log.Printf("❌ No recent IVS recordings found for channel: %s", channelID)
	log.Printf("💡 This might be because:")
	log.Printf("   1. Recording configuration is not working (check S3 bucket policy)")
	log.Printf("   2. Stream was too short (IVS needs minimum ~30 seconds)")
	log.Printf("   3. IVS is still processing the recording (can take 5-10 minutes)")
	return "", nil
}


// Copy S3 object from one key to another
func copyS3Object(sourceKey, destKey string) error {
	copySource := fmt.Sprintf("%s/%s", s3Bucket, sourceKey)

	_, err := s3Client.CopyObject(&s3.CopyObjectInput{
		Bucket:     aws.String(s3Bucket),
		CopySource: aws.String(copySource),
		Key:        aws.String(destKey),
	})

	return err
}

// Upload sample video to S3 for testing purposes
func uploadSampleVideoToS3(s3RecordingKey, vodID string) {
	log.Printf("🎬 Creating sample video for VOD: %s", vodID)

	// Instead of downloading a large video, use an existing video from your S3 bucket
	// This is much faster and more reliable for testing
	existingVideoKey := "sample-videos/BigBuckBunny.mp4"

	log.Printf("📋 Copying existing sample video from S3: %s", existingVideoKey)

	// Try to copy existing sample video, if it doesn't exist, create a placeholder
	copySource := fmt.Sprintf("%s/%s", s3Bucket, existingVideoKey)
	_, err := s3Client.CopyObject(&s3.CopyObjectInput{
		Bucket:     aws.String(s3Bucket),
		CopySource: aws.String(copySource),
		Key:        aws.String(s3RecordingKey),
	})

	if err != nil {
		log.Printf("⚠️ Existing sample video not found, creating placeholder: %v", err)

		// Create a simple placeholder file
		placeholderContent := fmt.Sprintf("Sample video placeholder for VOD: %s\nThis would be your actual streaming recording.", vodID)

		_, err = s3Client.PutObject(&s3.PutObjectInput{
			Bucket:      aws.String(s3Bucket),
			Key:         aws.String(s3RecordingKey),
			Body:        strings.NewReader(placeholderContent),
			ContentType: aws.String("video/mp4"),
		})

		if err != nil {
			log.Printf("❌ Error creating placeholder video: %v", err)
			// Mark VOD as failed
			db.Exec(`UPDATE vods SET recording_status = 'failed', updated_at = NOW() WHERE vod_id = ?`, vodID)
			return
		}

		log.Printf("✅ Placeholder video created successfully")
	} else {
		log.Printf("✅ Sample video copied successfully from existing file")
	}

	// Update VOD record with video URL
	videoURL := fmt.Sprintf("https://%s.s3.us-west-2.amazonaws.com/%s", s3Bucket, s3RecordingKey)
	thumbnailURL := fmt.Sprintf("https://%s.s3.us-west-2.amazonaws.com/thumbnails/%s.jpg", s3Bucket, vodID)

	log.Printf("🎥 Updating VOD database with video URL: %s", videoURL)

	_, err = db.Exec(`
		UPDATE vods
		SET video_url = ?, thumbnail_url = ?, recording_status = 'completed', updated_at = NOW()
		WHERE vod_id = ?
	`, videoURL, thumbnailURL, vodID)

	if err != nil {
		log.Printf("❌ Error updating VOD record: %v", err)
		// Mark as failed
		db.Exec(`UPDATE vods SET recording_status = 'failed', updated_at = NOW() WHERE vod_id = ?`, vodID)
		return
	}

	log.Printf("🎉 VOD processing completed successfully: %s", vodID)
}

// Get IVS recording configuration for a channel
func getIVSRecordingConfiguration(channelArn string) (string, error) {
	log.Printf("🔍 Getting IVS recording configuration for channel: %s", channelArn)

	// Extract channel name from ARN
	channelName := extractChannelNameFromArn(channelArn)

	// In AWS IVS, recordings are automatically saved to S3 if recording is enabled
	// The recording configuration determines where recordings are stored
	recordingConfigArn := fmt.Sprintf("arn:aws:ivs:us-west-2:%s:recording-configuration/%s-recording", "123456789012", channelName)

	log.Printf("📹 Using recording configuration: %s", recordingConfigArn)
	return recordingConfigArn, nil
}

// Wait for IVS recording to be available
func waitForIVSRecording(channelArn, recordingConfig string) (string, error) {
	log.Printf("⏳ Waiting for IVS recording to be available...")

	// Extract channel name for recording path
	channelName := extractChannelNameFromArn(channelArn)

	// IVS recordings are typically stored in S3 with this pattern:
	// s3://bucket/ivs-recordings/channel-name/year/month/day/hour/recording.mp4
	now := time.Now()
	recordingPath := fmt.Sprintf("ivs-recordings/%s/%04d/%02d/%02d/%02d/recording.mp4",
		channelName, now.Year(), now.Month(), now.Day(), now.Hour())

	// In production, you would poll IVS API to check if recording is ready
	// For now, simulate waiting time
	time.Sleep(10 * time.Second)

	recordingURL := fmt.Sprintf("https://%s.s3.us-west-2.amazonaws.com/%s", s3Bucket, recordingPath)
	log.Printf("📹 IVS recording should be available at: %s", recordingURL)

	return recordingURL, nil
}

// Download recording from IVS S3 location and upload to our S3 bucket
func downloadAndUploadRecording(recordingURL, s3RecordingKey string) error {
	log.Printf("📥 Downloading recording from IVS: %s", recordingURL)

	// Try to download the actual IVS recording
	resp, err := http.Get(recordingURL)
	if err != nil {
		log.Printf("❌ Error downloading IVS recording: %v", err)
		log.Printf("🔄 Falling back to sample video for testing...")
		return uploadSampleVideoDirectly(s3RecordingKey)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Printf("❌ IVS recording not found (HTTP %d), falling back to sample video", resp.StatusCode)
		return uploadSampleVideoDirectly(s3RecordingKey)
	}

	// Read the recording data
	recordingData, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("❌ Error reading recording data: %v", err)
		return uploadSampleVideoDirectly(s3RecordingKey)
	}

	// Upload to our S3 bucket
	_, err = s3Client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(s3Bucket),
		Key:         aws.String(s3RecordingKey),
		Body:        strings.NewReader(string(recordingData)),
		ContentType: aws.String("video/mp4"),
	})

	if err != nil {
		log.Printf("❌ Error uploading recording to S3: %v", err)
		return err
	}

	log.Printf("✅ Real IVS recording uploaded successfully to S3: %s", s3RecordingKey)
	return nil
}

// Upload sample video directly (fallback)
func uploadSampleVideoDirectly(s3RecordingKey string) error {
	log.Printf("🎬 Uploading sample video as fallback...")

	sampleVideoURL := "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
	resp, err := http.Get(sampleVideoURL)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	videoData, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	_, err = s3Client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(s3Bucket),
		Key:         aws.String(s3RecordingKey),
		Body:        strings.NewReader(string(videoData)),
		ContentType: aws.String("video/mp4"),
	})

	return err
}

// Extract channel name from ARN
func extractChannelNameFromArn(channelArn string) string {
	// ARN format: arn:aws:ivs:region:account:channel/channel-id
	parts := strings.Split(channelArn, "/")
	if len(parts) > 0 {
		return parts[len(parts)-1]
	}
	return "unknown-channel"
}

// Create IVS recording configuration
func createRecordingConfiguration(streamID string) (string, error) {
	log.Printf("📹 Creating IVS recording configuration for stream: %s", streamID)

	// Use a simpler, more reliable recording configuration name
	recordingConfigName := fmt.Sprintf("moneybags-recording-%d", time.Now().Unix())

	// S3 destination for recordings with proper configuration
	s3DestinationConfig := &ivs.S3DestinationConfiguration{
		BucketName: aws.String(s3Bucket),
	}

	destinationConfig := &ivs.DestinationConfiguration{
		S3: s3DestinationConfig,
	}

	// Create recording configuration with minimal required fields
	createRecordingInput := &ivs.CreateRecordingConfigurationInput{
		Name:                     &recordingConfigName,
		DestinationConfiguration: destinationConfig,
		// Remove tags for now to avoid potential issues
	}

	log.Printf("🔧 Attempting to create recording configuration with bucket: %s", s3Bucket)
	result, err := ivsClient.CreateRecordingConfiguration(createRecordingInput)
	if err != nil {
		log.Printf("❌ Failed to create recording configuration: %v", err)

		// Check for common error types
		if strings.Contains(err.Error(), "AccessDenied") {
			log.Printf("⚠️ Access denied - IVS service may not have permission to write to S3 bucket")
			log.Printf("💡 Solution: Add IVS service permissions to S3 bucket policy")
		} else if strings.Contains(err.Error(), "InvalidParameter") {
			log.Printf("⚠️ Invalid parameter - check S3 bucket configuration")
		} else if strings.Contains(err.Error(), "already exists") {
			log.Printf("📹 Recording configuration already exists, trying to find existing one")
			return findExistingRecordingConfiguration()
		}

		return "", fmt.Errorf("failed to create recording configuration: %v", err)
	}

	recordingConfigArn := *result.RecordingConfiguration.Arn
	log.Printf("✅ Created IVS recording configuration: %s", recordingConfigArn)

	// Wait for recording configuration to be active
	log.Printf("⏳ Waiting for recording configuration to become active...")
	err = waitForRecordingConfigurationActive(recordingConfigArn)
	if err != nil {
		log.Printf("⚠️ Warning: Recording configuration may not be ready: %v", err)
		// Continue anyway, but without recording
		return "", nil
	}

	log.Printf("✅ Recording configuration is now active and ready to use")
	return recordingConfigArn, nil
}

// Find existing recording configuration
func findExistingRecordingConfiguration() (string, error) {
	log.Printf("🔍 Looking for existing recording configurations...")

	// List existing recording configurations
	listInput := &ivs.ListRecordingConfigurationsInput{
		MaxResults: aws.Int64(10), // Get up to 10 configurations
	}

	result, err := ivsClient.ListRecordingConfigurations(listInput)
	if err != nil {
		log.Printf("❌ Error listing recording configurations: %v", err)
		return "", err
	}

	// Clean up any failed recording configurations first
	for _, config := range result.RecordingConfigurations {
		if config.State != nil && *config.State == "CREATE_FAILED" {
			log.Printf("🧹 Cleaning up failed recording configuration: %s", *config.Arn)
			deleteInput := &ivs.DeleteRecordingConfigurationInput{
				Arn: config.Arn,
			}
			_, err := ivsClient.DeleteRecordingConfiguration(deleteInput)
			if err != nil {
				log.Printf("⚠️ Failed to delete failed recording configuration: %v", err)
			} else {
				log.Printf("✅ Deleted failed recording configuration")
			}
		}
	}

	// Find the first active recording configuration
	for _, config := range result.RecordingConfigurations {
		if config.State != nil && *config.State == "ACTIVE" {
			log.Printf("✅ Found existing active recording configuration: %s", *config.Arn)
			return *config.Arn, nil
		}
	}

	log.Printf("⚠️ No active recording configurations found")
	return "", fmt.Errorf("no active recording configurations available")
}

// Get or create a global recording configuration (reusable for all streams)
func getOrCreateGlobalRecordingConfiguration() (string, error) {
	log.Printf("🔍 Getting or creating global recording configuration...")

	// 1) Allow explicit override via environment variable
	if override := strings.TrimSpace(os.Getenv("IVS_RECORDING_CONFIG_ARN")); override != "" {
		log.Printf("🔧 Using IVS_RECORDING_CONFIG_ARN from env: %s", override)
		// Try to fetch status for visibility (non-fatal)
		if ivsClient != nil {
			if res, err := ivsClient.GetRecordingConfiguration(&ivs.GetRecordingConfigurationInput{Arn: aws.String(override)}); err != nil {
				log.Printf("⚠️ Could not validate override recording config: %v", err)
			} else if res != nil && res.RecordingConfiguration != nil && res.RecordingConfiguration.State != nil {
				log.Printf("📹 Override recording configuration state: %s", *res.RecordingConfiguration.State)
			}
		}
		return override, nil
	}

	// 2) Otherwise, try to find an existing active recording configuration
	existingArn, err := findExistingRecordingConfiguration()
	if err == nil && existingArn != "" {
		log.Printf("✅ Using existing recording configuration: %s", existingArn)
		return existingArn, nil
	}

	log.Printf("📹 No existing recording configuration found, creating new one...")

	// First, verify S3 bucket is accessible
	log.Printf("🔍 Verifying S3 bucket accessibility: %s", s3Bucket)
	_, err = s3Client.HeadBucket(&s3.HeadBucketInput{
		Bucket: aws.String(s3Bucket),
	})
	if err != nil {
		log.Printf("❌ S3 bucket not accessible: %v", err)
		if awsErr, ok := err.(awserr.Error); ok {
			log.Printf("❌ S3 Error Code: %s", awsErr.Code())
			log.Printf("❌ S3 Error Message: %s", awsErr.Message())
		}
		return "", fmt.Errorf("S3 bucket not accessible: %v", err)
	}
	log.Printf("✅ S3 bucket is accessible")

	// Get bucket location to verify region
	locationResult, err := s3Client.GetBucketLocation(&s3.GetBucketLocationInput{
		Bucket: aws.String(s3Bucket),
	})
	if err != nil {
		log.Printf("⚠️ Could not get bucket location: %v", err)
	} else {
		bucketRegion := "us-east-1" // Default region if LocationConstraint is nil
		if locationResult.LocationConstraint != nil {
			bucketRegion = *locationResult.LocationConstraint
		}
		log.Printf("🌍 S3 bucket region: %s", bucketRegion)
		log.Printf("🌍 IVS client region: us-west-2")

		if bucketRegion != "us-west-2" {
			log.Printf("⚠️ WARNING: S3 bucket region (%s) differs from IVS region (us-west-2)", bucketRegion)
			log.Printf("💡 This might cause recording configuration to fail")
		}
	}

	// Create a new global recording configuration
	recordingConfigName := fmt.Sprintf("moneybags-global-recording-%d", time.Now().Unix())

	// S3 destination for recordings
	s3DestinationConfig := &ivs.S3DestinationConfiguration{
		BucketName: aws.String(s3Bucket),
	}

	destinationConfig := &ivs.DestinationConfiguration{
		S3: s3DestinationConfig,
	}

	// Create recording configuration
	createRecordingInput := &ivs.CreateRecordingConfigurationInput{
		Name:                     &recordingConfigName,
		DestinationConfiguration: destinationConfig,
	}

	log.Printf("🔧 Creating global recording configuration with S3 bucket: %s", s3Bucket)
	log.Printf("🔧 Recording config name: %s", recordingConfigName)
	log.Printf("🔧 S3 destination config: BucketName=%s", s3Bucket)

	result, err := ivsClient.CreateRecordingConfiguration(createRecordingInput)
	if err != nil {
		log.Printf("❌ Failed to create global recording configuration: %v", err)
		log.Printf("❌ Error type: %T", err)

		// Get more detailed error information
		if awsErr, ok := err.(awserr.Error); ok {
			log.Printf("❌ AWS Error Code: %s", awsErr.Code())
			log.Printf("❌ AWS Error Message: %s", awsErr.Message())
			if awsErr.OrigErr() != nil {
				log.Printf("❌ AWS Error Original: %s", awsErr.OrigErr())
			}
		}

		// Provide helpful error messages
		if strings.Contains(err.Error(), "AccessDenied") {
			log.Printf("💡 Fix: Add the following policy to your S3 bucket to allow IVS to write recordings:")
			log.Printf(`{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AllowIVSRecording",
      "Effect": "Allow",
      "Principal": {
        "Service": "ivs.amazonaws.com"
      },
      "Action": [
        "s3:PutObject",
        "s3:PutObjectAcl"
      ],
      "Resource": "arn:aws:s3:::%s/*"
    }
  ]
}`, s3Bucket)
		} else if strings.Contains(err.Error(), "InvalidParameter") {
			log.Printf("💡 Invalid parameter error - checking S3 bucket exists and is accessible")
		} else if strings.Contains(err.Error(), "ValidationException") {
			log.Printf("💡 Validation error - checking if bucket name and region are correct")
		}

		return "", fmt.Errorf("failed to create global recording configuration: %v", err)
	}

	recordingConfigArn := *result.RecordingConfiguration.Arn
	log.Printf("✅ Created global recording configuration: %s", recordingConfigArn)

	// Wait for it to become active
	log.Printf("⏳ Waiting for recording configuration to become active...")
	err = waitForRecordingConfigurationActive(recordingConfigArn)
	if err != nil {
		log.Printf("⚠️ Recording configuration may not be fully ready: %v", err)
		// Return the ARN anyway, it might work
		return recordingConfigArn, nil
	}

	log.Printf("🎉 Global recording configuration is active and ready!")
	return recordingConfigArn, nil
}

// Wait for recording configuration to become active
func waitForRecordingConfigurationActive(recordingConfigArn string) error {
	log.Printf("⏳ Checking recording configuration status...")

	maxAttempts := 90 // Wait up to ~180 seconds (2s per attempt)
	for i := 0; i < maxAttempts; i++ {
		// Get recording configuration status
		getInput := &ivs.GetRecordingConfigurationInput{
			Arn: aws.String(recordingConfigArn),
		}

		result, err := ivsClient.GetRecordingConfiguration(getInput)
		if err != nil {
			log.Printf("❌ Error checking recording configuration status: %v", err)
			return err
		}

		state := *result.RecordingConfiguration.State
		log.Printf("📹 Recording configuration state: %s (attempt %d/%d)", state, i+1, maxAttempts)

		if state == "ACTIVE" {
			log.Printf("✅ Recording configuration is now ACTIVE!")
			return nil
		}

		if state == "CREATE_FAILED" {
			log.Printf("❌ Recording configuration creation failed!")

			// Get detailed error information
			config := result.RecordingConfiguration
			log.Printf("❌ Recording config details:")
			log.Printf("   - Name: %s", *config.Name)
			log.Printf("   - ARN: %s", *config.Arn)
			log.Printf("   - State: %s", *config.State)
			if config.DestinationConfiguration != nil && config.DestinationConfiguration.S3 != nil {
				log.Printf("   - S3 Bucket: %s", *config.DestinationConfiguration.S3.BucketName)
			}

			log.Printf("💡 Possible causes:")
			log.Printf("   1. S3 bucket policy missing or incorrect")
			log.Printf("   2. S3 bucket doesn't exist or not accessible")
			log.Printf("   3. IVS service doesn't have permission to write to S3")
			log.Printf("   4. S3 bucket is in different region than IVS")

			return fmt.Errorf("recording configuration creation failed with state: %s", state)
		}

		// Wait 2 seconds before checking again
		time.Sleep(2 * time.Second)
	}

	return fmt.Errorf("timeout waiting for recording configuration to become active")
}

// Enable recording for a stream in the background (non-blocking)
func enableRecordingForStream(streamID, channelArn string) {
	log.Printf("🎬 Starting background recording setup for stream: %s", streamID)

	// First, check if the channel already has recording enabled
	getChannelInput := &ivs.GetChannelInput{
		Arn: aws.String(channelArn),
	}

	channelResult, err := ivsClient.GetChannel(getChannelInput)
	if err != nil {
		log.Printf("⚠️ Failed to get channel info for %s: %v", streamID, err)
		return
	}

	// Check if recording is already enabled
	if channelResult.Channel.RecordingConfigurationArn != nil {
		log.Printf("✅ Recording already enabled for stream %s: %s", streamID, *channelResult.Channel.RecordingConfigurationArn)
		return
	}

	// S3 bucket permissions are now working! Re-enable recording
	log.Printf("✅ S3 bucket permissions verified - enabling recording for stream: %s", streamID)

	// Try to get or create a global recording configuration
	recordingConfigArn, err := getOrCreateGlobalRecordingConfiguration()
	if err != nil {
		log.Printf("⚠️ Failed to get recording configuration for stream %s: %v", streamID, err)
		log.Printf("📹 Stream will continue without recording capability")
		return
	}

	if recordingConfigArn == "" {
		log.Printf("⚠️ Recording configuration not ready for stream %s, skipping recording setup", streamID)
		return
	}

	// Update the IVS channel to enable recording
	log.Printf("🔧 Updating IVS channel to enable recording...")
	updateChannelInput := &ivs.UpdateChannelInput{
		Arn:                       aws.String(channelArn),
		RecordingConfigurationArn: aws.String(recordingConfigArn),
	}

	_, err = ivsClient.UpdateChannel(updateChannelInput)
	if err != nil {
		log.Printf("⚠️ Failed to enable recording for stream %s: %v", streamID, err)
		log.Printf("📹 Stream will continue without recording capability")
		return
	}

	log.Printf("✅ Recording successfully enabled for stream: %s", streamID)
	log.Printf("📹 Recording configuration: %s", recordingConfigArn)
}

// Test S3 bucket permissions for IVS
func testS3BucketPermissions() {
	log.Printf("🧪 Testing S3 bucket permissions for IVS...")

	// Try to create a simple test recording configuration to see the exact error
	testConfigName := fmt.Sprintf("test-config-%d", time.Now().Unix())

	s3DestinationConfig := &ivs.S3DestinationConfiguration{
		BucketName: aws.String(s3Bucket),
	}

	destinationConfig := &ivs.DestinationConfiguration{
		S3: s3DestinationConfig,
	}

	createRecordingInput := &ivs.CreateRecordingConfigurationInput{
		Name:                     &testConfigName,
		DestinationConfiguration: destinationConfig,
	}

	log.Printf("🔧 Attempting to create test recording configuration...")
	result, err := ivsClient.CreateRecordingConfiguration(createRecordingInput)
	if err != nil {
		log.Printf("❌ Test recording configuration failed: %v", err)

		// Get more detailed error information
		if awsErr, ok := err.(awserr.Error); ok {
			log.Printf("❌ AWS Error Code: %s", awsErr.Code())
			log.Printf("❌ AWS Error Message: %s", awsErr.Message())
			if awsErr.OrigErr() != nil {
				log.Printf("❌ AWS Error Original: %s", awsErr.OrigErr())
			}
		}

		// Provide specific guidance based on error
		if strings.Contains(err.Error(), "AccessDenied") || strings.Contains(err.Error(), "access denied") {
			log.Printf("💡 SOLUTION: The S3 bucket policy is missing required permissions for AWS IVS")
			log.Printf("💡 Required S3 bucket policy:")
			log.Printf(`{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::moneybagsio/*"
        },
        {
            "Sid": "AllowIVSRecording",
            "Effect": "Allow",
            "Principal": {
                "Service": "ivs.amazonaws.com"
            },
            "Action": [
                "s3:PutObject",
                "s3:PutObjectAcl",
                "s3:GetBucketLocation",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::moneybagsio",
                "arn:aws:s3:::moneybagsio/*"
            ],
            "Condition": {
                "StringEquals": {
                    "aws:SourceAccount": "************"
                }
            }
        }
    ]
}`)
		}
		return
	}

	log.Printf("✅ Test recording configuration created successfully: %s", *result.RecordingConfiguration.Arn)
	log.Printf("✅ S3 bucket permissions are working correctly!")

	// Clean up test configuration
	deleteInput := &ivs.DeleteRecordingConfigurationInput{
		Arn: result.RecordingConfiguration.Arn,
	}
	_, deleteErr := ivsClient.DeleteRecordingConfiguration(deleteInput)
	if deleteErr != nil {
		log.Printf("⚠️ Failed to delete test configuration: %v", deleteErr)
	} else {
		log.Printf("🧹 Test configuration cleaned up successfully")
	}
}

// Clean up all failed recording configurations
func cleanupFailedRecordingConfigurations() {
	log.Printf("🧹 Cleaning up failed recording configurations...")

	listInput := &ivs.ListRecordingConfigurationsInput{}
	result, err := ivsClient.ListRecordingConfigurations(listInput)
	if err != nil {
		log.Printf("❌ Error listing recording configurations for cleanup: %v", err)
		return
	}

	failedCount := 0
	cleanedCount := 0

	for _, config := range result.RecordingConfigurations {
		if *config.State == "CREATE_FAILED" {
			failedCount++
			log.Printf("🗑️ Deleting failed recording configuration: %s", *config.Name)

			deleteInput := &ivs.DeleteRecordingConfigurationInput{
				Arn: config.Arn,
			}

			_, deleteErr := ivsClient.DeleteRecordingConfiguration(deleteInput)
			if deleteErr != nil {
				log.Printf("❌ Failed to delete configuration %s: %v", *config.Name, deleteErr)
			} else {
				cleanedCount++
				log.Printf("✅ Successfully deleted failed configuration: %s", *config.Name)
			}
		}
	}

	log.Printf("🧹 Cleanup complete: %d failed configurations found, %d successfully deleted", failedCount, cleanedCount)
}

// Diagnostic function to check recording configuration status
func checkRecordingConfigurationStatus() {
	log.Printf("🔍 Checking all recording configurations...")

	listInput := &ivs.ListRecordingConfigurationsInput{}
	result, err := ivsClient.ListRecordingConfigurations(listInput)
	if err != nil {
		log.Printf("❌ Error listing recording configurations: %v", err)
		return
	}

	log.Printf("📋 Found %d recording configurations:", len(result.RecordingConfigurations))
	for i, config := range result.RecordingConfigurations {
		log.Printf("   %d. ARN: %s", i+1, *config.Arn)
		log.Printf("      Name: %s", *config.Name)
		log.Printf("      State: %s", *config.State)
		if config.DestinationConfiguration != nil && config.DestinationConfiguration.S3 != nil {
			log.Printf("      S3 Bucket: %s", *config.DestinationConfiguration.S3.BucketName)
		}

		// Get detailed error information for failed configurations
		if *config.State == "CREATE_FAILED" {
			log.Printf("      🔍 Getting detailed error for failed configuration...")
			detailInput := &ivs.GetRecordingConfigurationInput{
				Arn: config.Arn,
			}
			detailResult, err := ivsClient.GetRecordingConfiguration(detailInput)
			if err != nil {
				log.Printf("      ❌ Error getting details: %v", err)
			} else if detailResult.RecordingConfiguration != nil {
				if detailResult.RecordingConfiguration.State != nil {
					log.Printf("      📄 Detailed State: %s", *detailResult.RecordingConfiguration.State)
				}
				// Try to get more error details if available
				log.Printf("      📄 Full Configuration: %+v", detailResult.RecordingConfiguration)
			}
		}
	}
}

// Generate pre-signed URL for S3 object access
func generatePresignedURL(s3Key string, expiration time.Duration) (string, error) {
	if s3Client == nil {
		return "", fmt.Errorf("S3 client not initialized")
	}

	req, _ := s3Client.GetObjectRequest(&s3.GetObjectInput{
		Bucket: aws.String(s3Bucket),
		Key:    aws.String(s3Key),
	})

	urlStr, err := req.Presign(expiration)
	if err != nil {
		return "", err
	}

	return urlStr, nil
}

// Manual VOD completion endpoint for testing
func completeVODProcessing(c *gin.Context) {
	vodID := c.Param("id")

	// Generate real S3 URLs for the recorded stream
	s3RecordingKey := fmt.Sprintf("recordings/danielcheung98/%s.mp4", vodID)
	videoURL := fmt.Sprintf("https://%s.s3.us-west-2.amazonaws.com/%s", s3Bucket, s3RecordingKey)
	thumbnailURL := fmt.Sprintf("https://%s.s3.us-west-2.amazonaws.com/thumbnails/%s.jpg", s3Bucket, vodID)

	log.Printf("🎥 Setting VOD video URL to: %s", videoURL)

	// For testing: Upload a sample video to S3 at the correct path
	go uploadSampleVideoToS3(s3RecordingKey, vodID)

	// Update VOD record with processed URLs
	_, err := db.Exec(`
		UPDATE vods
		SET video_url = ?, thumbnail_url = ?, recording_status = 'completed', updated_at = NOW()
		WHERE vod_id = ?
	`, videoURL, thumbnailURL, vodID)

	if err != nil {
		log.Printf("Error completing VOD processing: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to complete VOD processing"})
		return
	}

	log.Printf("VOD processing manually completed: %s", vodID)
	c.JSON(http.StatusOK, gin.H{
		"message": "VOD processing completed",
		"vod_id":  vodID,
		"status":  "completed",
	})
}

// Get VODs for current user
func getVODs(c *gin.Context) {
	// Get current user
	currentUser := getCurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	rows, err := db.Query(`
		SELECT vod_id, title, description, streamer_name, category_name, duration, views,
		       COALESCE(video_url, '') as video_url,
		       COALESCE(thumbnail_url, '') as thumbnail_url,
		       recording_status, stream_started_at, created_at
		FROM vods
		WHERE streamer_id = ? OR streamer_name = ?
		ORDER BY stream_started_at DESC
		LIMIT 50
	`, currentUser.ID, currentUser.Username)
	if err != nil {
		log.Printf("Error fetching VODs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch VODs"})
		return
	}
	defer rows.Close()

	var vods []gin.H
	for rows.Next() {
		var vod struct {
			VodID           string    `db:"vod_id"`
			Title           string    `db:"title"`
			Description     string    `db:"description"`
			StreamerName    string    `db:"streamer_name"`
			CategoryName    string    `db:"category_name"`
			Duration        int       `db:"duration"`
			Views           int       `db:"views"`
			VideoURL        string    `db:"video_url"`
			ThumbnailURL    string    `db:"thumbnail_url"`
			RecordingStatus string    `db:"recording_status"`
			StreamStartedAt time.Time `db:"stream_started_at"`
			CreatedAt       time.Time `db:"created_at"`
		}

		err := rows.Scan(&vod.VodID, &vod.Title, &vod.Description, &vod.StreamerName,
			&vod.CategoryName, &vod.Duration, &vod.Views, &vod.VideoURL, &vod.ThumbnailURL,
			&vod.RecordingStatus, &vod.StreamStartedAt, &vod.CreatedAt)
		if err != nil {
			log.Printf("Error scanning VOD row: %v", err)
			continue
		}

		// Format duration
		durationStr := fmt.Sprintf("%02d:%02d:%02d",
			vod.Duration/3600, (vod.Duration%3600)/60, vod.Duration%60)

		vods = append(vods, gin.H{
			"id":               vod.VodID,
			"title":            vod.Title,
			"description":      vod.Description,
			"streamer_name":    vod.StreamerName,
			"category_name":    vod.CategoryName,
			"duration":         durationStr,
			"views":            vod.Views,
			"video_url":        vod.VideoURL,
			"thumbnail_url":    vod.ThumbnailURL,
			"recording_status": vod.RecordingStatus,
			"type":             "vod",
			"started_at":       vod.StreamStartedAt,
			"created_at":       vod.CreatedAt,
		})
	}

	c.JSON(http.StatusOK, vods)
}

// Get combined content (live streams first, then VODs)
func getContent(c *gin.Context) {
	var content []gin.H

	// Get current user info to prioritize their stream
	currentUser := getCurrentUser(c)
	var currentUserID string
	if currentUser != nil {
		currentUserID = currentUser.ID
	}

	// First, get live streams - prioritize current user's stream
	var query string
	if currentUserID != "" {
		// Order by: current user's stream first, then by viewers DESC
		query = `
			SELECT id, title, description, streamer_name, category_name, viewers,
			       thumbnail_url, playback_url, started_at, streamer_id
			FROM streams
			WHERE is_live = TRUE
			ORDER BY
				CASE WHEN streamer_id = ? THEN 0 ELSE 1 END,
				viewers DESC, started_at DESC
		`
	} else {
		// Default ordering for non-authenticated users
		query = `
			SELECT id, title, description, streamer_name, category_name, viewers,
			       thumbnail_url, playback_url, started_at, streamer_id
			FROM streams
			WHERE is_live = TRUE
			ORDER BY viewers DESC, started_at DESC
		`
	}

	var liveRows *sql.Rows
	var err error
	if currentUserID != "" {
		liveRows, err = db.Query(query, currentUserID)
	} else {
		liveRows, err = db.Query(query)
	}
	if err != nil {
		log.Printf("Error fetching live streams: %v", err)
	} else {
		defer liveRows.Close()
		for liveRows.Next() {
			var stream struct {
				ID           string    `db:"id"`
				Title        string    `db:"title"`
				Description  string    `db:"description"`
				StreamerName string    `db:"streamer_name"`
				CategoryName string    `db:"category_name"`
				Viewers      int       `db:"viewers"`
				ThumbnailURL string    `db:"thumbnail_url"`
				PlaybackURL  string    `db:"playback_url"`
				StartedAt    time.Time `db:"started_at"`
				StreamerID   string    `db:"streamer_id"`
			}

			var thumbnailURL sql.NullString
		err := liveRows.Scan(&stream.ID, &stream.Title, &stream.Description, &stream.StreamerName,
				&stream.CategoryName, &stream.Viewers, &thumbnailURL, &stream.PlaybackURL, &stream.StartedAt, &stream.StreamerID)
		if thumbnailURL.Valid {
			stream.ThumbnailURL = thumbnailURL.String
		}
			if err != nil {
				log.Printf("Error scanning live stream row: %v", err)
				continue
			}

			// Check if this is the current user's stream
			isCurrentUserStream := currentUserID != "" && stream.StreamerID == currentUserID

			content = append(content, gin.H{
				"id":                    stream.ID,
				"title":                 stream.Title,
				"description":           stream.Description,
				"streamer_name":         stream.StreamerName,
				"category_name":         stream.CategoryName,
				"viewers":               stream.Viewers,
				"thumbnail_url":         stream.ThumbnailURL,
				"playback_url":          stream.PlaybackURL,
				"type":                  "live",
				"started_at":            stream.StartedAt,
				"is_live":               true,
				"is_current_user_stream": isCurrentUserStream,
			})
		}
	}

	// If no live streams, get VODs
	if len(content) == 0 {
		vodRows, err := db.Query(`
			SELECT vod_id, title, description, streamer_name, category_name, duration, views,
			       video_url, thumbnail_url, stream_started_at
			FROM vods
			WHERE recording_status = 'completed'
			ORDER BY stream_started_at DESC
			LIMIT 20
		`)
		if err != nil {
			log.Printf("Error fetching VODs: %v", err)
		} else {
			defer vodRows.Close()
			for vodRows.Next() {
				var vod struct {
					VodID           string    `db:"vod_id"`
					Title           string    `db:"title"`
					Description     string    `db:"description"`
					StreamerName    string    `db:"streamer_name"`
					CategoryName    string    `db:"category_name"`
					Duration        int       `db:"duration"`
					Views           int       `db:"views"`
					VideoURL        string    `db:"video_url"`
					ThumbnailURL    string    `db:"thumbnail_url"`
					StreamStartedAt time.Time `db:"stream_started_at"`
				}

				err := vodRows.Scan(&vod.VodID, &vod.Title, &vod.Description, &vod.StreamerName,
					&vod.CategoryName, &vod.Duration, &vod.Views, &vod.VideoURL, &vod.ThumbnailURL, &vod.StreamStartedAt)
				if err != nil {
					log.Printf("Error scanning VOD row: %v", err)
					continue
				}

				// Format duration
				durationStr := fmt.Sprintf("%02d:%02d:%02d",
					vod.Duration/3600, (vod.Duration%3600)/60, vod.Duration%60)

				content = append(content, gin.H{
					"id":            vod.VodID,
					"title":         vod.Title,
					"description":   vod.Description,
					"streamer_name": vod.StreamerName,
					"category_name": vod.CategoryName,
					"duration":      durationStr,
					"views":         vod.Views,
					"video_url":     vod.VideoURL,
					"thumbnail_url": vod.ThumbnailURL,
					"type":          "vod",
					"started_at":    vod.StreamStartedAt,
					"is_live":       false,
				})
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"content":     content,
		"live_count":  countLiveStreams(),
		"vod_count":   countVODs(),
		"has_live":    len(content) > 0 && content[0]["type"] == "live",
	})
}

// Get browse content (VODs only for browse page)
func getBrowseContent(c *gin.Context) {
	var content []gin.H

	// Get category filter from query parameter
	categoryFilter := c.Query("category")

	var vodRows *sql.Rows
	var err error

	if categoryFilter != "" {
		// Get VODs filtered by category
		vodRows, err = db.Query(`
			SELECT vod_id, title, description, streamer_name, category_name, duration, views,
			       video_url, thumbnail_url, stream_started_at
			FROM vods
			WHERE recording_status = 'completed' AND category_name = ?
			ORDER BY stream_started_at DESC
			LIMIT 50
		`, categoryFilter)
	} else {
		// Get all completed VODs ordered by most recent
		vodRows, err = db.Query(`
			SELECT vod_id, title, description, streamer_name, category_name, duration, views,
			       video_url, thumbnail_url, stream_started_at
			FROM vods
			WHERE recording_status = 'completed'
			ORDER BY stream_started_at DESC
			LIMIT 50
		`)
	}
	if err != nil {
		log.Printf("Error fetching VODs for browse: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch VODs"})
		return
	}
	defer vodRows.Close()

	for vodRows.Next() {
		var vod struct {
			VodID           string    `db:"vod_id"`
			Title           string    `db:"title"`
			Description     string    `db:"description"`
			StreamerName    string    `db:"streamer_name"`
			CategoryName    string    `db:"category_name"`
			Duration        int       `db:"duration"`
			Views           int       `db:"views"`
			VideoURL        string    `db:"video_url"`
			ThumbnailURL    string    `db:"thumbnail_url"`
			StreamStartedAt time.Time `db:"stream_started_at"`
		}

		err := vodRows.Scan(&vod.VodID, &vod.Title, &vod.Description, &vod.StreamerName,
			&vod.CategoryName, &vod.Duration, &vod.Views, &vod.VideoURL, &vod.ThumbnailURL, &vod.StreamStartedAt)
		if err != nil {
			log.Printf("Error scanning VOD row: %v", err)
			continue
		}

		// Format duration
		durationStr := fmt.Sprintf("%02d:%02d:%02d",
			vod.Duration/3600, (vod.Duration%3600)/60, vod.Duration%60)

		content = append(content, gin.H{
			"id":            vod.VodID,
			"title":         vod.Title,
			"description":   vod.Description,
			"streamer_name": vod.StreamerName,
			"category_name": vod.CategoryName,
			"duration":      durationStr,
			"views":         vod.Views,
			"video_url":     vod.VideoURL,
			"thumbnail_url": vod.ThumbnailURL,
			"type":          "vod",
			"started_at":    vod.StreamStartedAt,
			"is_live":       false,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"content":         content,
		"live_count":      0, // No live streams on browse page
		"vod_count":       len(content),
		"has_live":        false, // Browse page only shows VODs
		"category_filter": categoryFilter,
	})
}

// Helper function to count live streams
func countLiveStreams() int {
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM streams WHERE is_live = TRUE").Scan(&count)
	if err != nil {
		log.Printf("Error counting live streams: %v", err)
		return 0
	}
	return count
}

// Helper function to count VODs
func countVODs() int {
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM vods WHERE recording_status = 'completed'").Scan(&count)
	if err != nil {
		log.Printf("Error counting VODs: %v", err)
		return 0
	}
	return count
}

// Get VODs grouped by categories for home page
func getVODCategories(c *gin.Context) {
	// Get all categories that have VODs
	categoryRows, err := db.Query(`
		SELECT DISTINCT c.id, c.name, c.photo_url
		FROM categories c
		INNER JOIN vods v ON c.name = v.category_name
		WHERE v.recording_status = 'completed'
		ORDER BY c.name
	`)
	if err != nil {
		log.Printf("Error fetching categories with VODs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch categories"})
		return
	}
	defer categoryRows.Close()

	var categories []gin.H
	for categoryRows.Next() {
		var categoryID int
		var categoryName, photoURL sql.NullString

		err := categoryRows.Scan(&categoryID, &categoryName, &photoURL)
		if err != nil {
			log.Printf("Error scanning category row: %v", err)
			continue
		}

		// Get recent VODs for this category (limit 6 per category)
		vodRows, err := db.Query(`
			SELECT vod_id, title, description, streamer_name, duration, views,
			       COALESCE(video_url, '') as video_url,
			       COALESCE(thumbnail_url, '') as thumbnail_url,
			       stream_started_at
			FROM vods
			WHERE category_name = ? AND recording_status = 'completed'
			ORDER BY stream_started_at DESC
			LIMIT 6
		`, categoryName.String)

		if err != nil {
			log.Printf("Error fetching VODs for category %s: %v", categoryName.String, err)
			continue
		}

		var vods []gin.H
		for vodRows.Next() {
			var vod struct {
				VodID           string    `db:"vod_id"`
				Title           string    `db:"title"`
				Description     string    `db:"description"`
				StreamerName    string    `db:"streamer_name"`
				Duration        int       `db:"duration"`
				Views           int       `db:"views"`
				VideoURL        string    `db:"video_url"`
				ThumbnailURL    string    `db:"thumbnail_url"`
				StreamStartedAt time.Time `db:"stream_started_at"`
			}

			err := vodRows.Scan(&vod.VodID, &vod.Title, &vod.Description, &vod.StreamerName,
				&vod.Duration, &vod.Views, &vod.VideoURL, &vod.ThumbnailURL, &vod.StreamStartedAt)
			if err != nil {
				log.Printf("Error scanning VOD row: %v", err)
				continue
			}

			// Format duration
			durationStr := fmt.Sprintf("%d:%02d", vod.Duration/60, vod.Duration%60)

			vods = append(vods, gin.H{
				"id":            vod.VodID,
				"title":         vod.Title,
				"description":   vod.Description,
				"streamer_name": vod.StreamerName,
				"duration":      durationStr,
				"views":         vod.Views,
				"video_url":     vod.VideoURL,
				"thumbnail_url": vod.ThumbnailURL,
				"created_at":    vod.StreamStartedAt.Format("2006-01-02 15:04:05"),
				"type":          "vod",
			})
		}
		vodRows.Close()

		// Only include categories that have VODs
		if len(vods) > 0 {
			var photoURLStr string
			if photoURL.Valid {
				photoURLStr = photoURL.String
			}

			categories = append(categories, gin.H{
				"id":        categoryID,
				"name":      categoryName.String,
				"photo_url": photoURLStr,
				"vods":      vods,
				"vod_count": len(vods),
			})
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"categories": categories,
		"total_categories": len(categories),
	})
}

// VOD player page
func vodPlayerPage(c *gin.Context) {
	vodID := c.Param("id")

	// Get VOD details with creator avatar
	var vod VOD
	var creatorAvatarURL sql.NullString
	err := db.QueryRow(`
		SELECT v.vod_id, v.title, v.description, v.streamer_id, v.streamer_name,
		       v.category_name, v.duration, v.views, v.video_url, v.thumbnail_url,
		       v.recording_status, v.stream_started_at, v.created_at, u.avatar_url
		FROM vods v
		LEFT JOIN users u ON v.streamer_id = u.id
		WHERE v.vod_id = ? AND v.recording_status = 'completed'`, vodID).Scan(
		&vod.VodID, &vod.Title, &vod.Description, &vod.StreamerID, &vod.StreamerName,
		&vod.CategoryName, &vod.Duration, &vod.Views, &vod.VideoURL, &vod.ThumbnailURL,
		&vod.RecordingStatus, &vod.StreamStartedAt, &vod.CreatedAt, &creatorAvatarURL)

	if err != nil {
		c.HTML(http.StatusNotFound, "index.html", gin.H{
			"title":    "VOD Not Found",
			"siteName": siteName,
			"error":    "VOD not found or still processing",
		})
		return
	}

	// Increment view count
	db.Exec("UPDATE vods SET views = views + 1 WHERE vod_id = ?", vodID)
	vod.Views++

	// Get user info from query params or JWT token
	userID := c.Query("user_id")
	username := c.Query("username")

	// Try to get from JWT token if not in query params
	if userID == "" || username == "" {
		tokenString, err := c.Cookie("auth_token")
		if err == nil {
			claims, err := validateJWTToken(tokenString)
			if err == nil {
				userID = claims.UserID
				username = claims.Username
			}
		}
	}

	// Check if user is the creator
	isCreator := (userID == vod.StreamerID)
	isAuthenticated := (userID != "")

	// Decide how to serve the VOD URL
	var videoURL string
	if strings.Contains(strings.ToLower(vod.VideoURL), ".m3u8") {
		// Use our proxy manifest so segments are also accessible without S3 CORS/public access
		videoURL = fmt.Sprintf("/api/vod/hls/%s/manifest", vodID)
	} else if strings.Contains(vod.VideoURL, "s3.us-west-2.amazonaws.com") {
		// For non-HLS assets stored privately on S3, use a pre-signed URL
		s3Key := strings.Split(vod.VideoURL, ".amazonaws.com/")[1]
		presignedURL, err := generatePresignedURL(s3Key, 2*time.Hour)
		if err != nil {
			log.Printf("Error generating pre-signed URL: %v", err)
			videoURL = vod.VideoURL // Fallback to original URL
		} else {
			videoURL = presignedURL
			log.Printf("🔐 Generated pre-signed URL for VOD: %s", vodID)
		}
	} else {
		videoURL = vod.VideoURL // Use original URL for non-S3 videos
	}

	log.Printf("🎥 VOD Player - VideoURL: %s, UserID: %s, IsAuthenticated: %t", videoURL, userID, isAuthenticated)

	// Determine creator avatar URL
	var creatorAvatar string
	if creatorAvatarURL.Valid && creatorAvatarURL.String != "" {
		creatorAvatar = creatorAvatarURL.String
	} else {
		creatorAvatar = "/static/images/default-avatar.png"
	}

	c.HTML(http.StatusOK, "video-player.html", gin.H{
		"video": gin.H{
			"VideoID":        vod.VodID,
			"Title":          vod.Title,
			"Description":    vod.Description,
			"CreatorID":      vod.StreamerID,
			"CreatorName":    vod.StreamerName,
			"CreatorAvatar":  creatorAvatar,
			"CategoryName":   vod.CategoryName,
			"Duration":       vod.Duration,
			"Views":          vod.Views,
			"VideoURL":       videoURL,
			"ThumbnailURL":   vod.ThumbnailURL,
			"CreatedAt":      vod.StreamStartedAt, // Use stream start time as creation time
		},
		"userID":         userID,
		"username":       username,
		"isCreator":      isCreator,
		"isAuthenticated": isAuthenticated,
		"siteName":       siteName,
		"title":          vod.Title + " - VOD Player",
	})
}

// User profile page
func userProfilePage(c *gin.Context) {
	profileUsername := c.Param("username")

	// Get user profile information from database
	var userProfile struct {
		Username    string         `json:"username"`
		DisplayName sql.NullString `json:"display_name"`
		Bio         sql.NullString `json:"bio"`
		AvatarURL   sql.NullString `json:"avatar_url"`
		BannerURL   sql.NullString `json:"banner_url"`
		CreatedAt   time.Time      `json:"created_at"`
	}

	err := db.QueryRow(`
		SELECT username, display_name, bio, avatar_url, banner_url, created_at
		FROM users WHERE username = ?`, profileUsername).Scan(
		&userProfile.Username, &userProfile.DisplayName, &userProfile.Bio,
		&userProfile.AvatarURL, &userProfile.BannerURL, &userProfile.CreatedAt)

	if err != nil {
		c.HTML(http.StatusNotFound, "index.html", gin.H{
			"title":    "User Not Found",
			"siteName": siteName,
			"error":    "User not found",
		})
		return
	}

	// Get user's VODs
	rows, err := db.Query(`
		SELECT vod_id, title, description, category_name, duration, views,
		       thumbnail_url, stream_started_at, created_at
		FROM vods
		WHERE streamer_name = ? AND recording_status = 'completed'
		ORDER BY stream_started_at DESC
		LIMIT 50`, profileUsername)

	var userVODs []VOD
	var totalViews int
	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var vod VOD
			err := rows.Scan(&vod.VodID, &vod.Title, &vod.Description, &vod.CategoryName,
				&vod.Duration, &vod.Views, &vod.ThumbnailURL, &vod.StreamStartedAt, &vod.CreatedAt)
			if err == nil {
				userVODs = append(userVODs, vod)
				totalViews += vod.Views
			}
		}
	}

	// Check if current user is authenticated
	var currentUserID, currentUsername string
	isAuthenticated := false
	tokenString, err := c.Cookie("auth_token")
	if err == nil {
		claims, err := validateJWTToken(tokenString)
		if err == nil {
			currentUserID = claims.UserID
			currentUsername = claims.Username
			isAuthenticated = true
		}
	}

	// Check if this is the current user's own profile
	isOwnProfile := (currentUsername == profileUsername)

	c.HTML(http.StatusOK, "user-profile.html", gin.H{
		"title":           profileUsername + " - Profile",
		"siteName":        siteName,
		"profileUser": gin.H{
			"Username":    userProfile.Username,
			"DisplayName": userProfile.DisplayName.String,
			"Bio":         userProfile.Bio.String,
			"AvatarURL":   userProfile.AvatarURL.String,
			"BannerURL":   userProfile.BannerURL.String,
			"CreatedAt":   userProfile.CreatedAt,
		},
		"userVODs":        userVODs,
		"totalViews":      totalViews,
		"currentUserID":   currentUserID,
		"currentUsername": currentUsername,
		"username":        currentUsername, // Add this for header template
		"isAuthenticated": isAuthenticated,
		"isOwnProfile":    isOwnProfile,
	})
}

// Search page
func searchPage(c *gin.Context) {
	query := c.Query("q")

	// Check if current user is authenticated
	var currentUserID, currentUsername string
	isAuthenticated := false
	tokenString, err := c.Cookie("auth_token")
	if err == nil {
		claims, err := validateJWTToken(tokenString)
		if err == nil {
			currentUserID = claims.UserID
			currentUsername = claims.Username
			isAuthenticated = true
		}
	}

	var searchResults []map[string]interface{}

	if query != "" {
		// Search for users
		rows, err := db.Query(`
			SELECT username, display_name, bio, avatar_url, created_at
			FROM users
			WHERE username LIKE ? OR display_name LIKE ?
			ORDER BY username ASC
			LIMIT 20`, "%"+query+"%", "%"+query+"%")

		if err == nil {
			defer rows.Close()
			for rows.Next() {
				var user struct {
					Username    string         `json:"username"`
					DisplayName sql.NullString `json:"display_name"`
					Bio         sql.NullString `json:"bio"`
					AvatarURL   sql.NullString `json:"avatar_url"`
					CreatedAt   time.Time      `json:"created_at"`
				}

				err := rows.Scan(&user.Username, &user.DisplayName, &user.Bio, &user.AvatarURL, &user.CreatedAt)
				if err == nil {
					searchResults = append(searchResults, map[string]interface{}{
						"type":        "user",
						"username":    user.Username,
						"displayName": user.DisplayName.String,
						"bio":         user.Bio.String,
						"avatarURL":   user.AvatarURL.String,
						"createdAt":   user.CreatedAt,
					})
				}
			}
		}
	}

	c.HTML(http.StatusOK, "search.html", gin.H{
		"title":           "Search Results - " + query,
		"siteName":        siteName,
		"query":           query,
		"searchResults":   searchResults,
		"currentUserID":   currentUserID,
		"currentUsername": currentUsername,
		"isAuthenticated": isAuthenticated,
	})
}

// Proxy HLS manifest to rewrite relative URIs to backend proxy endpoints
func vodHLSManifest(c *gin.Context) {
	vodID := c.Param("vodID")
	// load the VOD's video_url
	var videoURL string
	err := db.QueryRow("SELECT video_url FROM vods WHERE vod_id = ?", vodID).Scan(&videoURL)
	if err != nil {
		c.String(http.StatusNotFound, "VOD not found")
		return
	}
	if !strings.Contains(videoURL, ".amazonaws.com/") {
		c.String(http.StatusBadRequest, "VOD is not stored on S3")
		return
	}
	// Determine which manifest key to fetch
	s3Key := strings.Split(videoURL, ".amazonaws.com/")[1]
	if alt := c.Query("key"); strings.TrimSpace(alt) != "" {
		// use provided child manifest key
		s3Key = alt
	}
	baseDir := path.Dir(s3Key)
	// Fetch manifest from S3
	obj, err := s3Client.GetObject(&s3.GetObjectInput{
		Bucket: aws.String(s3Bucket),
		Key:    aws.String(s3Key),
	})
	if err != nil {
		log.Printf("Error fetching HLS manifest from S3: %v", err)
		c.String(http.StatusInternalServerError, "Failed to fetch manifest")
		return
	}
	defer obj.Body.Close()
	data, err := io.ReadAll(obj.Body)
	if err != nil {
		c.String(http.StatusInternalServerError, "Failed to read manifest")
		return
	}
	lines := strings.Split(string(data), "\n")
	var outLines []string
	for _, line := range lines {
		trim := strings.TrimSpace(line)
		if trim == "" || strings.HasPrefix(trim, "#") {
			outLines = append(outLines, line)
			continue
		}
		// absolute URLs pass through as-is
		if strings.HasPrefix(trim, "http://") || strings.HasPrefix(trim, "https://") {
			outLines = append(outLines, trim)
			continue
		}
		// relative reference
		joined := path.Clean(path.Join(baseDir, trim))
		if strings.HasSuffix(strings.ToLower(trim), ".m3u8") {
			rewritten := "/api/vod/hls/" + vodID + "/manifest?key=" + url.QueryEscape(joined)
			outLines = append(outLines, rewritten)
		} else {
			rewritten := "/api/vod/hls/" + vodID + "/segment?key=" + url.QueryEscape(joined)
			outLines = append(outLines, rewritten)
		}
	}
	c.Header("Content-Type", "application/vnd.apple.mpegurl; charset=utf-8")
	c.Header("Cache-Control", "no-cache")
	c.String(http.StatusOK, strings.Join(outLines, "\n"))
}

// Proxy HLS segments (and other assets) from S3 through backend to avoid CORS
func vodHLSSegment(c *gin.Context) {
	vodID := c.Param("vodID")
	key := c.Query("key")
	if strings.TrimSpace(key) == "" {
		c.String(http.StatusBadRequest, "missing key")
		return
	}
	// Validate key is under the VOD base directory
	var videoURL string
	err := db.QueryRow("SELECT video_url FROM vods WHERE vod_id = ?", vodID).Scan(&videoURL)
	if err != nil {
		c.String(http.StatusNotFound, "VOD not found")
		return
	}
	if !strings.Contains(videoURL, ".amazonaws.com/") {
		c.String(http.StatusBadRequest, "VOD is not stored on S3")
		return
	}
	baseDir := path.Dir(strings.Split(videoURL, ".amazonaws.com/")[1])
	clean := path.Clean(key)
	if !(clean == baseDir || strings.HasPrefix(clean, baseDir+"/")) {
		c.String(http.StatusForbidden, "invalid key")
		return
	}
	// Fetch object and stream to client
	obj, err := s3Client.GetObject(&s3.GetObjectInput{
		Bucket: aws.String(s3Bucket),
		Key:    aws.String(clean),
	})
	if err != nil {
		log.Printf("Error fetching HLS asset from S3: %v", err)
		c.String(http.StatusInternalServerError, "Failed to fetch asset")
		return
	}
	defer obj.Body.Close()
	ctype := "application/octet-stream"
	if obj.ContentType != nil && *obj.ContentType != "" {
		ctype = *obj.ContentType
	} else if strings.HasSuffix(strings.ToLower(clean), ".ts") {
		ctype = "video/MP2T"
	} else if strings.HasSuffix(strings.ToLower(clean), ".m3u8") {
		ctype = "application/vnd.apple.mpegurl"
	}
	c.Header("Content-Type", ctype)
	c.Header("Cache-Control", "private, max-age=600")
	if _, err := io.Copy(c.Writer, obj.Body); err != nil {
		log.Printf("Error streaming HLS asset: %v", err)
	}
}

// Proxy HLS manifest for regular videos (similar to VOD HLS)
func videoHLSManifest(c *gin.Context) {
	videoID := c.Param("videoID")
	// load the video's video_url
	var videoURL string
	err := db.QueryRow("SELECT video_url FROM videos WHERE video_id = ?", videoID).Scan(&videoURL)
	if err != nil {
		c.String(http.StatusNotFound, "Video not found")
		return
	}
	if !strings.Contains(videoURL, ".amazonaws.com/") {
		c.String(http.StatusBadRequest, "Video is not stored on S3")
		return
	}
	// Determine which manifest key to fetch
	s3Key := strings.Split(videoURL, ".amazonaws.com/")[1]
	if alt := c.Query("key"); strings.TrimSpace(alt) != "" {
		// use provided child manifest key
		s3Key = alt
	}

	// Fetch manifest from S3
	obj, err := s3Client.GetObject(&s3.GetObjectInput{
		Bucket: aws.String(s3Bucket),
		Key:    aws.String(s3Key),
	})
	if err != nil {
		log.Printf("Error fetching video HLS manifest from S3: %v", err)
		c.String(http.StatusInternalServerError, "Failed to fetch manifest")
		return
	}
	defer obj.Body.Close()

	// Read manifest content
	content, err := io.ReadAll(obj.Body)
	if err != nil {
		log.Printf("Error reading video HLS manifest: %v", err)
		c.String(http.StatusInternalServerError, "Failed to read manifest")
		return
	}

	// Parse and rewrite manifest
	lines := strings.Split(string(content), "\n")
	var outLines []string
	baseDir := path.Dir(s3Key)

	for _, line := range lines {
		trim := strings.TrimSpace(line)
		if trim == "" || strings.HasPrefix(trim, "#") {
			outLines = append(outLines, line)
			continue
		}
		if strings.HasPrefix(trim, "http://") || strings.HasPrefix(trim, "https://") {
			// absolute URL, keep as-is
			outLines = append(outLines, line)
			continue
		}
		// relative reference
		joined := path.Clean(path.Join(baseDir, trim))
		if strings.HasSuffix(strings.ToLower(trim), ".m3u8") {
			rewritten := "/api/video/hls/" + videoID + "/manifest?key=" + url.QueryEscape(joined)
			outLines = append(outLines, rewritten)
		} else {
			rewritten := "/api/video/hls/" + videoID + "/segment?key=" + url.QueryEscape(joined)
			outLines = append(outLines, rewritten)
		}
	}
	c.Header("Content-Type", "application/vnd.apple.mpegurl; charset=utf-8")
	c.Header("Cache-Control", "no-cache")
	c.String(http.StatusOK, strings.Join(outLines, "\n"))
}

// Proxy HLS segments for regular videos (similar to VOD HLS)
func videoHLSSegment(c *gin.Context) {
	videoID := c.Param("videoID")
	key := c.Query("key")
	if strings.TrimSpace(key) == "" {
		c.String(http.StatusBadRequest, "missing key")
		return
	}
	// Validate key is under the video base directory
	var videoURL string
	err := db.QueryRow("SELECT video_url FROM videos WHERE video_id = ?", videoID).Scan(&videoURL)
	if err != nil {
		c.String(http.StatusNotFound, "Video not found")
		return
	}
	if !strings.Contains(videoURL, ".amazonaws.com/") {
		c.String(http.StatusBadRequest, "Video is not stored on S3")
		return
	}
	baseDir := path.Dir(strings.Split(videoURL, ".amazonaws.com/")[1])
	clean := path.Clean(key)
	if !(clean == baseDir || strings.HasPrefix(clean, baseDir+"/")) {
		c.String(http.StatusForbidden, "invalid key")
		return
	}
	// Fetch object and stream to client
	obj, err := s3Client.GetObject(&s3.GetObjectInput{
		Bucket: aws.String(s3Bucket),
		Key:    aws.String(clean),
	})
	if err != nil {
		log.Printf("Error fetching video HLS asset from S3: %v", err)
		c.String(http.StatusInternalServerError, "Failed to fetch asset")
		return
	}
	defer obj.Body.Close()
	ctype := "application/octet-stream"
	if obj.ContentType != nil && *obj.ContentType != "" {
		ctype = *obj.ContentType
	} else if strings.HasSuffix(strings.ToLower(clean), ".ts") {
		ctype = "video/MP2T"
	} else if strings.HasSuffix(strings.ToLower(clean), ".m3u8") {
		ctype = "application/vnd.apple.mpegurl"
	}
	c.Header("Content-Type", ctype)
	c.Header("Cache-Control", "private, max-age=600")
	if _, err := io.Copy(c.Writer, obj.Body); err != nil {
		log.Printf("Error streaming video HLS asset: %v", err)
	}
}

// Video API handlers
func uploadVideo(c *gin.Context) {
	// Check file size limit (2GB)
	c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, 2<<30)

	err := c.Request.ParseMultipartForm(2 << 30) // 2GB
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File too large (max 2GB)"})
		return
	}

	file, header, err := c.Request.FormFile("video")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No video file provided"})
		return
	}
	defer file.Close()

	// Validate file type
	contentType := header.Header.Get("Content-Type")
	if !strings.HasPrefix(contentType, "video/") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Only video files are allowed"})
		return
	}

	// Get form data
	title := c.Request.FormValue("title")
	category := c.Request.FormValue("category")
	description := c.Request.FormValue("description")
	userID := c.Request.FormValue("user_id")
	username := c.Request.FormValue("username")

	if title == "" || category == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Title and category are required"})
		return
	}

	if userID == "" || username == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User information is required"})
		return
	}

	// Generate unique video ID
	videoID := fmt.Sprintf("video_%d", time.Now().Unix())

	// Create temporary directory for processing
	tempDir := fmt.Sprintf("temp/%s", videoID)
	os.MkdirAll(tempDir, 0755)

	// Save original file
	originalPath := fmt.Sprintf("%s/original_%s", tempDir, header.Filename)
	out, err := os.Create(originalPath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file"})
		return
	}
	defer out.Close()

	_, err = io.Copy(out, file)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file"})
		return
	}

	// Get file size
	fileInfo, _ := out.Stat()
	fileSize := fileInfo.Size()

	// Get category ID
	categoryID := getCategoryIDByName(category)

	// Insert video record into database
	video := Video{
		VideoID:         videoID,
		Title:           title,
		Description:     description,
		CreatorID:       userID,
		CreatorName:     username,
		CategoryID:      categoryID,
		CategoryName:    category,
		Status:          "processing",
		ProcessingStage: "upload",
		OriginalFilename: header.Filename,
		FileSize:        fileSize,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	_, err = db.Exec(`
		INSERT INTO videos (video_id, title, description, creator_id, creator_name, category_id, category_name,
			status, processing_stage, original_filename, file_size, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		video.VideoID, video.Title, video.Description, video.CreatorID, video.CreatorName,
		video.CategoryID, video.CategoryName, video.Status, video.ProcessingStage,
		video.OriginalFilename, video.FileSize, video.CreatedAt, video.UpdatedAt)

	if err != nil {
		log.Printf("Error inserting video: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save video information"})
		return
	}

	// Start background processing
	go processVideo(videoID, originalPath, tempDir)

	c.JSON(http.StatusOK, gin.H{
		"video_id": videoID,
		"status":   "processing",
		"message":  "Video upload successful, processing started",
	})
}

// Process video with FFmpeg
func processVideo(videoID, originalPath, tempDir string) {
	log.Printf("Starting video processing for %s", videoID)

	// Check if FFmpeg is available
	_, err := exec.LookPath("ffmpeg")
	if err != nil {
		log.Printf("FFmpeg not available for %s, using original file: %v", videoID, err)
		// Use original file without processing
		processVideoWithoutFFmpeg(videoID, originalPath, tempDir)
		return
	}

	// Update status to encoding
	updateVideoProcessingStage(videoID, "encoding")

	// Generate output filename
	outputPath := fmt.Sprintf("%s/encoded_%s.mp4", tempDir, videoID)

	// Generate HLS output directory
	hlsDir := fmt.Sprintf("%s/hls_%s", tempDir, videoID)
	err = os.MkdirAll(hlsDir, 0755)
	if err != nil {
		log.Printf("Failed to create HLS directory for %s: %v", videoID, err)
		processVideoWithoutFFmpeg(videoID, originalPath, tempDir)
		return
	}

	hlsPlaylist := fmt.Sprintf("%s/playlist.m3u8", hlsDir)

	// FFmpeg command to create HLS with multiple quality levels
	cmd := exec.Command("ffmpeg",
		"-i", originalPath,
		// Video encoding settings
		"-c:v", "libx264",
		"-preset", "medium",
		"-crf", "23",
		// Audio encoding
		"-c:a", "aac",
		"-b:a", "128k",
		// HLS specific settings
		"-f", "hls",
		"-hls_time", "6",           // 6 second segments
		"-hls_playlist_type", "vod", // Video on demand
		"-hls_segment_filename", fmt.Sprintf("%s/segment_%%03d.ts", hlsDir),
		"-y", // Overwrite output files
		hlsPlaylist)

	// Run FFmpeg
	err = cmd.Run()
	if err != nil {
		log.Printf("FFmpeg encoding failed for %s: %v", videoID, err)
		// Fallback to original file
		log.Printf("Falling back to original file for %s", videoID)
		processVideoWithoutFFmpeg(videoID, originalPath, tempDir)
		return
	}

	log.Printf("Video encoding completed for %s", videoID)

	// Generate thumbnail
	thumbnailPath := fmt.Sprintf("%s/thumbnail_%s.jpg", tempDir, videoID)
	generateThumbnail(originalPath, thumbnailPath)

	// Update status to uploading to S3
	updateVideoProcessingStage(videoID, "uploading_s3")

	// Upload HLS files to S3
	videoURL, thumbnailURL, err := uploadHLSToS3(videoID, hlsDir, thumbnailPath)
	if err != nil {
		log.Printf("HLS S3 upload failed for %s: %v", videoID, err)
		// Fallback to original file
		log.Printf("HLS S3 upload failed, falling back to original file for %s", videoID)
		processVideoWithoutFFmpeg(videoID, originalPath, tempDir)
		return
	}

	// Get video duration
	duration := getVideoDuration(outputPath)

	// Update video record with final URLs and status
	_, err = db.Exec(`
		UPDATE videos
		SET video_url = ?, thumbnail_url = ?, duration = ?, status = ?, processing_stage = ?, updated_at = ?
		WHERE video_id = ?`,
		videoURL, thumbnailURL, duration, "completed", "completed", time.Now(), videoID)

	if err != nil {
		log.Printf("Error updating video record for %s: %v", videoID, err)
	}

	log.Printf("Video processing completed for %s", videoID)

	// Cleanup temporary files
	cleanup(tempDir)
}

// Process video without FFmpeg (fallback)
func processVideoWithoutFFmpeg(videoID, originalPath, tempDir string) {
	log.Printf("Processing video %s without FFmpeg", videoID)

	// Update status to uploading to S3
	updateVideoProcessingStage(videoID, "uploading_s3")

	// Upload original file to S3
	videoURL, err := uploadFileToS3(originalPath, fmt.Sprintf("videos/%s_original.mp4", videoID))
	if err != nil {
		log.Printf("Failed to upload original file for %s: %v", videoID, err)
		updateVideoStatus(videoID, "failed", "s3_upload_failed")
		cleanup(tempDir)
		return
	}

	// Use a default thumbnail
	thumbnailURL := "/static/images/default-video-thumbnail.svg"

	// Update video record with original file URL
	_, err = db.Exec(`
		UPDATE videos
		SET video_url = ?, thumbnail_url = ?, duration = ?, status = ?, processing_stage = ?, updated_at = ?
		WHERE video_id = ?`,
		videoURL, thumbnailURL, 0, "completed", "completed", time.Now(), videoID)

	if err != nil {
		log.Printf("Error updating video record for %s: %v", videoID, err)
		updateVideoStatus(videoID, "failed", "database_update_failed")
		cleanup(tempDir)
		return
	}

	log.Printf("Video processing completed for %s (without FFmpeg)", videoID)

	// Cleanup temporary files
	cleanup(tempDir)
}

// Helper functions
func updateVideoProcessingStage(videoID, stage string) {
	db.Exec("UPDATE videos SET processing_stage = ?, updated_at = ? WHERE video_id = ?",
		stage, time.Now(), videoID)
}

func updateVideoStatus(videoID, status, stage string) {
	db.Exec("UPDATE videos SET status = ?, processing_stage = ?, updated_at = ? WHERE video_id = ?",
		status, stage, time.Now(), videoID)
}

func generateThumbnail(inputPath, outputPath string) error {
	cmd := exec.Command("ffmpeg",
		"-i", inputPath,
		"-ss", "00:00:03", // Take screenshot at 3 seconds
		"-vframes", "1",
		"-q:v", "2",
		"-y",
		outputPath)

	return cmd.Run()
}

// Generate thumbnail from HLS stream recording
func generateThumbnailFromHLS(vodID, playlistKey string) string {
	// Check if FFmpeg is available
	_, err := exec.LookPath("ffmpeg")
	if err != nil {
		log.Printf("FFmpeg not available for thumbnail generation: %v", err)
		return ""
	}

	if s3Client == nil {
		log.Printf("S3 client not available for thumbnail generation")
		return ""
	}

	// Create temp directory for processing
	tempDir := fmt.Sprintf("/tmp/vod_thumbnail_%s", vodID)
	err = os.MkdirAll(tempDir, 0755)
	if err != nil {
		log.Printf("Error creating temp directory for thumbnail: %v", err)
		return ""
	}
	defer os.RemoveAll(tempDir)

	// Download the HLS playlist to get the first segment
	playlistURL := fmt.Sprintf("https://%s.s3.us-west-2.amazonaws.com/%s", s3Bucket, playlistKey)

	// Generate thumbnail using FFmpeg directly from HLS URL
	thumbnailPath := fmt.Sprintf("%s/thumbnail_%s.jpg", tempDir, vodID)

	log.Printf("Generating thumbnail from HLS stream: %s", playlistURL)

	cmd := exec.Command("ffmpeg",
		"-i", playlistURL,
		"-ss", "00:00:05", // Take screenshot at 5 seconds
		"-vframes", "1",
		"-q:v", "2",
		"-vf", "scale=640:360", // Scale to standard thumbnail size
		"-y",
		thumbnailPath)

	err = cmd.Run()
	if err != nil {
		log.Printf("Error generating thumbnail from HLS: %v", err)
		return ""
	}

	// Upload thumbnail to S3
	thumbnailKey := fmt.Sprintf("thumbnails/%s.jpg", vodID)
	thumbnailURL, err := uploadFileToS3(thumbnailPath, thumbnailKey)
	if err != nil {
		log.Printf("Error uploading thumbnail to S3: %v", err)
		return ""
	}

	log.Printf("✅ Thumbnail generated and uploaded: %s", thumbnailURL)
	return thumbnailURL
}

func getVideoDuration(videoPath string) int {
	cmd := exec.Command("ffprobe",
		"-v", "quiet",
		"-show_entries", "format=duration",
		"-of", "csv=p=0",
		videoPath)

	output, err := cmd.Output()
	if err != nil {
		return 0
	}

	durationStr := strings.TrimSpace(string(output))
	duration, err := strconv.ParseFloat(durationStr, 64)
	if err != nil {
		return 0
	}

	return int(duration)
}

func cleanup(tempDir string) {
	os.RemoveAll(tempDir)
}

// Process VOD with FFmpeg (for admin uploaded VODs)
func processVOD(vodID, originalPath, tempDir string) {
	log.Printf("Starting VOD processing for %s", vodID)

	// Check if FFmpeg is available
	_, err := exec.LookPath("ffmpeg")
	if err != nil {
		log.Printf("FFmpeg not available for %s, using original file: %v", vodID, err)
		// Use original file without processing
		processVODWithoutFFmpeg(vodID, originalPath, tempDir)
		return
	}

	// Update status to encoding
	updateVODProcessingStage(vodID, "encoding")

	// Generate output filename
	outputPath := fmt.Sprintf("%s/encoded_%s.mp4", tempDir, vodID)

	// Generate HLS output directory
	hlsDir := fmt.Sprintf("%s/hls_%s", tempDir, vodID)
	err = os.MkdirAll(hlsDir, 0755)
	if err != nil {
		log.Printf("Failed to create HLS directory for VOD %s: %v", vodID, err)
		processVODWithoutFFmpeg(vodID, originalPath, tempDir)
		return
	}

	hlsPlaylist := fmt.Sprintf("%s/playlist.m3u8", hlsDir)

	// FFmpeg command to create HLS with multiple quality levels
	cmd := exec.Command("ffmpeg",
		"-i", originalPath,
		// Video encoding settings
		"-c:v", "libx264",
		"-preset", "medium",
		"-crf", "23",
		// Audio encoding
		"-c:a", "aac",
		"-b:a", "128k",
		// HLS specific settings
		"-f", "hls",
		"-hls_time", "6",           // 6 second segments
		"-hls_playlist_type", "vod", // Video on demand
		"-hls_segment_filename", fmt.Sprintf("%s/segment_%%03d.ts", hlsDir),
		"-y", // Overwrite output files
		hlsPlaylist)

	// Run FFmpeg
	err = cmd.Run()
	if err != nil {
		log.Printf("FFmpeg encoding failed for %s: %v", vodID, err)
		// Fallback to original file
		log.Printf("Falling back to original file for %s", vodID)
		processVODWithoutFFmpeg(vodID, originalPath, tempDir)
		return
	}

	log.Printf("VOD encoding completed for %s", vodID)

	// Generate thumbnail
	thumbnailPath := fmt.Sprintf("%s/thumbnail_%s.jpg", tempDir, vodID)
	generateThumbnail(originalPath, thumbnailPath)

	// Update status to uploading to S3
	updateVODProcessingStage(vodID, "uploading_s3")

	// Upload HLS files to S3
	videoURL, thumbnailURL, err := uploadHLSToS3(vodID, hlsDir, thumbnailPath)
	if err != nil {
		log.Printf("HLS S3 upload failed for VOD %s: %v", vodID, err)
		// Fallback to original file
		log.Printf("HLS S3 upload failed, falling back to original file for VOD %s", vodID)
		processVODWithoutFFmpeg(vodID, originalPath, tempDir)
		return
	}

	// Get video duration
	duration := getVideoDuration(outputPath)

	// Update VOD record with final URLs and status
	_, err = db.Exec(`
		UPDATE vods
		SET video_url = ?, thumbnail_url = ?, duration = ?, recording_status = ?, updated_at = ?
		WHERE vod_id = ?`,
		videoURL, thumbnailURL, duration, "completed", time.Now(), vodID)

	if err != nil {
		log.Printf("Error updating VOD record for %s: %v", vodID, err)
	}

	log.Printf("VOD processing completed for %s", vodID)

	// Cleanup temporary files
	cleanup(tempDir)
}

// Process VOD without FFmpeg (fallback)
func processVODWithoutFFmpeg(vodID, originalPath, tempDir string) {
	log.Printf("Processing VOD %s without FFmpeg", vodID)

	// Update status to uploading to S3
	updateVODProcessingStage(vodID, "uploading_s3")
	log.Printf("VOD %s: Updated status to uploading_s3", vodID)

	// Upload original file to S3
	log.Printf("VOD %s: Starting S3 upload from %s", vodID, originalPath)
	videoURL, err := uploadFileToS3(originalPath, fmt.Sprintf("videos/%s_original.mp4", vodID))
	if err != nil {
		log.Printf("Failed to upload original file for %s: %v", vodID, err)
		updateVODStatus(vodID, "failed")
		cleanup(tempDir)
		return
	}
	log.Printf("VOD %s: S3 upload completed, URL: %s", vodID, videoURL)

	// Use a default thumbnail
	thumbnailURL := "/static/images/default-video-thumbnail.svg"

	// Update VOD record with original file URL
	log.Printf("VOD %s: Updating database record", vodID)
	_, err = db.Exec(`
		UPDATE vods
		SET video_url = ?, thumbnail_url = ?, duration = ?, recording_status = ?, updated_at = ?
		WHERE vod_id = ?`,
		videoURL, thumbnailURL, 0, "completed", time.Now(), vodID)

	if err != nil {
		log.Printf("Error updating VOD record for %s: %v", vodID, err)
		updateVODStatus(vodID, "failed")
		cleanup(tempDir)
		return
	}

	log.Printf("VOD processing completed for %s (without FFmpeg)", vodID)

	// Cleanup temporary files
	log.Printf("VOD %s: Cleaning up temporary files", vodID)
	cleanup(tempDir)
}

// Helper functions for VOD processing
func updateVODProcessingStage(vodID, stage string) {
	db.Exec("UPDATE vods SET recording_status = ?, updated_at = ? WHERE vod_id = ?",
		stage, time.Now(), vodID)
}

func updateVODStatus(vodID, status string) {
	db.Exec("UPDATE vods SET recording_status = ?, updated_at = ? WHERE vod_id = ?",
		status, time.Now(), vodID)
}

func uploadVideoToS3(videoID, videoPath, thumbnailPath string) (string, string, error) {
	if s3Client == nil {
		return "", "", fmt.Errorf("S3 client not configured")
	}

	// Upload video file
	videoKey := fmt.Sprintf("videos/%s.mp4", videoID)
	videoURL, err := uploadFileToS3(videoPath, videoKey)
	if err != nil {
		return "", "", fmt.Errorf("failed to upload video: %v", err)
	}

	// Upload thumbnail
	thumbnailKey := fmt.Sprintf("thumbnails/%s.jpg", videoID)
	thumbnailURL, err := uploadFileToS3(thumbnailPath, thumbnailKey)
	if err != nil {
		return "", "", fmt.Errorf("failed to upload thumbnail: %v", err)
	}

	return videoURL, thumbnailURL, nil
}

// Upload HLS files to S3
func uploadHLSToS3(videoID, hlsDir, thumbnailPath string) (string, string, error) {
	if s3Client == nil {
		return "", "", fmt.Errorf("S3 client not configured")
	}

	log.Printf("Uploading HLS files for video %s from directory %s", videoID, hlsDir)

	// Read HLS directory
	files, err := os.ReadDir(hlsDir)
	if err != nil {
		return "", "", fmt.Errorf("failed to read HLS directory: %v", err)
	}

	var playlistURL string
	hlsBaseKey := fmt.Sprintf("videos/hls/%s", videoID)

	// Upload all HLS files
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		fileName := file.Name()
		filePath := fmt.Sprintf("%s/%s", hlsDir, fileName)
		s3Key := fmt.Sprintf("%s/%s", hlsBaseKey, fileName)

		fileURL, err := uploadFileToS3(filePath, s3Key)
		if err != nil {
			return "", "", fmt.Errorf("failed to upload HLS file %s: %v", fileName, err)
		}

		// Store the playlist URL
		if fileName == "playlist.m3u8" {
			playlistURL = fileURL
		}

		log.Printf("Uploaded HLS file: %s -> %s", fileName, fileURL)
	}

	if playlistURL == "" {
		return "", "", fmt.Errorf("playlist.m3u8 not found in HLS output")
	}

	// Upload thumbnail
	var thumbnailURL string
	if thumbnailPath != "" {
		thumbnailKey := fmt.Sprintf("thumbnails/%s.jpg", videoID)
		thumbnailURL, err = uploadFileToS3(thumbnailPath, thumbnailKey)
		if err != nil {
			return "", "", fmt.Errorf("failed to upload thumbnail: %v", err)
		}
	}

	log.Printf("HLS upload completed for video %s. Playlist URL: %s", videoID, playlistURL)
	return playlistURL, thumbnailURL, nil
}

func uploadFileToS3(filePath, key string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	_, err = s3Client.PutObject(&s3.PutObjectInput{
		Bucket: aws.String(s3Bucket),
		Key:    aws.String(key),
		Body:   file,
		// Removed ACL as the bucket doesn't support ACLs
	})

	if err != nil {
		return "", err
	}

	// Return the public URL
	return fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", s3Bucket, "us-west-2", key), nil
}

func getCategoryIDByName(categoryName string) int {
	var categoryID int
	err := db.QueryRow("SELECT id FROM categories WHERE name = ?", categoryName).Scan(&categoryID)
	if err != nil {
		return 1 // Default category
	}
	return categoryID
}

// Delete user's own video
func deleteUserVideo(c *gin.Context) {
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database not available"})
		return
	}

	videoID := c.Param("id")

	// Get current user
	user := getCurrentUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	log.Printf("🗑️ User %s attempting to delete video ID: %s", user.Username, videoID)

	// First, verify the video exists and belongs to the user
	var creatorID string
	var thumbnailURL, videoURL sql.NullString
	var videoType string

	// Check in videos table (user uploads)
	err := db.QueryRow("SELECT creator_id, thumbnail_url, video_url FROM videos WHERE video_id = ?", videoID).Scan(&creatorID, &thumbnailURL, &videoURL)
	if err == nil {
		videoType = "upload"
	} else if err == sql.ErrNoRows {
		// Check in vods table (stream recordings)
		err = db.QueryRow("SELECT streamer_id, thumbnail_url, video_url FROM vods WHERE vod_id = ?", videoID).Scan(&creatorID, &thumbnailURL, &videoURL)
		if err == nil {
			videoType = "stream"
		} else if err == sql.ErrNoRows {
			log.Printf("❌ Video not found: %s", videoID)
			c.JSON(http.StatusNotFound, gin.H{"error": "Video not found"})
			return
		} else {
			log.Printf("Error fetching video details: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch video details"})
			return
		}
	} else {
		log.Printf("Error fetching video details: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch video details"})
		return
	}

	// Verify ownership
	if creatorID != user.ID {
		log.Printf("❌ User %s attempted to delete video %s owned by %s", user.Username, videoID, creatorID)
		c.JSON(http.StatusForbidden, gin.H{"error": "You can only delete your own videos"})
		return
	}

	// Delete the video record from the appropriate table
	var result sql.Result
	if videoType == "upload" {
		result, err = db.Exec("DELETE FROM videos WHERE video_id = ?", videoID)
		log.Printf("🗑️ Deleting user upload from videos table: %s", videoID)
	} else {
		result, err = db.Exec("DELETE FROM vods WHERE vod_id = ?", videoID)
		log.Printf("🗑️ Deleting user stream VOD from vods table: %s", videoID)
	}

	if err != nil {
		log.Printf("Error deleting video %s: %v", videoID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete video"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		log.Printf("❌ No rows affected when deleting video: %s", videoID)
		c.JSON(http.StatusNotFound, gin.H{"error": "Video not found"})
		return
	}

	// TODO: Optionally delete files from S3
	// This could be implemented later for complete cleanup
	if s3Client != nil {
		// Clean up thumbnail from S3 if exists
		if thumbnailURL.Valid && thumbnailURL.String != "" {
			// Extract S3 key from URL and delete
			// Implementation can be added later
		}
		// Clean up video file from S3 if exists
		if videoURL.Valid && videoURL.String != "" {
			// Extract S3 key from URL and delete
			// Implementation can be added later
		}
	}

	log.Printf("✅ User %s successfully deleted %s video ID: %s", user.Username, videoType, videoID)
	c.JSON(http.StatusOK, gin.H{
		"message": "Video deleted successfully",
	})
}

// Get video processing status
func getVideoStatus(c *gin.Context) {
	videoID := c.Param("id")

	var video Video
	err := db.QueryRow(`
		SELECT video_id, title, status, processing_stage, created_at, updated_at
		FROM videos WHERE video_id = ?`, videoID).Scan(
		&video.VideoID, &video.Title, &video.Status, &video.ProcessingStage,
		&video.CreatedAt, &video.UpdatedAt)

	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Video not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"video_id": video.VideoID,
		"title":    video.Title,
		"status":   video.Status,
		"stage":    video.ProcessingStage,
		"created_at": video.CreatedAt,
		"updated_at": video.UpdatedAt,
	})
}

// Video player page
func videoPlayerPage(c *gin.Context) {
	videoID := c.Param("id")
	log.Printf("🎥 Video player requested for ID: %s", videoID)

	// Get video details
	var video Video
	err := db.QueryRow(`
		SELECT v.video_id, v.title, v.description, v.creator_id, v.creator_name,
			   v.category_name, v.duration, v.views, v.video_url, v.thumbnail_url,
			   v.status, v.created_at
		FROM videos v WHERE v.video_id = ? AND v.status = 'completed'`, videoID).Scan(
		&video.VideoID, &video.Title, &video.Description, &video.CreatorID, &video.CreatorName,
		&video.CategoryName, &video.Duration, &video.Views, &video.VideoURL, &video.ThumbnailURL,
		&video.Status, &video.CreatedAt)

	if err != nil {
		log.Printf("❌ Video not found or error: %v", err)
		// Try to find any video with this ID regardless of status
		var statusCheck string
		checkErr := db.QueryRow("SELECT status FROM videos WHERE video_id = ?", videoID).Scan(&statusCheck)
		if checkErr == nil {
			log.Printf("📋 Video exists but status is: %s", statusCheck)
		} else {
			log.Printf("📋 Video does not exist in database")
		}

		c.HTML(http.StatusNotFound, "404.html", gin.H{
			"error": "Video not found or still processing",
		})
		return
	}

	log.Printf("✅ Video found: %s, URL: %s", video.Title, video.VideoURL)

	// Increment view count
	db.Exec("UPDATE videos SET views = views + 1 WHERE video_id = ?", videoID)
	video.Views++

	// Get user info from query params
	userID := c.Query("user_id")
	username := c.Query("username")

	// For now, allow viewing without authentication but limit functionality
	// In production, you might want to require authentication for all video access
	if userID == "" || username == "" {
		// Allow viewing but disable interactive features
		userID = ""
		username = ""
	}

	// Check if user is the creator
	isCreator := (userID == video.CreatorID)
	isAuthenticated := (userID != "")

	// Decide how to serve the video URL (similar to VOD logic)
	var videoURL string
	if strings.Contains(strings.ToLower(video.VideoURL), ".m3u8") {
		// Use our proxy manifest for HLS videos
		videoURL = fmt.Sprintf("/api/video/hls/%s/manifest", videoID)
	} else if strings.Contains(video.VideoURL, "s3.us-west-2.amazonaws.com") {
		// For non-HLS assets stored privately on S3, use a pre-signed URL
		s3Key := strings.Split(video.VideoURL, ".amazonaws.com/")[1]
		presignedURL, err := generatePresignedURL(s3Key, 2*time.Hour)
		if err != nil {
			log.Printf("Error generating pre-signed URL for video %s: %v", videoID, err)
			videoURL = video.VideoURL // Fallback to original URL
		} else {
			videoURL = presignedURL
			log.Printf("🔐 Generated pre-signed URL for video: %s", videoID)
		}
	} else {
		videoURL = video.VideoURL // Use original URL for non-S3 videos
	}

	log.Printf("🎥 Video Player - VideoURL: %s, UserID: %s, IsAuthenticated: %t", videoURL, userID, isAuthenticated)

	c.HTML(http.StatusOK, "video-player.html", gin.H{
		"video": gin.H{
			"VideoID":        video.VideoID,
			"Title":          video.Title,
			"Description":    video.Description,
			"CreatorID":      video.CreatorID,
			"CreatorName":    video.CreatorName,
			"CreatorAvatar":  "/static/images/default-avatar.png", // TODO: Get actual creator avatar
			"CategoryName":   video.CategoryName,
			"Duration":       video.Duration,
			"Views":          video.Views,
			"VideoURL":       videoURL,
			"ThumbnailURL":   video.ThumbnailURL,
			"CreatedAt":      video.CreatedAt,
		},
		"userID":         userID,
		"username":       username,
		"isCreator":      isCreator,
		"isAuthenticated": isAuthenticated,
		"siteName":       siteName,
		"title":          video.Title + " - Video Player",
	})
}

// Get video comments
func getVideoComments(c *gin.Context) {
	videoID := c.Param("videoId")

	rows, err := db.Query(`
		SELECT id, video_id, user_id, username, comment, created_at
		FROM video_comments
		WHERE video_id = ?
		ORDER BY created_at DESC`, videoID)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch comments"})
		return
	}
	defer rows.Close()

	var comments []VideoComment
	for rows.Next() {
		var comment VideoComment
		err := rows.Scan(&comment.ID, &comment.VideoID, &comment.UserID,
			&comment.Username, &comment.Comment, &comment.CreatedAt)
		if err != nil {
			continue
		}
		comments = append(comments, comment)
	}

	c.JSON(http.StatusOK, comments)
}

// Add video comment
func addVideoComment(c *gin.Context) {
	videoID := c.Param("videoId")

	var req struct {
		Comment string `json:"comment" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Comment is required"})
		return
	}

	// Get user info from headers
	userID := c.GetHeader("X-User-ID")
	username := c.GetHeader("X-Username")

	if userID == "" || username == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User authentication required"})
		return
	}

	// Insert comment
	result, err := db.Exec(`
		INSERT INTO video_comments (video_id, user_id, username, comment, created_at)
		VALUES (?, ?, ?, ?, ?)`,
		videoID, userID, username, req.Comment, time.Now())

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to add comment"})
		return
	}

	commentID, _ := result.LastInsertId()

	c.JSON(http.StatusOK, gin.H{
		"id":         commentID,
		"video_id":   videoID,
		"user_id":    userID,
		"username":   username,
		"comment":    req.Comment,
		"created_at": time.Now(),
	})
}

// Chat API handlers
func getChatMessages(c *gin.Context) {
	streamID := c.Param("streamId")

	rows, err := db.Query(`
		SELECT id, user_id, username, message, created_at
		FROM chat_messages
		WHERE stream_id = ?
		ORDER BY created_at DESC
		LIMIT 50
	`, streamID)
	if err != nil {
		log.Printf("Error fetching chat messages: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch messages"})
		return
	}
	defer rows.Close()

	var messages []gin.H
	for rows.Next() {
		var msg struct {
			ID        int       `json:"id"`
			UserID    *string   `json:"user_id"`
			Username  string    `json:"username"`
			Message   string    `json:"message"`
			CreatedAt time.Time `json:"created_at"`
		}

		err := rows.Scan(&msg.ID, &msg.UserID, &msg.Username, &msg.Message, &msg.CreatedAt)
		if err != nil {
			log.Printf("Error scanning message row: %v", err)
			continue
		}

		messages = append(messages, gin.H{
			"id":         msg.ID,
			"user_id":    msg.UserID,
			"username":   msg.Username,
			"message":    msg.Message,
			"created_at": msg.CreatedAt,
		})
	}

	// Reverse to show oldest first
	for i, j := 0, len(messages)-1; i < j; i, j = i+1, j-1 {
		messages[i], messages[j] = messages[j], messages[i]
	}

	c.JSON(http.StatusOK, messages)
}

func sendChatMessage(c *gin.Context) {
	streamID := c.Param("streamId")

	type ChatMessageRequest struct {
		Message string `json:"message" binding:"required"`
	}

	var req ChatMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user info from token (mock for now)
	userID := "user_123"
	username := "TestUser"

	// Save message to database
	result, err := db.Exec(`
		INSERT INTO chat_messages (stream_id, user_id, username, message)
		VALUES (?, ?, ?, ?)
	`, streamID, userID, username, req.Message)

	if err != nil {
		log.Printf("Error saving chat message: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send message"})
		return
	}

	messageID, _ := result.LastInsertId()

	// Return the message
	message := gin.H{
		"id":         messageID,
		"user_id":    userID,
		"username":   username,
		"message":    req.Message,
		"created_at": time.Now(),
	}

	c.JSON(http.StatusOK, message)
}

// GetChatConfig returns WebSocket configuration for the frontend
func getChatConfig(c *gin.Context) {
	awsWebSocketURL := os.Getenv("AWS_WEBSOCKET_API_URL")
	awsWebSocketStage := os.Getenv("AWS_WEBSOCKET_STAGE")
	if awsWebSocketStage == "" {
		awsWebSocketStage = "production"
	}

	// Determine if we should use AWS WebSocket or local WebSocket
	useAWSWebSocket := awsWebSocketURL != "" && awsWebSocketURL != "wss://your-api-id.execute-api.us-west-2.amazonaws.com/production"

	c.JSON(http.StatusOK, gin.H{
		"useAWSWebSocket":   useAWSWebSocket,
		"awsWebSocketURL":   awsWebSocketURL,
		"awsWebSocketStage": awsWebSocketStage,
		"localWebSocketURL": fmt.Sprintf("%s://%s",
			func() string {
				if c.Request.TLS != nil {
					return "wss"
				}
				return "ws"
			}(), c.Request.Host),
	})
}

// WebSocket handler for real-time chat
func handleWebSocket(c *gin.Context) {
	streamID := c.Param("streamId")

	// Get user info (mock for now - in production, get from JWT token)
	userID := c.Query("user_id")
	username := c.Query("username")
	if userID == "" {
		userID = "anonymous_" + fmt.Sprintf("%d", time.Now().Unix())
	}
	if username == "" {
		username = "Anonymous"
	}

	// Upgrade HTTP connection to WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket upgrade error: %v", err)
		return
	}

	// Get or create chat room
	room, exists := chatRooms[streamID]
	if !exists {
		room = newChatRoom(streamID)
		chatRooms[streamID] = room
		go room.run()
		log.Printf("Created new chat room for stream: %s", streamID)
	}

	// Create new client
	client := &ChatClient{
		conn:     conn,
		send:     make(chan ChatMessage, 256),
		room:     room,
		userID:   userID,
		username: username,
	}

	// Register client to room
	room.register <- client

	// Start client goroutines
	go client.writePump()
	go client.readPump()
}

// AWS WebSocket handler for real-time chat (similar to AWS Lambda HandleRequest)
func handleAWSWebSocket(c *gin.Context) {
	streamID := c.Param("streamId")

	// Get user info (mock for now - in production, get from JWT token)
	userID := c.Query("user_id")
	username := c.Query("username")
	if userID == "" {
		userID = "anonymous_" + fmt.Sprintf("%d", time.Now().Unix())
	}
	if username == "" {
		username = "StreamViewer"
	}

	log.Printf("AWS WebSocket connection request for stream %s, user: %s", streamID, username)

	// Upgrade HTTP connection to WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("AWS WebSocket upgrade error: %v", err)
		return
	}

	// Get or create chat room
	room, exists := chatRooms[streamID]
	if !exists {
		room = newChatRoom(streamID)
		chatRooms[streamID] = room
		go room.run()
		log.Printf("Created new AWS chat room for stream: %s", streamID)
	}

	// Create client with AWS WebSocket format
	client := &ChatClient{
		conn:     conn,
		send:     make(chan ChatMessage, 256),
		room:     room,
		userID:   userID,
		username: username,
	}

	// Register client and start AWS-style goroutines
	client.room.register <- client

	log.Printf("Starting AWS WebSocket pumps for user %s in stream %s", username, streamID)

	// Use the AWS-style read/write pumps
	go client.writeAWSPump()
	go client.readAWSPump()

	log.Printf("AWS WebSocket pumps started for user %s", username)

	log.Printf("AWS WebSocket client connected to room %s. Total clients: %d", streamID, len(room.clients))
}

// Initialize configuration from environment variables
func initConfig() {
	siteName = os.Getenv("SITE_NAME")
	if siteName == "" {
		siteName = "MoneyBags"
	}

	userPoolID = os.Getenv("AWS_COGNITO_USER_POOL_ID")
	clientID = os.Getenv("AWS_COGNITO_CLIENT_ID")
	clientSecret = os.Getenv("AWS_COGNITO_CLIENT_SECRET")

	// Parse social login toggles
	enableGoogleLogin = os.Getenv("ENABLE_GOOGLE_LOGIN") == "true"
	enableFacebookLogin = os.Getenv("ENABLE_FACEBOOK_LOGIN") == "true"
	enableAppleLogin = os.Getenv("ENABLE_APPLE_LOGIN") == "true"

	// Database configuration
	dbHost = os.Getenv("DB_HOST")
	dbPort = os.Getenv("DB_PORT")
	if dbPort == "" {
		dbPort = "3306"
	}
	dbUser = os.Getenv("DB_USER")
	dbPassword = os.Getenv("DB_PASSWORD")
	dbName = os.Getenv("DB_NAME")

	// S3 configuration
	s3Bucket = os.Getenv("S3_BUCKET")
	s3Region = os.Getenv("S3_REGION")
	if s3Region == "" {
		s3Region = "us-east-1"
	}
}

// Initialize AWS Cognito client
func initCognito() {
	region := os.Getenv("AWS_REGION")
	if region == "" {
		region = "us-east-1"
	}

	// Check if AWS credentials are provided
	accessKey := os.Getenv("AWS_ACCESS_KEY_ID")
	secretKey := os.Getenv("AWS_SECRET_ACCESS_KEY")

	if accessKey == "" || secretKey == "" {
		log.Println("WARNING: AWS credentials not provided. Cognito features will not work.")
		log.Println("Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in your .env file")
		log.Println("Admin interface will show mock user data instead of real Cognito users")
		return
	}

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(region),
	})
	if err != nil {
		log.Printf("Failed to create AWS session: %v", err)
		return
	}

	cognitoClient = cognitoidentityprovider.New(sess)
	log.Printf("AWS Cognito client initialized for region: %s", region)

	// Initialize IVS client
	ivsClient = ivs.New(sess)
	log.Printf("AWS IVS client initialized for region: %s", region)
}

// Initialize S3 client
func initS3() {
	if s3Bucket == "" {
		log.Println("WARNING: S3 bucket not configured. File upload features will not work.")
		log.Println("Please set S3_BUCKET in your .env file to enable file uploads")
		return
	}

	region := s3Region
	if region == "" {
		region = "us-east-1"
	}

	// Check if AWS credentials are provided
	accessKey := os.Getenv("AWS_ACCESS_KEY_ID")
	secretKey := os.Getenv("AWS_SECRET_ACCESS_KEY")

	if accessKey == "" || secretKey == "" {
		log.Println("WARNING: AWS credentials not provided. S3 file upload will not work.")
		log.Println("Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in your .env file")
		return
	}

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(region),
	})
	if err != nil {
		log.Printf("Failed to create AWS session for S3: %v", err)
		return
	}

	s3Client = s3.New(sess)
	log.Printf("AWS S3 client initialized for region: %s, bucket: %s", region, s3Bucket)
}

// Initialize database connection
func initDatabase() {
	if dbHost == "" || dbUser == "" || dbPassword == "" || dbName == "" {
		log.Println("Database configuration not provided, skipping database initialization")
		return
	}

	log.Printf("Attempting to connect to database: %s:%s/%s", dbHost, dbPort, dbName)
	log.Printf("Database user: %s", dbUser)

	// Test basic network connectivity first
	log.Printf("Testing network connectivity to %s:%s...", dbHost, dbPort)
	conn, netErr := net.DialTimeout("tcp", dbHost+":"+dbPort, 10*time.Second)
	if netErr != nil {
		log.Printf("❌ Network connectivity test failed: %v", netErr)
		log.Println("This suggests a network/firewall issue, not a database authentication problem")
		log.Println("Please check:")
		log.Println("1. RDS Security Group allows inbound port 3306")
		log.Println("2. RDS is publicly accessible")
		log.Println("3. RDS is in a public subnet with Internet Gateway")
		log.Println("4. Network ACLs allow port 3306")
		return
	}
	conn.Close()
	log.Printf("✅ Network connectivity test successful!")

	// Try different connection approaches
	approaches := []struct {
		name string
		dsn  string
	}{
		{
			"Standard connection",
			dbUser + ":" + dbPassword + "@tcp(" + dbHost + ":" + dbPort + ")/" + dbName + "?charset=utf8mb4&parseTime=True&loc=Local&timeout=30s",
		},
		{
			"Connection with SSL disabled",
			dbUser + ":" + dbPassword + "@tcp(" + dbHost + ":" + dbPort + ")/" + dbName + "?charset=utf8mb4&parseTime=True&loc=Local&timeout=30s&tls=false",
		},
		{
			"Connection with SSL skip-verify",
			dbUser + ":" + dbPassword + "@tcp(" + dbHost + ":" + dbPort + ")/" + dbName + "?charset=utf8mb4&parseTime=True&loc=Local&timeout=30s&tls=skip-verify",
		},
		{
			"Connection without database (to create it)",
			dbUser + ":" + dbPassword + "@tcp(" + dbHost + ":" + dbPort + ")/?charset=utf8mb4&parseTime=True&loc=Local&timeout=30s",
		},
	}

	var err error
	for _, approach := range approaches {
		log.Printf("Trying %s...", approach.name)

		db, err = sql.Open("mysql", approach.dsn)
		if err != nil {
			log.Printf("Failed to create connection with %s: %v", approach.name, err)
			continue
		}

		// Test the connection
		log.Printf("Testing connection with %s...", approach.name)
		err = db.Ping()
		if err != nil {
			log.Printf("Failed to ping with %s: %v", approach.name, err)
			db.Close()
			db = nil
			continue
		}

		log.Printf("✅ Successfully connected with %s!", approach.name)

		// If we connected without specifying a database, create it and reconnect
		if approach.name == "Connection without database (to create it)" {
			log.Printf("Creating database '%s'...", dbName)
			_, err = db.Exec("CREATE DATABASE IF NOT EXISTS " + dbName)
			if err != nil {
				log.Printf("Failed to create database: %v", err)
				db.Close()
				db = nil
				continue
			}

			log.Printf("✅ Database '%s' created!", dbName)

			// Close the connection without database
			db.Close()

			// Reconnect with the database specified
			log.Printf("Reconnecting with database specified...")
			dsn := dbUser + ":" + dbPassword + "@tcp(" + dbHost + ":" + dbPort + ")/" + dbName + "?charset=utf8mb4&parseTime=True&loc=Local&timeout=30s"
			db, err = sql.Open("mysql", dsn)
			if err != nil {
				log.Printf("Failed to reconnect with database: %v", err)
				db = nil
				continue
			}

			// Test the new connection
			err = db.Ping()
			if err != nil {
				log.Printf("Failed to ping after reconnection: %v", err)
				db.Close()
				db = nil
				continue
			}

			log.Printf("✅ Successfully reconnected with database '%s'!", dbName)
		}

		break
	}

	if db == nil {
		log.Println("❌ All connection attempts failed - continuing with mock data")
		return
	}

	// Set connection pool settings
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(25)
	db.SetConnMaxLifetime(5 * time.Minute)

	log.Printf("Database connected successfully to %s:%s/%s", dbHost, dbPort, dbName)

	// Initialize database schema
	initDatabaseSchema()
}

// Initialize database schema
func initDatabaseSchema() {
	// Create categories table
	categoriesTable := `
	CREATE TABLE IF NOT EXISTS categories (
		id INT AUTO_INCREMENT PRIMARY KEY,
		name VARCHAR(255) NOT NULL UNIQUE,
		description TEXT,
		photo_url TEXT,
		is_enabled BOOLEAN DEFAULT TRUE,
		viewers INT DEFAULT 0,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
	)`

	// Create users table
	usersTable := `
	CREATE TABLE IF NOT EXISTS users (
		id VARCHAR(255) PRIMARY KEY,
		username VARCHAR(255) NOT NULL UNIQUE,
		email VARCHAR(255) NOT NULL UNIQUE,
		display_name VARCHAR(255),
		avatar_url TEXT,
		banner_url TEXT,
		bio TEXT,
		status ENUM('active', 'banned', 'suspended') DEFAULT 'active',
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
	)`

	// Create streams table
	streamsTable := `
	CREATE TABLE IF NOT EXISTS streams (
		id VARCHAR(100) PRIMARY KEY,
		title VARCHAR(255) NOT NULL,
		description TEXT,
		streamer_id VARCHAR(255) NOT NULL,
		streamer_name VARCHAR(255) NOT NULL,
		category_id INT,
		category_name VARCHAR(255),
		viewers INT DEFAULT 0,
		is_live BOOLEAN DEFAULT TRUE,
		stream_key VARCHAR(255) UNIQUE,
		playback_url VARCHAR(500),
		ingest_endpoint VARCHAR(500),
		ivs_channel_arn VARCHAR(500),
		thumbnail_url TEXT,
		started_at TIMESTAMP NULL,
		ended_at TIMESTAMP NULL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
	)`

	// Create chat_messages table
	chatMessagesTable := `
	CREATE TABLE IF NOT EXISTS chat_messages (
		id INT AUTO_INCREMENT PRIMARY KEY,
		stream_id VARCHAR(100) NOT NULL,
		user_id VARCHAR(255),
		username VARCHAR(100) NOT NULL,
		message TEXT NOT NULL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (stream_id) REFERENCES streams(id) ON DELETE CASCADE
	)`

	// Create videos table
	videosTable := `
	CREATE TABLE IF NOT EXISTS videos (
		id INT AUTO_INCREMENT PRIMARY KEY,
		video_id VARCHAR(255) UNIQUE NOT NULL,
		title VARCHAR(255) NOT NULL,
		description TEXT,
		creator_id VARCHAR(255) NOT NULL,
		creator_name VARCHAR(255) NOT NULL,
		category_id INT,
		category_name VARCHAR(255),
		duration INT DEFAULT 0,
		views INT DEFAULT 0,
		video_url TEXT,
		thumbnail_url TEXT,
		status ENUM('processing', 'completed', 'failed') DEFAULT 'processing',
		processing_stage VARCHAR(100) DEFAULT 'upload',
		original_filename VARCHAR(255),
		file_size BIGINT DEFAULT 0,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		INDEX idx_video_id (video_id),
		INDEX idx_creator_id (creator_id),
		INDEX idx_status (status),
		FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
	)`

	// Create VODs table for recorded streams
	vodsTable := `
	CREATE TABLE IF NOT EXISTS vods (
		id INT AUTO_INCREMENT PRIMARY KEY,
		vod_id VARCHAR(255) UNIQUE NOT NULL,
		stream_id VARCHAR(255) NOT NULL,
		title VARCHAR(255) NOT NULL,
		description TEXT,
		streamer_id VARCHAR(255) NOT NULL,
		streamer_name VARCHAR(255) NOT NULL,
		category_id INT,
		category_name VARCHAR(255),
		duration INT DEFAULT 0,
		views INT DEFAULT 0,
		video_url TEXT,
		thumbnail_url TEXT,
		s3_recording_key VARCHAR(500),
		recording_status ENUM('recording', 'processing', 'completed', 'failed') DEFAULT 'recording',
		stream_started_at TIMESTAMP NULL,
		stream_ended_at TIMESTAMP NULL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		INDEX idx_vod_id (vod_id),
		INDEX idx_stream_id (stream_id),
		INDEX idx_streamer_id (streamer_id),
		INDEX idx_category_id (category_id),
		INDEX idx_recording_status (recording_status),
		FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
		FOREIGN KEY (stream_id) REFERENCES streams(id) ON DELETE CASCADE
	)`

	// Create video comments table
	videoCommentsTable := `
	CREATE TABLE IF NOT EXISTS video_comments (
		id INT AUTO_INCREMENT PRIMARY KEY,
		video_id VARCHAR(255) NOT NULL,
		user_id VARCHAR(255) NOT NULL,
		username VARCHAR(255) NOT NULL,
		comment TEXT NOT NULL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		INDEX idx_video_id (video_id),
		INDEX idx_user_id (user_id),
		FOREIGN KEY (video_id) REFERENCES videos(video_id) ON DELETE CASCADE
	)`

	// Create user follows table
	userFollowsTable := `
	CREATE TABLE IF NOT EXISTS user_follows (
		id INT AUTO_INCREMENT PRIMARY KEY,
		follower_id VARCHAR(255) NOT NULL,
		following_id VARCHAR(255) NOT NULL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		UNIQUE KEY unique_follow (follower_id, following_id),
		INDEX idx_follower (follower_id),
		INDEX idx_following (following_id),
		FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
		FOREIGN KEY (following_id) REFERENCES users(id) ON DELETE CASCADE
	)`

	// Create video likes table (supports both videos and vods)
	videoLikesTable := `
	CREATE TABLE IF NOT EXISTS video_likes (
		id INT AUTO_INCREMENT PRIMARY KEY,
		video_id VARCHAR(255) NOT NULL,
		user_id VARCHAR(255) NOT NULL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		UNIQUE KEY unique_like (video_id, user_id),
		INDEX idx_video_id (video_id),
		INDEX idx_user_id (user_id),
		FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
	)`

	// Create user channels table for channel reuse
	userChannelsTable := `
	CREATE TABLE IF NOT EXISTS user_channels (
		id INT AUTO_INCREMENT PRIMARY KEY,
		user_id VARCHAR(255) NOT NULL,
		username VARCHAR(255) NOT NULL,
		channel_arn VARCHAR(500) NOT NULL,
		playback_url VARCHAR(500) NOT NULL,
		ingest_endpoint VARCHAR(500) NOT NULL,
		stream_key VARCHAR(255) NOT NULL,
		is_active BOOLEAN DEFAULT TRUE,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		UNIQUE KEY unique_user_channel (user_id),
		INDEX idx_user_id (user_id),
		INDEX idx_channel_arn (channel_arn),
		INDEX idx_is_active (is_active)
	)`

	// First, drop and recreate tables to fix schema issues
	log.Println("Migrating database schema...")

	// Drop tables in correct order (child tables first)
	_, err := db.Exec("DROP TABLE IF EXISTS video_likes")
	if err != nil {
		log.Printf("Error dropping video_likes table: %v", err)
	}

	_, err = db.Exec("DROP TABLE IF EXISTS video_comments")
	if err != nil {
		log.Printf("Error dropping video_comments table: %v", err)
	}

	_, err = db.Exec("DROP TABLE IF EXISTS chat_messages")
	if err != nil {
		log.Printf("Error dropping chat_messages table: %v", err)
	}

	_, err = db.Exec("DROP TABLE IF EXISTS videos")
	if err != nil {
		log.Printf("Error dropping videos table: %v", err)
	}

	_, err = db.Exec("DROP TABLE IF EXISTS streams")
	if err != nil {
		log.Printf("Error dropping streams table: %v", err)
	}

	tables := []string{categoriesTable, usersTable, streamsTable, chatMessagesTable, videosTable, vodsTable, videoCommentsTable, userFollowsTable, videoLikesTable, userChannelsTable}

	for _, table := range tables {
		_, err := db.Exec(table)
		if err != nil {
			log.Printf("Error creating table: %v", err)
		}
	}


		// Ensure profile columns exist even if users table already existed
		ensureUserProfileColumns()

	log.Println("Database schema initialized")

	// Insert default categories if none exist
	insertDefaultCategories()
}

// Insert default categories
func insertDefaultCategories() {
	if db == nil {
		return
	}

	// Check if categories exist and have the correct gambling categories
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM categories WHERE name IN ('Blackjack', 'Baccarat', 'Slots', 'Roulette', 'Poker Cash Game', 'Poker Tournaments', 'Sports Betting', 'Political Betting', 'Predictions', 'Crypto')").Scan(&count)
	if err != nil {
		log.Printf("Error checking categories count: %v", err)
		return
	}

	if count >= 10 {
		log.Println("Gambling categories already exist, skipping initialization")
		return // Categories already exist
	}

	// Clear existing categories table to start fresh
	log.Println("Clearing categories table and inserting new gambling/betting categories...")

	// Disable foreign key checks temporarily
	_, err = db.Exec("SET FOREIGN_KEY_CHECKS = 0")
	if err != nil {
		log.Printf("Error disabling foreign key checks: %v", err)
	}

	// Delete all existing categories
	_, err = db.Exec("DELETE FROM categories")
	if err != nil {
		log.Printf("Error clearing categories table: %v", err)
		return
	}

	// Reset auto increment
	_, err = db.Exec("ALTER TABLE categories AUTO_INCREMENT = 1")
	if err != nil {
		log.Printf("Error resetting auto increment: %v", err)
	}

	// Re-enable foreign key checks
	_, err = db.Exec("SET FOREIGN_KEY_CHECKS = 1")
	if err != nil {
		log.Printf("Error re-enabling foreign key checks: %v", err)
	}

	log.Println("Categories table cleared successfully")

	// Insert new gambling/betting categories
	defaultCategories := []struct {
		name        string
		description string
		viewers     int
	}{
		{"Blackjack", "Live blackjack games and strategy discussions", 85000},
		{"Baccarat", "Baccarat gameplay and betting strategies", 72000},
		{"Slots", "Slot machine games and jackpot streams", 95000},
		{"Roulette", "Live roulette spins and betting systems", 68000},
		{"Poker Cash Game", "Live poker cash games and analysis", 110000},
		{"Poker Tournaments", "Tournament poker and championship events", 125000},
		{"Sports Betting", "Sports betting tips and live bet analysis", 150000},
		{"Political Betting", "Political prediction markets and election betting", 45000},
		{"Predictions", "General prediction markets and forecasting", 38000},
		{"Crypto", "Cryptocurrency trading and market analysis", 92000},
	}

	for _, cat := range defaultCategories {
		_, err := db.Exec("INSERT INTO categories (name, description, viewers, is_enabled) VALUES (?, ?, ?, TRUE)",
			cat.name, cat.description, cat.viewers)
		if err != nil {
			log.Printf("Error inserting category %s: %v", cat.name, err)
		} else {
			log.Printf("✅ Inserted category: %s", cat.name)
		}
	}

	log.Println("New gambling/betting categories inserted successfully")
}

// Calculate secret hash for Cognito (required when client secret is set)
func calculateSecretHash(username string) *string {
	if clientSecret == "" {
		return nil
	}

	message := username + clientID
	key := []byte(clientSecret)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(message))
	hash := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return &hash
}

// Handle Cognito errors and return user-friendly messages
func handleCognitoError(err error) (int, string) {
	if awsErr, ok := err.(awserr.Error); ok {
		switch awsErr.Code() {
		case "NotAuthorizedException":
			return http.StatusUnauthorized, "Invalid username or password"
		case "UserNotFoundException":
			return http.StatusUnauthorized, "User not found"
		case "UserNotConfirmedException":
			return http.StatusUnauthorized, "Please verify your email address"
		case "UsernameExistsException":
			return http.StatusConflict, "Username already exists"
		case "InvalidPasswordException":
			return http.StatusBadRequest, "Password does not meet requirements"
		case "InvalidParameterException":
			return http.StatusBadRequest, "Invalid input parameters"
		case "TooManyRequestsException":
			return http.StatusTooManyRequests, "Too many requests. Please try again later"
		case "LimitExceededException":
			return http.StatusTooManyRequests, "Rate limit exceeded. Please try again later"
		case "CodeMismatchException":
			return http.StatusBadRequest, "Invalid verification code"
		case "ExpiredCodeException":
			return http.StatusBadRequest, "Verification code has expired"
		case "AliasExistsException":
			return http.StatusConflict, "Email address already in use"
		default:
			log.Printf("Unhandled Cognito error: %s - %s", awsErr.Code(), awsErr.Message())
			return http.StatusInternalServerError, "Authentication service error"
		}
	}
	return http.StatusInternalServerError, "Internal server error"
}

// JWT Claims structure
type JWTClaims struct {
	Username string `json:"username"`
	UserID   string `json:"user_id"`
	jwt.RegisteredClaims
}

// Generate JWT token for authenticated user
func generateJWTToken(username string) (string, error) {
	// Set token expiration time (7 days)
	expirationTime := time.Now().Add(7 * 24 * time.Hour)

	// Create claims
	claims := &JWTClaims{
		Username: username,
		UserID:   username, // For now, using username as user_id
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "moneybags-app",
		},
	}

	// Create token with claims
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token with secret
	tokenString, err := token.SignedString(jwtSecret)
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// Validate JWT token and extract user information
func validateJWTToken(tokenString string) (*JWTClaims, error) {
	// Parse token
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	// Extract claims
	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// Middleware to authenticate requests using JWT tokens from cookies
func authenticateJWT() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from cookie
		tokenString, err := c.Cookie("auth_token")
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
			c.Abort()
			return
		}

		// Validate token
		claims, err := validateJWTToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
			c.Abort()
			return
		}

		// Get user information from database
		var userID, username, email string
		err = db.QueryRow("SELECT id, username, email FROM users WHERE username = ?", claims.Username).Scan(&userID, &username, &email)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
			} else {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
			}
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", userID)
		c.Set("username", username)
		c.Set("email", email)

		c.Next()
	}
}

// Placeholder handlers for API endpoints
func getCategories(c *gin.Context) {
	if db == nil {
		// Fallback to mock data if database is not available
		categories := []map[string]interface{}{
			{"id": 1, "name": "Gaming", "viewers": 125000, "photo_url": ""},
			{"id": 2, "name": "Just Chatting", "viewers": 98000, "photo_url": ""},
			{"id": 3, "name": "Music", "viewers": 45000, "photo_url": ""},
			{"id": 4, "name": "Art", "viewers": 23000, "photo_url": ""},
			{"id": 5, "name": "Sports", "viewers": 18000, "photo_url": ""},
		}
		c.JSON(http.StatusOK, categories)
		return
	}

	rows, err := db.Query(`
		SELECT c.id, c.name, c.viewers, COALESCE(c.photo_url, '') as photo_url,
		       COALESCE(COUNT(v.vod_id), 0) as vod_count
		FROM categories c
		LEFT JOIN vods v ON c.name = v.category_name AND v.recording_status = 'completed'
		WHERE c.is_enabled = TRUE
		GROUP BY c.id, c.name, c.viewers, c.photo_url
		ORDER BY vod_count DESC, c.viewers DESC
	`)
	if err != nil {
		log.Printf("Error querying categories: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch categories"})
		return
	}
	defer rows.Close()

	var categories []map[string]interface{}
	for rows.Next() {
		var id, viewers, vodCount int
		var name, photoURL string

		err := rows.Scan(&id, &name, &viewers, &photoURL, &vodCount)
		if err != nil {
			log.Printf("Error scanning category row: %v", err)
			continue
		}

		category := map[string]interface{}{
			"id":        id,
			"name":      name,
			"viewers":   viewers,
			"photo_url": photoURL,
			"vod_count": vodCount,
		}

		// Debug logging
		log.Printf("Category: %s, Photo URL: '%s', VOD Count: %d", name, photoURL, vodCount)

		categories = append(categories, category)
	}

	c.JSON(http.StatusOK, categories)
}

func getChannels(c *gin.Context) {
	if db == nil {
		// Fallback to mock data if database is not available
		channels := []map[string]interface{}{
			{"id": 1, "name": "StreamerOne", "title": "Epic Gaming Session!", "viewers": 1250, "category": "Gaming", "avatar": "/static/images/avatar1.jpg"},
			{"id": 2, "name": "ChatMaster", "title": "Morning Coffee Chat", "viewers": 890, "category": "Just Chatting", "avatar": "/static/images/avatar2.jpg"},
			{"id": 3, "name": "MusicLive", "title": "Live Piano Performance", "viewers": 567, "category": "Music", "avatar": "/static/images/avatar3.jpg"},
			{"id": 4, "name": "ArtCreator", "title": "Digital Art Tutorial", "viewers": 234, "category": "Art", "avatar": "/static/images/avatar4.jpg"},
		}
		c.JSON(http.StatusOK, channels)
		return
	}

	rows, err := db.Query(`
		SELECT s.id, s.streamer_name, s.title, s.viewers, s.category_name, u.avatar_url
		FROM streams s
		LEFT JOIN users u ON s.streamer_id = u.id
		WHERE s.is_live = TRUE
		ORDER BY s.viewers DESC
	`)
	if err != nil {
		log.Printf("Error querying streams: %v", err)
		// Return empty array instead of error to prevent frontend issues
		c.JSON(http.StatusOK, []map[string]interface{}{})
		return
	}
	defer rows.Close()

	var channels []map[string]interface{}
	for rows.Next() {
		var id string  // Changed from int to string
		var viewers int
		var name, title, category string
		var avatar sql.NullString

		err := rows.Scan(&id, &name, &title, &viewers, &category, &avatar)
		if err != nil {
			log.Printf("Error scanning stream row: %v", err)
			continue
		}

		avatarURL := "/static/images/default-avatar.png"
		if avatar.Valid && avatar.String != "" {
			avatarURL = avatar.String
		}

		channels = append(channels, map[string]interface{}{
			"id":       id,
			"name":     name,
			"title":    title,
			"viewers":  viewers,
			"category": category,
			"avatar":   avatarURL,
		})
	}

	c.JSON(http.StatusOK, channels)
}

func getRecommendedUsers(c *gin.Context) {
	if db == nil {
		// Fallback to mock data if database is not available
		users := []map[string]interface{}{
			{"id": "user1", "username": "StreamerPro", "avatar_url": "/static/images/default-avatar.png", "total_viewers": 2500, "is_live": true},
			{"id": "user2", "username": "GamerGirl", "avatar_url": "/static/images/default-avatar.png", "total_viewers": 1800, "is_live": true},
			{"id": "user3", "username": "ChatKing", "avatar_url": "/static/images/default-avatar.png", "total_viewers": 1200, "is_live": false},
			{"id": "user4", "username": "MusicMaster", "avatar_url": "/static/images/default-avatar.png", "total_viewers": 950, "is_live": false},
			{"id": "user5", "username": "ArtCreator", "avatar_url": "/static/images/default-avatar.png", "total_viewers": 750, "is_live": false},
			{"id": "user6", "username": "TechGuru", "avatar_url": "/static/images/default-avatar.png", "total_viewers": 600, "is_live": false},
		}
		c.JSON(http.StatusOK, users)
		return
	}

	// Get recommended users from database
	// Order by: live streaming users first, then by total viewers descending
	query := `
		SELECT
			u.id,
			u.username,
			COALESCE(u.avatar_url, '') as avatar_url,
			COALESCE(SUM(v.views), 0) as total_viewers,
			CASE WHEN s.id IS NOT NULL AND s.is_live = 1 THEN 1 ELSE 0 END as is_live,
			COALESCE(s.id, '') as stream_id
		FROM users u
		LEFT JOIN vods v ON u.username = v.streamer_name
		LEFT JOIN streams s ON u.username = s.streamer_name AND s.is_live = 1
		WHERE u.username != 'admin'
		GROUP BY u.id, u.username, u.avatar_url, s.id
		ORDER BY is_live DESC, total_viewers DESC
		LIMIT 12
	`

	rows, err := db.Query(query)
	if err != nil {
		log.Printf("Error fetching recommended users: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch recommended users"})
		return
	}
	defer rows.Close()

	var users []map[string]interface{}
	for rows.Next() {
		var userID, username, avatarURL, streamID string
		var totalViewers int
		var isLive bool

		err := rows.Scan(&userID, &username, &avatarURL, &totalViewers, &isLive, &streamID)
		if err != nil {
			log.Printf("Error scanning recommended user: %v", err)
			continue
		}

		// Use default avatar if none is set
		if avatarURL == "" {
			avatarURL = "/static/images/default-avatar.png"
		}

		user := map[string]interface{}{
			"id":            userID,
			"username":      username,
			"avatar_url":    avatarURL,
			"total_viewers": totalViewers,
			"is_live":       isLive,
		}

		// Add stream_id only if user is live
		if isLive && streamID != "" {
			user["stream_id"] = streamID
		}

		users = append(users, user)
	}

	c.JSON(http.StatusOK, users)
}

// Login request structure
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// Signup request structure
type SignupRequest struct {
	Username  string `json:"username" binding:"required"`
	Password  string `json:"password" binding:"required"`
	Email     string `json:"email" binding:"required"`
	BirthDate string `json:"birthDate"`
}

// Confirmation request structures
type ConfirmSignupRequest struct {
	Username string `json:"username" binding:"required"`
	Code     string `json:"code" binding:"required"`
}

type ResendCodeRequest struct {
	Username string `json:"username" binding:"required"`
}

// Social login request structure
type SocialLoginRequest struct {
	Provider     string `json:"provider" binding:"required"`
	AccessToken  string `json:"accessToken" binding:"required"`
	IdToken      string `json:"idToken,omitempty"`
}

func login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if cognitoClient == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Authentication service not available"})
		return
	}

	// Prepare auth parameters
	authParams := map[string]*string{
		"USERNAME": aws.String(req.Username),
		"PASSWORD": aws.String(req.Password),
	}

	// Add secret hash if client secret is configured
	if secretHash := calculateSecretHash(req.Username); secretHash != nil {
		authParams["SECRET_HASH"] = secretHash
	}

	// Try USER_PASSWORD_AUTH first, fallback to USER_SRP_AUTH if needed
	input := &cognitoidentityprovider.InitiateAuthInput{
		AuthFlow:       aws.String("USER_PASSWORD_AUTH"),
		ClientId:       aws.String(clientID),
		AuthParameters: authParams,
	}

	result, err := cognitoClient.InitiateAuth(input)
	if err != nil {
		// Check if USER_PASSWORD_AUTH is not enabled, try SRP instead
		if awsErr, ok := err.(awserr.Error); ok && awsErr.Code() == "InvalidParameterException" {
			log.Printf("USER_PASSWORD_AUTH not enabled, trying SRP authentication")

			// Try SRP authentication as fallback
			srpInput := &cognitoidentityprovider.InitiateAuthInput{
				AuthFlow: aws.String("USER_SRP_AUTH"),
				ClientId: aws.String(clientID),
				AuthParameters: map[string]*string{
					"USERNAME": aws.String(req.Username),
				},
			}

			// Add secret hash for SRP if needed
			if secretHash := calculateSecretHash(req.Username); secretHash != nil {
				srpInput.AuthParameters["SECRET_HASH"] = secretHash
			}

			// This will require SRP challenge handling - for now, return helpful error
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Please enable USER_PASSWORD_AUTH in your Cognito app client settings, or contact support for SRP authentication setup.",
				"details": "Go to AWS Cognito Console → Your User Pool → App Integration → App Clients → Edit → Enable 'ALLOW_USER_PASSWORD_AUTH'",
			})
			return
		}

		log.Printf("Login error: %v", err)
		statusCode, message := handleCognitoError(err)
		c.JSON(statusCode, gin.H{"error": message})
		return
	}

	// Sync user to local database
	go syncUserToDatabase(req.Username, req.Username, "") // Email will be updated later if available

	// Generate JWT token for our application
	jwtToken, err := generateJWTToken(req.Username)
	if err != nil {
		log.Printf("Error generating JWT token: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate session token"})
		return
	}

	// Set secure HTTP-only cookie
	c.SetCookie(
		"auth_token",           // name
		jwtToken,               // value
		3600*24*7,              // maxAge (7 days)
		"/",                    // path
		"",                     // domain
		false,                  // secure (set to true in production with HTTPS)
		true,                   // httpOnly
	)

	// Prepare response data
	responseData := gin.H{
		"message":  "Login successful",
		"username": req.Username,
	}

	// Add tokens if available (they might be nil for certain auth flows)
	if result.AuthenticationResult != nil {
		if result.AuthenticationResult.AccessToken != nil {
			responseData["accessToken"] = *result.AuthenticationResult.AccessToken
		}
		if result.AuthenticationResult.RefreshToken != nil {
			responseData["refreshToken"] = *result.AuthenticationResult.RefreshToken
		}
		if result.AuthenticationResult.IdToken != nil {
			responseData["idToken"] = *result.AuthenticationResult.IdToken
		}
	}

	c.JSON(http.StatusOK, responseData)
}

func signup(c *gin.Context) {
	var req SignupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if cognitoClient == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Authentication service not available"})
		return
	}

	// Create user attributes
	attributes := []*cognitoidentityprovider.AttributeType{
		{
			Name:  aws.String("email"),
			Value: aws.String(req.Email),
		},
	}

	if req.BirthDate != "" {
		attributes = append(attributes, &cognitoidentityprovider.AttributeType{
			Name:  aws.String("birthdate"),
			Value: aws.String(req.BirthDate),
		})
	}

	// Sign up user in Cognito
	input := &cognitoidentityprovider.SignUpInput{
		ClientId:       aws.String(clientID),
		Username:       aws.String(req.Username),
		Password:       aws.String(req.Password),
		UserAttributes: attributes,
	}

	// Add secret hash if client secret is configured
	if secretHash := calculateSecretHash(req.Username); secretHash != nil {
		input.SecretHash = secretHash
	}

	result, err := cognitoClient.SignUp(input)
	if err != nil {
		log.Printf("Signup error: %v", err)
		statusCode, message := handleCognitoError(err)
		c.JSON(statusCode, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Account created successfully. Please check your email for verification code.",
		"userSub": result.UserSub,
		"needsVerification": true,
		"username": req.Username,
	})
}

func confirmSignup(c *gin.Context) {
	var req ConfirmSignupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Prepare confirmation input
	input := &cognitoidentityprovider.ConfirmSignUpInput{
		ClientId:         aws.String(clientID),
		Username:         aws.String(req.Username),
		ConfirmationCode: aws.String(req.Code),
	}

	// Add secret hash if client secret is configured
	if secretHash := calculateSecretHash(req.Username); secretHash != nil {
		input.SecretHash = secretHash
	}

	// Confirm signup with Cognito
	_, err := cognitoClient.ConfirmSignUp(input)
	if err != nil {
		log.Printf("Confirm signup error: %v", err)
		statusCode, message := handleCognitoError(err)
		c.JSON(statusCode, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Email verified successfully. You can now log in.",
		"verified": true,
	})
}

func resendConfirmationCode(c *gin.Context) {
	var req ResendCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Prepare resend input
	input := &cognitoidentityprovider.ResendConfirmationCodeInput{
		ClientId: aws.String(clientID),
		Username: aws.String(req.Username),
	}

	// Add secret hash if client secret is configured
	if secretHash := calculateSecretHash(req.Username); secretHash != nil {
		input.SecretHash = secretHash
	}

	// Resend confirmation code
	_, err := cognitoClient.ResendConfirmationCode(input)
	if err != nil {
		log.Printf("Resend confirmation error: %v", err)
		statusCode, message := handleCognitoError(err)
		c.JSON(statusCode, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Verification code sent to your email.",
	})
}

func socialLogin(c *gin.Context) {
	var req SocialLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// For now, return a placeholder response
	// In production, you would validate the social token with the respective provider
	// and then create or authenticate the user in Cognito
	c.JSON(http.StatusOK, gin.H{
		"message": "Social login endpoint - implementation depends on provider setup",
		"provider": req.Provider,
	})
}

func refreshToken(c *gin.Context) {
	type RefreshRequest struct {
		RefreshToken string `json:"refreshToken" binding:"required"`
	}

	var req RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if cognitoClient == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Authentication service not available"})
		return
	}

	input := &cognitoidentityprovider.InitiateAuthInput{
		AuthFlow: aws.String("REFRESH_TOKEN_AUTH"),
		ClientId: aws.String(clientID),
		AuthParameters: map[string]*string{
			"REFRESH_TOKEN": aws.String(req.RefreshToken),
		},
	}

	result, err := cognitoClient.InitiateAuth(input)
	if err != nil {
		log.Printf("Token refresh error: %v", err)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid refresh token"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"accessToken": result.AuthenticationResult.AccessToken,
		"idToken":     result.AuthenticationResult.IdToken,
	})
}

func logout(c *gin.Context) {
	type LogoutRequest struct {
		AccessToken string `json:"accessToken"`
	}

	var req LogoutRequest
	// Make accessToken optional since we're using JWT cookies now
	c.ShouldBindJSON(&req)

	// Clear JWT cookie first (most important for our new auth system)
	c.SetCookie(
		"auth_token",           // name
		"",                     // value (empty to clear)
		-1,                     // maxAge (negative to expire immediately)
		"/",                    // path
		"",                     // domain
		false,                  // secure
		true,                   // httpOnly
	)

	// Also try to logout from Cognito if access token is provided
	if req.AccessToken != "" && cognitoClient != nil {
		input := &cognitoidentityprovider.GlobalSignOutInput{
			AccessToken: aws.String(req.AccessToken),
		}

		_, err := cognitoClient.GlobalSignOut(input)
		if err != nil {
			log.Printf("Cognito logout error (non-critical): %v", err)
			// Don't return error here since JWT cookie is already cleared
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Logged out successfully",
	})
}

// Initialize admin users
func initAdminUsers() {
	// Create default admin user
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("admin123"), bcrypt.DefaultCost)
	if err != nil {
		log.Fatal("Failed to hash admin password:", err)
	}

	adminUsers["admin"] = AdminUser{
		Username:     "admin",
		PasswordHash: string(hashedPassword),
		Email:        "<EMAIL>",
		Role:         "super_admin",
	}

	log.Println("Admin user created:")
	log.Println("Username: admin")
	log.Println("Password: admin123")
	log.Println("Please change the password after first login!")
}

// Admin login handler
func adminLogin(c *gin.Context) {
	type AdminLoginRequest struct {
		Username string `json:"username" binding:"required"`
		Password string `json:"password" binding:"required"`
	}

	var req AdminLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if admin user exists
	adminUser, exists := adminUsers[req.Username]
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Verify password
	err := bcrypt.CompareHashAndPassword([]byte(adminUser.PasswordHash), []byte(req.Password))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// For simplicity, we'll use a basic session token (in production, use JWT)
	sessionToken := base64.StdEncoding.EncodeToString([]byte(req.Username + ":admin"))

	c.JSON(http.StatusOK, gin.H{
		"message":      "Admin login successful",
		"sessionToken": sessionToken,
		"user": gin.H{
			"username": adminUser.Username,
			"email":    adminUser.Email,
			"role":     adminUser.Role,
		},
	})
}

// Admin authentication middleware
func adminAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionToken := c.GetHeader("Authorization")
		if sessionToken == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "No authorization token provided"})
			c.Abort()
			return
		}

		// Remove "Bearer " prefix if present
		if len(sessionToken) > 7 && sessionToken[:7] == "Bearer " {
			sessionToken = sessionToken[7:]
		}

		// Decode session token
		decoded, err := base64.StdEncoding.DecodeString(sessionToken)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid session token"})
			c.Abort()
			return
		}

		// Extract username
		parts := string(decoded)
		if len(parts) < 6 || parts[len(parts)-6:] != ":admin" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid session token"})
			c.Abort()
			return
		}

		username := parts[:len(parts)-6]

		// Check if admin user exists
		_, exists := adminUsers[username]
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid session"})
			c.Abort()
			return
		}

		// Set username in context for use in handlers
		c.Set("adminUsername", username)
		c.Next()
	}
}

// Admin API handlers
func getUsers(c *gin.Context) {
	// Check if Cognito client is available
	if cognitoClient == nil {
		log.Println("Cognito client not available, returning mock user data")
		// Return mock user data when Cognito is not configured
		mockUsers := []User{
			{
				ID:          "mock-user-1",
				Username:    "demo_user",
				Email:       "<EMAIL>",
				DisplayName: "Demo User",
				Status:      "active",
				CreatedAt:   time.Now().AddDate(0, -1, 0),
				UpdatedAt:   time.Now(),
			},
			{
				ID:          "mock-user-2",
				Username:    "test_user",
				Email:       "<EMAIL>",
				DisplayName: "Test User",
				Status:      "active",
				CreatedAt:   time.Now().AddDate(0, -2, 0),
				UpdatedAt:   time.Now(),
			},
			{
				ID:          "mock-user-3",
				Username:    "banned_user",
				Email:       "<EMAIL>",
				DisplayName: "Banned User",
				Status:      "banned",
				CreatedAt:   time.Now().AddDate(0, -3, 0),
				UpdatedAt:   time.Now(),
			},
		}
		c.JSON(http.StatusOK, mockUsers)
		return
	}

	// Get users from AWS Cognito
	cognitoUsers, err := getCognitoUsers()
	if err != nil {
		log.Printf("Error fetching Cognito users: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch users from Cognito: " + err.Error()})
		return
	}

	// If database is available, merge with local user data
	if db != nil {
		// Get local user data for status and additional info
		localUsers := make(map[string]User)
		rows, err := db.Query(`
			SELECT id, username, email, display_name, avatar_url, status, created_at, updated_at
			FROM users
		`)
		if err == nil {
			defer rows.Close()
			for rows.Next() {
				var user User
				var displayName, avatarURL sql.NullString

				err := rows.Scan(
					&user.ID, &user.Username, &user.Email, &displayName, &avatarURL,
					&user.Status, &user.CreatedAt, &user.UpdatedAt,
				)
				if err == nil {
					user.DisplayName = displayName.String
					user.AvatarURL = avatarURL.String
					localUsers[user.ID] = user
				}
			}
		}

		// Merge Cognito users with local data
		for i, cognitoUser := range cognitoUsers {
			if localUser, exists := localUsers[cognitoUser.ID]; exists {
				cognitoUsers[i].Status = localUser.Status
				cognitoUsers[i].DisplayName = localUser.DisplayName
				cognitoUsers[i].AvatarURL = localUser.AvatarURL
			}
		}
	}

	c.JSON(http.StatusOK, cognitoUsers)
}

// Get user details
func getUserDetails(c *gin.Context) {
	userID := c.Param("id")

	if cognitoClient == nil {
		// Return mock user details when Cognito is not configured
		mockUser := User{
			ID:          userID,
			Username:    userID,
			Email:       userID + "@example.com",
			DisplayName: "Demo User",
			Status:      "active",
			CreatedAt:   time.Now().AddDate(0, -1, 0),
			UpdatedAt:   time.Now(),
		}
		c.JSON(http.StatusOK, mockUser)
		return
	}

	// Get user details from AWS Cognito
	input := &cognitoidentityprovider.AdminGetUserInput{
		UserPoolId: aws.String(userPoolID),
		Username:   aws.String(userID),
	}

	result, err := cognitoClient.AdminGetUser(input)
	if err != nil {
		log.Printf("Error fetching user details for %s: %v", userID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	user := User{
		ID:        aws.StringValue(result.Username),
		Username:  aws.StringValue(result.Username),
		Status:    "active",
		CreatedAt: *result.UserCreateDate,
	}

	// Extract user attributes
	for _, attr := range result.UserAttributes {
		switch aws.StringValue(attr.Name) {
		case "email":
			user.Email = aws.StringValue(attr.Value)
		case "name":
			user.DisplayName = aws.StringValue(attr.Value)
		case "phone_number":
			// You can add phone number field to User struct if needed
		}
	}

	// Set user status based on Cognito status and enabled state
	if result.UserStatus != nil {
		switch aws.StringValue(result.UserStatus) {
		case "CONFIRMED":
			user.Status = "active"
		case "UNCONFIRMED":
			user.Status = "pending"
		case "ARCHIVED":
			user.Status = "banned"
		case "COMPROMISED":
			user.Status = "suspended"
		case "UNKNOWN":
			user.Status = "suspended"
		case "RESET_REQUIRED":
			user.Status = "suspended"
		case "FORCE_CHANGE_PASSWORD":
			user.Status = "active"
		}
	}

	// Check if user is disabled (this overrides the status)
	if result.Enabled != nil && !*result.Enabled {
		user.Status = "banned"
	}

	// If no display name from attributes, use username
	if user.DisplayName == "" {
		user.DisplayName = user.Username
	}

	// Fetch additional user data from database (bio, avatar_url, banner_url)
	var bio, avatarURL, bannerURL sql.NullString
	err = db.QueryRow("SELECT bio, avatar_url, banner_url FROM users WHERE username = ?", userID).Scan(&bio, &avatarURL, &bannerURL)
	if err != nil && err != sql.ErrNoRows {
		log.Printf("Error fetching user profile data for %s: %v", userID, err)
	}

	// Create response with additional fields
	response := map[string]interface{}{
		"id":           user.ID,
		"username":     user.Username,
		"email":        user.Email,
		"display_name": user.DisplayName,
		"status":       user.Status,
		"created_at":   user.CreatedAt,
		"bio":          bio.String,
		"avatar_url":   avatarURL.String,
		"banner_url":   bannerURL.String,
	}

	c.JSON(http.StatusOK, response)
}

// Update user details
func updateUser(c *gin.Context) {
	userID := c.Param("id")

	if cognitoClient == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "AWS Cognito not configured. Cannot update user details.",
		})
		return
	}

	type UpdateUserRequest struct {
		Email       string `json:"email"`
		DisplayName string `json:"display_name"`
		PhoneNumber string `json:"phone_number"`
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Prepare attributes to update
	var attributes []*cognitoidentityprovider.AttributeType

	if req.Email != "" {
		attributes = append(attributes, &cognitoidentityprovider.AttributeType{
			Name:  aws.String("email"),
			Value: aws.String(req.Email),
		})
	}

	if req.DisplayName != "" {
		attributes = append(attributes, &cognitoidentityprovider.AttributeType{
			Name:  aws.String("name"),
			Value: aws.String(req.DisplayName),
		})
	}

	if req.PhoneNumber != "" {
		attributes = append(attributes, &cognitoidentityprovider.AttributeType{
			Name:  aws.String("phone_number"),
			Value: aws.String(req.PhoneNumber),
		})
	}

	if len(attributes) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No attributes to update"})
		return
	}

	// Update user attributes in Cognito
	input := &cognitoidentityprovider.AdminUpdateUserAttributesInput{
		UserPoolId:     aws.String(userPoolID),
		Username:       aws.String(userID),
		UserAttributes: attributes,
	}

	_, err := cognitoClient.AdminUpdateUserAttributes(input)
	if err != nil {
		log.Printf("Error updating user %s: %v", userID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
		return
	}

	log.Printf("Admin updated user: %s", userID)
	c.JSON(http.StatusOK, gin.H{
		"message": "User updated successfully",
	})
}

// Get users from AWS Cognito
func getCognitoUsers() ([]User, error) {
	if cognitoClient == nil {
		return nil, fmt.Errorf("Cognito client not initialized")
	}

	input := &cognitoidentityprovider.ListUsersInput{
		UserPoolId: aws.String(userPoolID),
		Limit:      aws.Int64(60), // Maximum allowed by Cognito
	}

	var allUsers []User

	for {
		result, err := cognitoClient.ListUsers(input)
		if err != nil {
			return nil, err
		}

		for _, cognitoUser := range result.Users {
			user := User{
				ID:        aws.StringValue(cognitoUser.Username),
				Username:  aws.StringValue(cognitoUser.Username),
				Status:    "active", // Default status
				CreatedAt: *cognitoUser.UserCreateDate,
			}

			// Extract user attributes
			for _, attr := range cognitoUser.Attributes {
				switch aws.StringValue(attr.Name) {
				case "email":
					user.Email = aws.StringValue(attr.Value)
				case "email_verified":
					// You can use this to show verification status
				case "name":
					user.DisplayName = aws.StringValue(attr.Value)
				}
			}

			// Set user status based on Cognito status
			if cognitoUser.UserStatus != nil {
				switch aws.StringValue(cognitoUser.UserStatus) {
				case "CONFIRMED":
					user.Status = "active"
				case "UNCONFIRMED":
					user.Status = "pending"
				case "ARCHIVED":
					user.Status = "banned"
				case "COMPROMISED":
					user.Status = "suspended"
				case "UNKNOWN":
					user.Status = "suspended"
				case "RESET_REQUIRED":
					user.Status = "suspended"
				case "FORCE_CHANGE_PASSWORD":
					user.Status = "active"
				}
			}

			// Check if user is disabled (this overrides the status)
			if cognitoUser.Enabled != nil && !*cognitoUser.Enabled {
				user.Status = "banned"
			}

			// If no display name from attributes, use username
			if user.DisplayName == "" {
				user.DisplayName = user.Username
			}

			allUsers = append(allUsers, user)
		}

		// Check if there are more users to fetch
		if result.PaginationToken == nil {
			break
		}
		input.PaginationToken = result.PaginationToken
	}

	return allUsers, nil
}

// Sync user to local database (called when user logs in)
func syncUserToDatabase(userID, username, email string) {
	if db == nil {
		return
	}

	// Generate a unique email if none provided (to avoid UNIQUE constraint violations)
	if email == "" {
		email = fmt.Sprintf("%<EMAIL>", username)
	}

	// Check if user already exists
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM users WHERE id = ?", userID).Scan(&count)
	if err != nil {
		log.Printf("Error checking user existence: %v", err)
		return
	}

	if count == 0 {
		// Insert new user
		_, err = db.Exec(`
			INSERT INTO users (id, username, email, display_name, status, created_at, updated_at)
			VALUES (?, ?, ?, ?, 'active', NOW(), NOW())
		`, userID, username, email, username)
		if err != nil {
			log.Printf("Error inserting user into database: %v", err)
		} else {
			log.Printf("✅ Synced new user to database: %s (email: %s)", username, email)
		}
	} else {
		// Update existing user (only update email if it's not a generated one)
		if !strings.HasSuffix(email, "@moneybags.local") {
			_, err = db.Exec(`
				UPDATE users SET username = ?, email = ?, updated_at = NOW()
				WHERE id = ?
			`, username, email, userID)
		} else {
			_, err = db.Exec(`
				UPDATE users SET username = ?, updated_at = NOW()
				WHERE id = ?
			`, username, userID)
		}
		if err != nil {
			log.Printf("Error updating user in database: %v", err)
		} else {
			log.Printf("✅ Updated existing user in database: %s", username)
		}
	}
}

func banUser(c *gin.Context) {
	if cognitoClient == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "AWS Cognito not configured. Please set AWS credentials in .env file to enable real user management.",
		})
		return
	}

	userID := c.Param("id")

	// Disable user in AWS Cognito
	input := &cognitoidentityprovider.AdminDisableUserInput{
		UserPoolId: aws.String(userPoolID),
		Username:   aws.String(userID),
	}

	_, err := cognitoClient.AdminDisableUser(input)
	if err != nil {
		log.Printf("Error banning user %s in Cognito: %v", userID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to ban user in Cognito"})
		return
	}

	// Update local database if available
	if db != nil {
		_, err = db.Exec("UPDATE users SET status = 'banned', updated_at = NOW() WHERE id = ?", userID)
		if err != nil {
			log.Printf("Error updating user status in database %s: %v", userID, err)
			// Don't fail the request if database update fails, Cognito is the source of truth
		}
	}

	log.Printf("Admin banned user ID: %s", userID)
	c.JSON(http.StatusOK, gin.H{
		"message": "User banned successfully",
	})
}

func unbanUser(c *gin.Context) {
	if cognitoClient == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "AWS Cognito not configured. Please set AWS credentials in .env file to enable real user management.",
		})
		return
	}

	userID := c.Param("id")

	// Enable user in AWS Cognito
	input := &cognitoidentityprovider.AdminEnableUserInput{
		UserPoolId: aws.String(userPoolID),
		Username:   aws.String(userID),
	}

	_, err := cognitoClient.AdminEnableUser(input)
	if err != nil {
		log.Printf("Error unbanning user %s in Cognito: %v", userID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unban user in Cognito"})
		return
	}

	// Update local database if available
	if db != nil {
		_, err = db.Exec("UPDATE users SET status = 'active', updated_at = NOW() WHERE id = ?", userID)
		if err != nil {
			log.Printf("Error updating user status in database %s: %v", userID, err)
			// Don't fail the request if database update fails, Cognito is the source of truth
		}
	}

	log.Printf("Admin unbanned user ID: %s", userID)
	c.JSON(http.StatusOK, gin.H{
		"message": "User unbanned successfully",
	})
}

// Create new user (admin only)
func createUser(c *gin.Context) {
	type CreateUserRequest struct {
		Username    string `json:"username" binding:"required"`
		Email       string `json:"email" binding:"required,email"`
		Password    string `json:"password" binding:"required,min=8"`
		DisplayName string `json:"display_name"`
		PhoneNumber string `json:"phone_number"`
		SendWelcome bool   `json:"send_welcome"`
	}

	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if cognitoClient == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "AWS Cognito not configured. Please set AWS credentials in .env file to enable user creation.",
		})
		return
	}

	// Create user in AWS Cognito
	input := &cognitoidentityprovider.AdminCreateUserInput{
		UserPoolId:        aws.String(userPoolID),
		Username:          aws.String(req.Username),
		TemporaryPassword: aws.String(req.Password),
		MessageAction:     aws.String("SUPPRESS"), // Always suppress automatic messages
		UserAttributes: []*cognitoidentityprovider.AttributeType{
			{
				Name:  aws.String("email"),
				Value: aws.String(req.Email),
			},
			{
				Name:  aws.String("email_verified"),
				Value: aws.String("true"),
			},
		},
	}

	// Add optional attributes
	if req.DisplayName != "" {
		input.UserAttributes = append(input.UserAttributes, &cognitoidentityprovider.AttributeType{
			Name:  aws.String("name"),
			Value: aws.String(req.DisplayName),
		})
	}

	if req.PhoneNumber != "" {
		input.UserAttributes = append(input.UserAttributes, &cognitoidentityprovider.AttributeType{
			Name:  aws.String("phone_number"),
			Value: aws.String(req.PhoneNumber),
		})
	}

	// Note: We always suppress automatic messages and handle welcome emails separately if needed
	// The WELCOME value is not supported in MessageAction, only SUPPRESS and RESEND are valid

	result, err := cognitoClient.AdminCreateUser(input)
	if err != nil {
		log.Printf("Error creating user in Cognito: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user in Cognito"})
		return
	}

	// Create user in local database if available
	if db != nil {
		_, err = db.Exec(`
			INSERT INTO users (id, username, email, display_name, phone_number, status, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())
			ON DUPLICATE KEY UPDATE
			email = VALUES(email),
			display_name = VALUES(display_name),
			phone_number = VALUES(phone_number),
			updated_at = NOW()
		`, req.Username, req.Username, req.Email, req.DisplayName, req.PhoneNumber)
		if err != nil {
			log.Printf("Error creating user in database: %v", err)
			// Don't fail the request if database insert fails, Cognito is the source of truth
		}
	}

	log.Printf("Admin created user: %s", req.Username)
	c.JSON(http.StatusOK, gin.H{
		"message": "User created successfully",
		"user": gin.H{
			"username":     req.Username,
			"email":        req.Email,
			"display_name": req.DisplayName,
			"status":       *result.User.UserStatus,
		},
	})
}

// Upload VOD on behalf of a user (admin only)
func uploadVODForUser(c *gin.Context) {
	username := c.Param("username")
	if username == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Username is required"})
		return
	}

	// Check file size limit (2GB)
	c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, 2<<30)

	err := c.Request.ParseMultipartForm(2 << 30) // 2GB
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File too large (max 2GB)"})
		return
	}

	file, header, err := c.Request.FormFile("video")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No video file provided"})
		return
	}
	defer file.Close()

	// Read the entire file content into memory first to avoid file pointer issues
	fileContent, err := io.ReadAll(file)
	if err != nil {
		log.Printf("Error reading uploaded file: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read video file"})
		return
	}

	log.Printf("Read uploaded file: %s (size: %d bytes)", header.Filename, len(fileContent))

	// Validate file type
	contentType := header.Header.Get("Content-Type")
	if !strings.HasPrefix(contentType, "video/") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Only video files are allowed"})
		return
	}

	// Get form data
	title := c.PostForm("title")
	description := c.PostForm("description")
	categoryIDStr := c.PostForm("category_id")

	if title == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Title is required"})
		return
	}

	categoryID, err := strconv.Atoi(categoryIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	// Verify user exists
	if cognitoClient != nil {
		input := &cognitoidentityprovider.AdminGetUserInput{
			UserPoolId: aws.String(userPoolID),
			Username:   aws.String(username),
		}
		_, err := cognitoClient.AdminGetUser(input)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
			return
		}
	}

	// Generate unique video ID
	videoID := fmt.Sprintf("admin_vod_%s_%d", username, time.Now().Unix())

	// Create temp directory for processing
	tempDir := fmt.Sprintf("/tmp/video_processing_%s", videoID)
	err = os.MkdirAll(tempDir, 0755)
	if err != nil {
		log.Printf("Error creating temp directory: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create processing directory"})
		return
	}
	// Note: tempDir cleanup is handled by processVideo goroutine

	// Save uploaded file content to temp directory
	originalPath := fmt.Sprintf("%s/original_%s", tempDir, header.Filename)
	err = os.WriteFile(originalPath, fileContent, 0644)
	if err != nil {
		log.Printf("Error saving file to %s: %v", originalPath, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save uploaded file"})
		return
	}

	log.Printf("Saved file to temp directory: %s (size: %d bytes)", originalPath, len(fileContent))

	// Handle thumbnail upload if provided
	var thumbnailURL string
	thumbnailFile, thumbnailHeader, err := c.Request.FormFile("thumbnail")
	if err == nil && thumbnailFile != nil {
		defer thumbnailFile.Close()

		// Validate thumbnail file type
		thumbnailContentType := thumbnailHeader.Header.Get("Content-Type")
		if !strings.HasPrefix(thumbnailContentType, "image/") {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Thumbnail must be an image file"})
			return
		}

		// Validate thumbnail file size (10MB limit)
		if thumbnailHeader.Size > 10*1024*1024 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Thumbnail file size must be less than 10MB"})
			return
		}

		// Upload thumbnail to S3
		if s3Client != nil {
			thumbnailKey := fmt.Sprintf("thumbnails/%s_%d_%s", username, time.Now().Unix(), thumbnailHeader.Filename)

			_, err = s3Client.PutObject(&s3.PutObjectInput{
				Bucket:      aws.String(s3Bucket),
				Key:         aws.String(thumbnailKey),
				Body:        thumbnailFile,
				ContentType: aws.String(thumbnailContentType),
				// Removed ACL as the bucket doesn't support ACLs
			})
			if err != nil {
				log.Printf("Error uploading thumbnail to S3: %v", err)
				// Don't fail the entire upload if thumbnail fails
			} else {
				thumbnailURL = fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", s3Bucket, s3Region, thumbnailKey)
				log.Printf("Thumbnail uploaded successfully: %s", thumbnailURL)
			}
		}
	}

	// Get category name from category ID
	var categoryName string
	if db != nil {
		err = db.QueryRow("SELECT name FROM categories WHERE id = ?", categoryID).Scan(&categoryName)
		if err != nil {
			log.Printf("Error getting category name for ID %d: %v", categoryID, err)
			categoryName = "Unknown" // Fallback to Unknown if category not found
		}
	}

	// All uploaded videos will be processed to HLS format for better streaming performance
	fileExt := strings.ToLower(filepath.Ext(header.Filename))
	status := "processing"
	var videoURL string

	log.Printf("VOD %s is %s format, will be processed to HLS for better streaming", videoID, fileExt)

	// Insert VOD record into database (use vods table since this should appear in creator's VOD section)
	if db != nil {
		// For admin uploads, we need to create or reference a dummy stream since stream_id has a foreign key constraint
		// First, ensure we have an admin stream record (use NULL for category to avoid foreign key issues)
		adminStreamID := "admin_upload_stream"
		_, err = db.Exec(`
			INSERT IGNORE INTO streams (id, title, description, streamer_id, streamer_name, category_id, category_name, is_live, started_at, created_at, updated_at)
			VALUES (?, 'Admin Upload Stream', 'Placeholder stream for admin uploaded VODs', 'admin', 'Admin', NULL, NULL, FALSE, NOW(), NOW(), NOW())
		`, adminStreamID)
		if err != nil {
			log.Printf("Warning: Could not create admin stream: %v", err)
		}

		_, err = db.Exec(`
			INSERT INTO vods (vod_id, stream_id, title, description, streamer_id, streamer_name, category_id, category_name, recording_status, thumbnail_url, video_url, stream_started_at, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW())
		`, videoID, adminStreamID, title, description, username, username, categoryID, categoryName, status, thumbnailURL, videoURL)
		if err != nil {
			log.Printf("Error inserting VOD record: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create VOD record"})
			return
		}
	}

	// Start background processing for HLS conversion
	go processVOD(videoID, originalPath, tempDir)

	log.Printf("Admin uploaded VOD for user %s: %s", username, videoID)

	message := fmt.Sprintf("VOD upload successful for user %s, HLS processing started for better streaming", username)

	response := gin.H{
		"video_id": videoID,
		"status":   status,
		"message":  message,
		"user":     username,
		"title":    title,
	}

	if thumbnailURL != "" {
		response["thumbnail_url"] = thumbnailURL
	}

	c.JSON(http.StatusOK, response)
}

func getAdminCategories(c *gin.Context) {
	if db == nil {
		// Return mock categories when database is not available
		mockCategories := []Category{
			{
				ID:          1,
				Name:        "Gaming",
				Description: "Video games and gaming content",
				PhotoURL:    "",
				IsEnabled:   true,
				Viewers:     125000,
				CreatedAt:   time.Now().AddDate(0, -1, 0),
				UpdatedAt:   time.Now(),
			},
			{
				ID:          2,
				Name:        "Music",
				Description: "Music performances and discussions",
				PhotoURL:    "",
				IsEnabled:   true,
				Viewers:     45000,
				CreatedAt:   time.Now().AddDate(0, -2, 0),
				UpdatedAt:   time.Now(),
			},
			{
				ID:          3,
				Name:        "Art",
				Description: "Digital art and creative content",
				PhotoURL:    "",
				IsEnabled:   false,
				Viewers:     23000,
				CreatedAt:   time.Now().AddDate(0, -3, 0),
				UpdatedAt:   time.Now(),
			},
		}
		c.JSON(http.StatusOK, mockCategories)
		return
	}

	rows, err := db.Query(`
		SELECT id, name, description, photo_url, is_enabled, viewers, created_at, updated_at
		FROM categories
		ORDER BY name ASC
	`)
	if err != nil {
		log.Printf("Error querying categories: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch categories"})
		return
	}
	defer rows.Close()

	var categories []Category
	for rows.Next() {
		var category Category
		var description, photoURL sql.NullString

		err := rows.Scan(
			&category.ID, &category.Name, &description, &photoURL, &category.IsEnabled,
			&category.Viewers, &category.CreatedAt, &category.UpdatedAt,
		)
		if err != nil {
			log.Printf("Error scanning category row: %v", err)
			continue
		}

		category.Description = description.String
		category.PhotoURL = photoURL.String
		categories = append(categories, category)
	}

	c.JSON(http.StatusOK, categories)
}

// Get single category for editing
func getAdminCategory(c *gin.Context) {
	categoryID := c.Param("id")

	if db == nil {
		// Return mock category when database is not available
		mockCategory := Category{
			ID:          1,
			Name:        "Gaming",
			Description: "Video games and gaming content",
			PhotoURL:    "",
			IsEnabled:   true,
			Viewers:     125000,
			CreatedAt:   time.Now().AddDate(0, -1, 0),
			UpdatedAt:   time.Now(),
		}
		c.JSON(http.StatusOK, mockCategory)
		return
	}

	var category Category
	var description, photoURL sql.NullString

	err := db.QueryRow(`
		SELECT id, name, description, photo_url, is_enabled, viewers, created_at, updated_at
		FROM categories
		WHERE id = ?
	`, categoryID).Scan(
		&category.ID, &category.Name, &description, &photoURL, &category.IsEnabled,
		&category.Viewers, &category.CreatedAt, &category.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		} else {
			log.Printf("Error fetching category %s: %v", categoryID, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch category"})
		}
		return
	}

	category.Description = description.String
	category.PhotoURL = photoURL.String

	c.JSON(http.StatusOK, category)
}

func createCategory(c *gin.Context) {
	if db == nil {
		// For demo purposes, return success even without database
		// In production, you would want to require database
		type CreateCategoryRequest struct {
			Name        string `json:"name" binding:"required"`
			Description string `json:"description"`
			PhotoURL    string `json:"photo_url"`
			IsEnabled   bool   `json:"is_enabled"`
		}

		var req CreateCategoryRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		log.Printf("Demo: Category would be created - Name: %s, Description: %s, Photo: %s, Enabled: %v",
			req.Name, req.Description, req.PhotoURL, req.IsEnabled)

		c.JSON(http.StatusOK, gin.H{
			"message": "Category created successfully (demo mode - database not configured)",
			"category": gin.H{
				"id":          999, // Mock ID
				"name":        req.Name,
				"description": req.Description,
				"photo_url":   req.PhotoURL,
				"is_enabled":  req.IsEnabled,
			},
		})
		return
	}

	type CreateCategoryRequest struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
		PhotoURL    string `json:"photo_url"`
		IsEnabled   bool   `json:"is_enabled"`
	}

	var req CreateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Insert new category
	result, err := db.Exec(
		"INSERT INTO categories (name, description, photo_url, is_enabled) VALUES (?, ?, ?, ?)",
		req.Name, req.Description, req.PhotoURL, req.IsEnabled,
	)
	if err != nil {
		log.Printf("Error creating category: %v", err)
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1062 {
			c.JSON(http.StatusConflict, gin.H{"error": "Category name already exists"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create category"})
		}
		return
	}

	categoryID, _ := result.LastInsertId()
	log.Printf("Admin created category: %s (ID: %d)", req.Name, categoryID)

	c.JSON(http.StatusOK, gin.H{
		"message": "Category created successfully",
		"category": gin.H{
			"id":          categoryID,
			"name":        req.Name,
			"description": req.Description,
			"photo_url":   req.PhotoURL,
			"is_enabled":  req.IsEnabled,
		},
	})
}

func updateCategory(c *gin.Context) {
	if db == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Database not configured. Please set up database connection to update categories.",
		})
		return
	}

	categoryID := c.Param("id")

	type UpdateCategoryRequest struct {
		Name        string `json:"name"`
		Description string `json:"description"`
		PhotoURL    string `json:"photo_url"`
		IsEnabled   *bool  `json:"is_enabled"` // Pointer to distinguish between false and not provided
	}

	var req UpdateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Build dynamic update query
	setParts := []string{}
	args := []interface{}{}

	if req.Name != "" {
		setParts = append(setParts, "name = ?")
		args = append(args, req.Name)
	}
	if req.Description != "" {
		setParts = append(setParts, "description = ?")
		args = append(args, req.Description)
	}
	if req.PhotoURL != "" {
		setParts = append(setParts, "photo_url = ?")
		args = append(args, req.PhotoURL)
	}
	if req.IsEnabled != nil {
		setParts = append(setParts, "is_enabled = ?")
		args = append(args, *req.IsEnabled)
	}

	if len(setParts) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	setParts = append(setParts, "updated_at = NOW()")
	args = append(args, categoryID)

	query := "UPDATE categories SET " + strings.Join(setParts, ", ") + " WHERE id = ?"
	_, err := db.Exec(query, args...)
	if err != nil {
		log.Printf("Error updating category %s: %v", categoryID, err)
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1062 {
			c.JSON(http.StatusConflict, gin.H{"error": "Category name already exists"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update category"})
		}
		return
	}

	log.Printf("Admin updated category ID: %s", categoryID)
	c.JSON(http.StatusOK, gin.H{
		"message": "Category updated successfully",
	})
}

// Toggle category enabled/disabled status
func toggleCategory(c *gin.Context) {
	if db == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Database not configured. Please set up database connection to toggle categories.",
		})
		return
	}

	categoryID := c.Param("id")

	// Get current status
	var isEnabled bool
	err := db.QueryRow("SELECT is_enabled FROM categories WHERE id = ?", categoryID).Scan(&isEnabled)
	if err != nil {
		log.Printf("Error getting category status %s: %v", categoryID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	// Toggle status
	newStatus := !isEnabled
	_, err = db.Exec("UPDATE categories SET is_enabled = ?, updated_at = NOW() WHERE id = ?", newStatus, categoryID)
	if err != nil {
		log.Printf("Error toggling category %s: %v", categoryID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to toggle category"})
		return
	}

	statusText := "enabled"
	if !newStatus {
		statusText = "disabled"
	}

	log.Printf("Admin %s category ID: %s", statusText, categoryID)
	c.JSON(http.StatusOK, gin.H{
		"message": fmt.Sprintf("Category %s successfully", statusText),
		"is_enabled": newStatus,
	})
}

func deleteCategory(c *gin.Context) {
	if db == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Database not configured. Please set up database connection to delete categories.",
		})
		return
	}

	categoryID := c.Param("id")

	// Check if category is being used by streams or videos
	var streamCount, videoCount int
	db.QueryRow("SELECT COUNT(*) FROM streams WHERE category_id = ?", categoryID).Scan(&streamCount)
	db.QueryRow("SELECT COUNT(*) FROM videos WHERE category_id = ?", categoryID).Scan(&videoCount)

	if streamCount > 0 || videoCount > 0 {
		c.JSON(http.StatusConflict, gin.H{
			"error": "Cannot delete category that is being used by streams or videos",
		})
		return
	}

	// Delete category
	result, err := db.Exec("DELETE FROM categories WHERE id = ?", categoryID)
	if err != nil {
		log.Printf("Error deleting category %s: %v", categoryID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete category"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	log.Printf("Admin deleted category ID: %s", categoryID)
	c.JSON(http.StatusOK, gin.H{
		"message": "Category deleted successfully",
	})
}

func getAdminStreams(c *gin.Context) {
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database not available"})
		return
	}

	rows, err := db.Query(`
		SELECT s.id, s.title, s.description, s.streamer_id, s.streamer_name,
		       s.category_id, s.category_name, s.viewers, s.is_live,
		       s.stream_key, s.thumbnail_url, s.created_at, s.updated_at
		FROM streams s
		WHERE s.is_live = TRUE
		ORDER BY s.created_at DESC
	`)
	if err != nil {
		log.Printf("Error querying streams: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch streams"})
		return
	}
	defer rows.Close()

	var streams []Stream
	for rows.Next() {
		var stream Stream
		var description, categoryName, streamKey, thumbnailURL sql.NullString
		var categoryID sql.NullInt64

		err := rows.Scan(
			&stream.ID, &stream.Title, &description, &stream.StreamerID, &stream.StreamerName,
			&categoryID, &categoryName, &stream.Viewers, &stream.IsLive,
			&streamKey, &thumbnailURL, &stream.CreatedAt, &stream.UpdatedAt,
		)
		if err != nil {
			log.Printf("Error scanning stream row: %v", err)
			continue
		}

		stream.Description = description.String
		stream.CategoryName = categoryName.String
		stream.StreamKey = streamKey.String
		stream.ThumbnailURL = thumbnailURL.String
		if categoryID.Valid {
			stream.CategoryID = int(categoryID.Int64)
		}

		streams = append(streams, stream)
	}

	c.JSON(http.StatusOK, streams)
}

func deleteStream(c *gin.Context) {
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database not available"})
		return
	}

	streamID := c.Param("id")

	// End the stream by setting is_live to false
	result, err := db.Exec("UPDATE streams SET is_live = FALSE, updated_at = NOW() WHERE id = ?", streamID)
	if err != nil {
		log.Printf("Error ending stream %s: %v", streamID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to end stream"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Stream not found"})
		return
	}

	log.Printf("Admin ended stream ID: %s", streamID)
	c.JSON(http.StatusOK, gin.H{
		"message": "Stream ended successfully",
	})
}

// Get all videos for admin management (both user uploads and stream VODs)
func getAdminVideos(c *gin.Context) {
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database not available"})
		return
	}

	// Query both videos (user uploads) and vods (stream recordings) tables
	query := `
		SELECT
			video_id as id,
			title,
			description,
			creator_id,
			creator_name,
			category_id,
			status,
			thumbnail_url,
			video_url,
			views,
			duration,
			created_at,
			updated_at,
			COALESCE(category_name, 'Unknown') as category_name,
			'upload' as type
		FROM videos
		UNION ALL
		SELECT
			vod_id as id,
			title,
			description,
			streamer_id as creator_id,
			streamer_name as creator_name,
			category_id,
			recording_status as status,
			thumbnail_url,
			video_url,
			views,
			duration,
			created_at,
			updated_at,
			COALESCE(category_name, 'Unknown') as category_name,
			'stream' as type
		FROM vods
		ORDER BY created_at DESC
	`

	rows, err := db.Query(query)
	if err != nil {
		log.Printf("Error querying videos and vods: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch videos"})
		return
	}
	defer rows.Close()

	videos := make([]map[string]interface{}, 0)
	for rows.Next() {
		var video struct {
			ID           string    `json:"id"`
			Title        string    `json:"title"`
			Description  string    `json:"description"`
			CreatorID    string    `json:"creator_id"`
			CreatorName  string    `json:"creator_name"`
			CategoryID   *int      `json:"category_id"`
			Status       string    `json:"status"`
			ThumbnailURL *string   `json:"thumbnail_url"`
			VideoURL     *string   `json:"video_url"`
			Views        int       `json:"views"`
			Duration     *int      `json:"duration"`
			CreatedAt    time.Time `json:"created_at"`
			UpdatedAt    time.Time `json:"updated_at"`
			CategoryName string    `json:"category_name"`
			Type         string    `json:"type"`
		}

		err := rows.Scan(
			&video.ID, &video.Title, &video.Description, &video.CreatorID,
			&video.CreatorName, &video.CategoryID, &video.Status, &video.ThumbnailURL,
			&video.VideoURL, &video.Views, &video.Duration, &video.CreatedAt,
			&video.UpdatedAt, &video.CategoryName, &video.Type,
		)
		if err != nil {
			log.Printf("Error scanning video row: %v", err)
			continue
		}

		// Format duration for display
		durationStr := "N/A"
		if video.Duration != nil && *video.Duration > 0 {
			// Convert seconds to HH:MM:SS format
			seconds := *video.Duration
			hours := seconds / 3600
			minutes := (seconds % 3600) / 60
			secs := seconds % 60
			if hours > 0 {
				durationStr = fmt.Sprintf("%d:%02d:%02d", hours, minutes, secs)
			} else {
				durationStr = fmt.Sprintf("%d:%02d", minutes, secs)
			}
		}

		// Format thumbnail URL
		thumbnailURL := ""
		if video.ThumbnailURL != nil {
			thumbnailURL = *video.ThumbnailURL
		}

		// Format status with type indicator
		statusDisplay := video.Status
		if video.Type == "stream" {
			statusDisplay = fmt.Sprintf("%s (Stream VOD)", video.Status)
		} else {
			statusDisplay = fmt.Sprintf("%s (Upload)", video.Status)
		}

		videos = append(videos, map[string]interface{}{
			"id":            video.ID,
			"title":         video.Title,
			"description":   video.Description,
			"creator":       video.CreatorName,
			"creator_id":    video.CreatorID,
			"category":      video.CategoryName,
			"status":        statusDisplay,
			"thumbnail_url": thumbnailURL,
			"views":         video.Views,
			"duration":      durationStr,
			"created_at":    video.CreatedAt.Format("2006-01-02 15:04:05"),
			"type":          video.Type,
		})
	}

	log.Printf("📹 Admin videos query returned %d videos", len(videos))
	c.JSON(http.StatusOK, videos)
}

// Delete a video (admin only) - handles both user uploads and stream VODs
func deleteAdminVideo(c *gin.Context) {
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database not available"})
		return
	}

	videoID := c.Param("id")
	log.Printf("🗑️ Admin attempting to delete video ID: %s", videoID)

	// First, try to find the video in the videos table (user uploads)
	var thumbnailURL, videoURL sql.NullString
	var videoType string

	err := db.QueryRow("SELECT thumbnail_url, video_url FROM videos WHERE video_id = ?", videoID).Scan(&thumbnailURL, &videoURL)
	if err == nil {
		videoType = "upload"
	} else if err == sql.ErrNoRows {
		// Not found in videos table, try vods table (stream recordings)
		err = db.QueryRow("SELECT thumbnail_url, video_url FROM vods WHERE vod_id = ?", videoID).Scan(&thumbnailURL, &videoURL)
		if err == nil {
			videoType = "stream"
		} else if err == sql.ErrNoRows {
			log.Printf("❌ Video not found in either videos or vods table: %s", videoID)
			c.JSON(http.StatusNotFound, gin.H{"error": "Video not found"})
			return
		} else {
			log.Printf("Error fetching VOD details: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch video details"})
			return
		}
	} else {
		log.Printf("Error fetching video details: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch video details"})
		return
	}

	// Delete the video record from the appropriate table
	var result sql.Result
	if videoType == "upload" {
		result, err = db.Exec("DELETE FROM videos WHERE video_id = ?", videoID)
		log.Printf("🗑️ Deleting from videos table: %s", videoID)
	} else {
		result, err = db.Exec("DELETE FROM vods WHERE vod_id = ?", videoID)
		log.Printf("🗑️ Deleting from vods table: %s", videoID)
	}

	if err != nil {
		log.Printf("Error deleting video %s: %v", videoID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete video"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		log.Printf("❌ No rows affected when deleting video: %s", videoID)
		c.JSON(http.StatusNotFound, gin.H{"error": "Video not found"})
		return
	}

	// TODO: Optionally delete files from S3
	// This could be implemented later for complete cleanup
	if s3Client != nil {
		// Clean up thumbnail from S3 if exists
		if thumbnailURL.Valid && thumbnailURL.String != "" {
			// Extract S3 key from URL and delete
			// Implementation can be added later
		}
		// Clean up video file from S3 if exists
		if videoURL.Valid && videoURL.String != "" {
			// Extract S3 key from URL and delete
			// Implementation can be added later
		}
	}

	log.Printf("✅ Admin successfully deleted %s video ID: %s", videoType, videoID)
	c.JSON(http.StatusOK, gin.H{
		"message": "Video deleted successfully",
	})
}

// Upload file to S3
func uploadFile(c *gin.Context) {
	if s3Client == nil || s3Bucket == "" {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "S3 service not configured. Please set S3_BUCKET and AWS credentials in .env file to enable file uploads.",
		})
		return
	}

	// Parse multipart form
	err := c.Request.ParseMultipartForm(10 << 20) // 10 MB max
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse form"})
		return
	}

	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file provided"})
		return
	}
	defer file.Close()

	// Validate file type (images only)
	contentType := header.Header.Get("Content-Type")
	if !strings.HasPrefix(contentType, "image/") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Only image files are allowed"})
		return
	}

	// Generate unique filename
	filename := fmt.Sprintf("categories/%d_%s", time.Now().Unix(), header.Filename)

	// Upload to S3 (without ACL to avoid AccessControlListNotSupported error)
	input := &s3.PutObjectInput{
		Bucket:      aws.String(s3Bucket),
		Key:         aws.String(filename),
		Body:        file,
		ContentType: aws.String(contentType),
		// Removed ACL parameter - bucket policy should handle public access
	}

	_, err = s3Client.PutObject(input)
	if err != nil {
		log.Printf("Error uploading file to S3: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload file"})
		return
	}

	// Generate public URL
	region := s3Region
	if region == "" {
		region = "us-east-1"
	}
	fileURL := fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", s3Bucket, region, filename)

	log.Printf("File uploaded successfully: %s", filename)
	c.JSON(http.StatusOK, gin.H{
		"message": "File uploaded successfully",
		"url":     fileURL,
		"filename": filename,
	})
}


// Request payload for saving profile settings
type ProfileSettingsRequest struct {
	DisplayName  string `json:"displayName"`
	Bio          string `json:"bio"`
	RemoveBanner bool   `json:"removeBanner"`
}

// getUserProfile returns current user's profile data for settings page
func getUserProfile(c *gin.Context) {
	// Authenticate via JWT cookie
	tokenString, err := c.Cookie("auth_token")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	claims, err := validateJWTToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
		return
	}

	// Get user profile data from database
	var displayName, avatarURL, bannerURL, bio sql.NullString
	err = db.QueryRow("SELECT display_name, avatar_url, banner_url, bio FROM users WHERE username = ?", claims.Username).Scan(&displayName, &avatarURL, &bannerURL, &bio)
	if err != nil {
		log.Printf("getUserProfile: failed to get user '%s': %v", claims.Username, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load profile"})
		return
	}

	response := gin.H{
		"username":    claims.Username,
		"displayName": displayName.String,
		"avatarUrl":   avatarURL.String,
		"bannerUrl":   bannerURL.String,
		"bio":         bio.String,
	}

	log.Printf("getUserProfile: returning data for user '%s': displayName='%s', avatarUrl='%s', bannerUrl='%s', bio='%s'",
		claims.Username, displayName.String, avatarURL.String, bannerURL.String, bio.String)

	c.JSON(http.StatusOK, response)
}

// saveProfileSettings updates basic profile fields like display name (bio is accepted but ignored unless supported in schema)
func saveProfileSettings(c *gin.Context) {
	// Authenticate via JWT cookie (settings page already requires auth)
	tokenString, err := c.Cookie("auth_token")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	claims, err := validateJWTToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
		return
	}

	var req ProfileSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	displayName := strings.TrimSpace(req.DisplayName)
	if displayName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "displayName is required"})
		return
	}

	log.Printf("saveProfileSettings: saving for user '%s': displayName='%s', bio='%s', removeBanner=%v",
		claims.Username, displayName, req.Bio, req.RemoveBanner)

	// Handle banner removal
	if req.RemoveBanner {
		if _, err := db.Exec("UPDATE users SET banner_url = NULL, updated_at = NOW() WHERE username = ?", claims.Username); err != nil {
			log.Printf("saveProfileSettings: failed to remove banner for user '%s': %v", claims.Username, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove banner"})
			return
		}
		log.Printf("saveProfileSettings: successfully removed banner for user '%s'", claims.Username)
		c.JSON(http.StatusOK, gin.H{"message": "Banner removed successfully"})
		return
	}

	// Update display_name and bio in users table
	if _, err := db.Exec("UPDATE users SET display_name = ?, bio = ?, updated_at = NOW() WHERE username = ?", displayName, req.Bio, claims.Username); err != nil {
		log.Printf("saveProfileSettings: failed to update user '%s': %v", claims.Username, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save profile settings"})
		return
	}

	log.Printf("saveProfileSettings: successfully updated user '%s'", claims.Username)

	c.JSON(http.StatusOK, gin.H{
		"message":     "Profile settings saved successfully",
		"displayName": displayName,
		"bio":         req.Bio,
	})
}

// Change password request structure
type ChangePasswordRequest struct {
	CurrentPassword string `json:"currentPassword" binding:"required"`
	NewPassword     string `json:"newPassword" binding:"required"`
}

// changePassword handles password change requests
func changePassword(c *gin.Context) {
	// Authenticate via JWT cookie
	tokenString, err := c.Cookie("auth_token")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	claims, err := validateJWTToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
		return
	}

	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Validate new password requirements
	if len(req.NewPassword) < 8 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "New password must be at least 8 characters long"})
		return
	}

	if cognitoClient == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Password change not available. AWS Cognito not configured.",
		})
		return
	}

	// User is already authenticated via JWT, so we can proceed with password change
	// Note: In a production environment, you might want to add additional verification
	// such as requiring the current password, but since the user has a valid JWT token,
	// they have already proven their identity
	log.Printf("🔐 Changing password for authenticated user: %s", claims.Username)

	changePasswordInput := &cognitoidentityprovider.AdminSetUserPasswordInput{
		UserPoolId: aws.String(userPoolID),
		Username:   aws.String(claims.Username),
		Password:   aws.String(req.NewPassword),
		Permanent:  aws.Bool(true), // Set as permanent password (not temporary)
	}

	_, changeErr := cognitoClient.AdminSetUserPassword(changePasswordInput)
	if changeErr != nil {
		log.Printf("Password change failed for user %s: %v", claims.Username, changeErr)
		if awsErr, ok := changeErr.(awserr.Error); ok {
			switch awsErr.Code() {
			case "InvalidPasswordException":
				c.JSON(http.StatusBadRequest, gin.H{"error": "New password does not meet requirements. Password must be at least 8 characters long and contain uppercase, lowercase, numbers, and special characters."})
				return
			case "UserNotFoundException":
				c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
				return
			case "LimitExceededException":
				c.JSON(http.StatusTooManyRequests, gin.H{"error": "Too many password change attempts. Please try again later"})
				return
			case "NotAuthorizedException":
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Not authorized to change password"})
				return
			}
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to change password"})
		return
	}

	log.Printf("Password changed successfully for user: %s", claims.Username)
	c.JSON(http.StatusOK, gin.H{
		"message": "Password changed successfully",
	})
}

// ensureUserProfileColumns adds missing user profile columns on existing databases
func ensureUserProfileColumns() {
	if db == nil {
		return
	}
	// Ensure 'bio' column exists
	var cnt int
	err := db.QueryRow("SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users' AND COLUMN_NAME = 'bio'", dbName).Scan(&cnt)
	if err != nil {
		log.Printf("ensureUserProfileColumns: failed to check bio column: %v", err)
	} else if cnt == 0 {
		if _, err := db.Exec("ALTER TABLE users ADD COLUMN bio TEXT NULL AFTER avatar_url"); err != nil {
			log.Printf("ensureUserProfileColumns: failed to add bio column: %v", err)
		} else {
			log.Printf("ensureUserProfileColumns: added 'bio' column to users table")
		}
	}

	// Ensure 'banner_url' column exists
	err = db.QueryRow("SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users' AND COLUMN_NAME = 'banner_url'", dbName).Scan(&cnt)
	if err != nil {
		log.Printf("ensureUserProfileColumns: failed to check banner_url column: %v", err)
	} else if cnt == 0 {
		if _, err := db.Exec("ALTER TABLE users ADD COLUMN banner_url TEXT NULL AFTER bio"); err != nil {
			log.Printf("ensureUserProfileColumns: failed to add banner_url column: %v", err)
		} else {
			log.Printf("ensureUserProfileColumns: added 'banner_url' column to users table")
		}
	}
}

// Upload avatar image to S3 and update user's avatar_url
func uploadAvatar(c *gin.Context) {
	if s3Client == nil || s3Bucket == "" {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "S3 service not configured"})
		return
	}
	// Authenticate
	tokenString, err := c.Cookie("auth_token")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}
	claims, err := validateJWTToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
		return
	}
	// Limit size to 6MB
	c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, 6<<20)
	if err := c.Request.ParseMultipartForm(6 << 20); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Image too large (max 6MB)"})
		return
	}
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file provided"})
		return
	}
	defer file.Close()
	contentType := header.Header.Get("Content-Type")
	if !strings.HasPrefix(contentType, "image/") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Only image files are allowed"})
		return
	}
	ext := path.Ext(header.Filename)
	if ext == "" {
		ext = ".jpg"
	}
	key := fmt.Sprintf("avatars/%s_%d%s", claims.Username, time.Now().Unix(), ext)
	_, err = s3Client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(s3Bucket),
		Key:         aws.String(key),
		Body:        file,
		ContentType: aws.String(contentType),
	})
	if err != nil {
		log.Printf("uploadAvatar: S3 put failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload image"})
		return
	}
	region := s3Region
	if region == "" {
		region = "us-east-1"
	}
	url := fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", s3Bucket, region, key)
	if _, err := db.Exec("UPDATE users SET avatar_url = ?, updated_at = NOW() WHERE username = ?", url, claims.Username); err != nil {
		log.Printf("uploadAvatar: DB update failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save avatar URL"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Avatar uploaded", "url": url})
}

// Upload banner image to S3 and update user's banner_url
func uploadBanner(c *gin.Context) {
	if s3Client == nil || s3Bucket == "" {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "S3 service not configured"})
		return
	}
	// Authenticate
	tokenString, err := c.Cookie("auth_token")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}
	claims, err := validateJWTToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
		return
	}
	// Get uploaded file
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file uploaded"})
		return
	}
	defer file.Close()
	// Validate file type
	contentType := header.Header.Get("Content-Type")
	if !strings.HasPrefix(contentType, "image/") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File must be an image"})
		return
	}
	// Generate unique filename
	timestamp := time.Now().Unix()
	key := fmt.Sprintf("banners/%s_%d.%s", claims.Username, timestamp, strings.Split(contentType, "/")[1])
	// Upload to S3
	_, err = s3Client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(s3Bucket),
		Key:         aws.String(key),
		Body:        file,
		ContentType: aws.String(contentType),
	})
	if err != nil {
		log.Printf("uploadBanner: S3 put failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload image"})
		return
	}
	region := s3Region
	if region == "" {
		region = "us-east-1"
	}
	url := fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", s3Bucket, region, key)
	if _, err := db.Exec("UPDATE users SET banner_url = ?, updated_at = NOW() WHERE username = ?", url, claims.Username); err != nil {
		log.Printf("uploadBanner: DB update failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save banner URL"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Banner uploaded", "url": url})
}

// Admin function to upload user avatar
func adminUploadUserAvatar(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	// Get the uploaded file
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		log.Printf("adminUploadUserAvatar: Failed to get file: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file uploaded"})
		return
	}
	defer file.Close()

	// Validate file type
	if !strings.HasPrefix(header.Header.Get("Content-Type"), "image/") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File must be an image"})
		return
	}

	// Validate file size (max 5MB)
	if header.Size > 5*1024*1024 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File size must be less than 5MB"})
		return
	}

	// Generate unique filename
	ext := filepath.Ext(header.Filename)
	if ext == "" {
		ext = ".jpg"
	}
	timestamp := time.Now().Unix()
	key := fmt.Sprintf("avatars/%s_%d%s", userID, timestamp, ext)

	// Upload to S3
	_, err = s3Client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(s3Bucket),
		Key:         aws.String(key),
		Body:        file,
		ContentType: aws.String(header.Header.Get("Content-Type")),
	})
	if err != nil {
		log.Printf("adminUploadUserAvatar: S3 upload failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload file"})
		return
	}

	// Generate S3 URL
	region := s3Region
	if region == "" {
		region = "us-east-1"
	}
	url := fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", s3Bucket, region, key)

	// Update user's avatar_url in database
	if _, err := db.Exec("UPDATE users SET avatar_url = ?, updated_at = NOW() WHERE username = ?", url, userID); err != nil {
		log.Printf("adminUploadUserAvatar: DB update failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save avatar URL"})
		return
	}

	log.Printf("Admin uploaded avatar for user '%s': %s", userID, url)
	c.JSON(http.StatusOK, gin.H{"message": "Avatar uploaded successfully", "url": url})
}

// Admin function to upload user banner
func adminUploadUserBanner(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	// Get the uploaded file
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		log.Printf("adminUploadUserBanner: Failed to get file: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file uploaded"})
		return
	}
	defer file.Close()

	// Validate file type
	if !strings.HasPrefix(header.Header.Get("Content-Type"), "image/") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File must be an image"})
		return
	}

	// Validate file size (max 10MB)
	if header.Size > 10*1024*1024 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File size must be less than 10MB"})
		return
	}

	// Generate unique filename
	ext := filepath.Ext(header.Filename)
	if ext == "" {
		ext = ".jpg"
	}
	timestamp := time.Now().Unix()
	key := fmt.Sprintf("banners/%s_%d%s", userID, timestamp, ext)

	// Upload to S3
	_, err = s3Client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(s3Bucket),
		Key:         aws.String(key),
		Body:        file,
		ContentType: aws.String(header.Header.Get("Content-Type")),
	})
	if err != nil {
		log.Printf("adminUploadUserBanner: S3 upload failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload file"})
		return
	}

	// Generate S3 URL
	region := s3Region
	if region == "" {
		region = "us-east-1"
	}
	url := fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", s3Bucket, region, key)

	// Update user's banner_url in database
	if _, err := db.Exec("UPDATE users SET banner_url = ?, updated_at = NOW() WHERE username = ?", url, userID); err != nil {
		log.Printf("adminUploadUserBanner: DB update failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save banner URL"})
		return
	}

	log.Printf("Admin uploaded banner for user '%s': %s", userID, url)
	c.JSON(http.StatusOK, gin.H{"message": "Banner uploaded successfully", "url": url})
}

// Admin function to update user profile (bio, display name, etc.)
func adminUpdateUserProfile(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	var req struct {
		DisplayName  string `json:"display_name"`
		Bio          string `json:"bio"`
		RemoveAvatar bool   `json:"remove_avatar"`
		RemoveBanner bool   `json:"remove_banner"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Validate bio length
	if len(req.Bio) > 300 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Bio must be 300 characters or less"})
		return
	}

	// Build update query dynamically
	var setParts []string
	var args []interface{}

	if req.DisplayName != "" {
		setParts = append(setParts, "display_name = ?")
		args = append(args, req.DisplayName)
	}

	setParts = append(setParts, "bio = ?")
	args = append(args, req.Bio)

	if req.RemoveAvatar {
		setParts = append(setParts, "avatar_url = NULL")
	}

	if req.RemoveBanner {
		setParts = append(setParts, "banner_url = NULL")
	}

	setParts = append(setParts, "updated_at = NOW()")
	args = append(args, userID)

	query := fmt.Sprintf("UPDATE users SET %s WHERE username = ?", strings.Join(setParts, ", "))

	if _, err := db.Exec(query, args...); err != nil {
		log.Printf("adminUpdateUserProfile: DB update failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user profile"})
		return
	}

	log.Printf("Admin updated profile for user '%s': displayName='%s', bio='%s', removeAvatar=%v, removeBanner=%v",
		userID, req.DisplayName, req.Bio, req.RemoveAvatar, req.RemoveBanner)
	c.JSON(http.StatusOK, gin.H{"message": "User profile updated successfully"})
}

