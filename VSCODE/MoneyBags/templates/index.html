<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{.siteName}} - {{.title}}</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
  <link href="/static/css/style.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
</head>
<body>
  <!-- Alert Container -->
  <div id="alertContainer" class="alert-container"></div>

  {{template "header.html" .}}

  <div class="d-flex flex-grow-1 overflow-hidden">
    {{template "sidebar.html" .}}

    <main class="flex-grow-1 overflow-y-auto">
      {{if ne .pageType "browse"}}
      <!-- Video Slideshow Banner Section -->
      <div class="video-slideshow-container" id="videoSlideshowContainer">
        <div class="video-slideshow" id="videoSlideshow">
          <!-- Slides will be populated by JavaScript -->
        </div>

        <!-- Navigation Controls -->
        <div class="slideshow-controls">
          <button class="slideshow-btn prev-btn" id="prevSlide" aria-label="Previous slide">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="15,18 9,12 15,6"></polyline>
            </svg>
          </button>
          <button class="slideshow-btn next-btn" id="nextSlide" aria-label="Next slide">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="9,18 15,12 9,6"></polyline>
            </svg>
          </button>
        </div>

        <!-- Slide Indicators -->
        <div class="slideshow-indicators" id="slideshowIndicators">
          <!-- Indicators will be populated by JavaScript -->
        </div>

        <!-- Loading State -->
        <div class="slideshow-loading" id="slideshowLoading">
          <div class="loading-spinner"></div>
          <p>Loading featured content...</p>
        </div>
      </div>
      {{end}}
      <!-- Top Live Categories Section / Browse VODs Section -->
      <div class="container py-3 py-md-4">
        <div class="mb-4">
          {{if eq .pageType "browse"}}
          {{if .categoryFilter}}
          <h2 class="h5 mb-2">{{.categoryFilter}} VODs</h2>
          <p class="text-muted">Browse {{.categoryFilter}} recorded streams from our community</p>
          {{else}}
          <h2 class="h5 mb-2">All VODs</h2>
          <p class="text-muted">Browse recorded streams from our community</p>
          {{end}}
          {{else}}
          <h2 class="h5 mb-2">Top Live Categories</h2>
          <p class="text-muted">The most popular betting categories right now</p>
          {{end}}
        </div>
        {{if eq .pageType "browse"}}
        <!-- VODs Grid for Browse Page (using profile page style) -->
        <div class="vod-grid" id="featuredStreams">
          <!-- VODs will be populated by JavaScript -->
        </div>
        {{else}}
        <!-- Categories for Home Page -->
        <div class="row row-cols-2 row-cols-sm-3 row-cols-lg-6 g-4" id="topCategories">
          <!-- Categories will be populated by JavaScript -->
        </div>
        {{end}}
      </div>

      {{if ne .pageType "browse"}}
      <!-- VOD Categories Section (only show on home page) -->
      <div class="container py-3 py-md-4">
        <div class="mb-4">
          <h2 class="h5 mb-2">Recent VODs by Category</h2>
          <p class="text-muted">Latest recorded streams organized by category</p>
        </div>
        <div id="vodCategories">
          <!-- VOD category sections will be populated by JavaScript -->
        </div>
      </div>

      <!-- Live Streams by Category (only show on home page) -->
      <div id="categoryStreams">
        <!-- Category sections will be populated by JavaScript -->
      </div>
      {{end}}
    </main>
  </div>

    <!-- Mobile User Section (shown when logged in) -->
    <div class="d-none" id="mobileUserSection">
      <div class="border-top pt-3 mt-3">
        <div class="d-flex align-items-center mb-3">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user me-2" style="width: 1.5rem; height: 1.5rem;">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
          <span id="mobileUsername">Username</span>
        </div>
        <div class="d-grid gap-2">
          <a href="/creator/dashboard" class="btn btn-outline-secondary text-start mobile-nav-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-video me-2" style="width: 1rem; height: 1rem;">
              <path d="m22 8-6 4 6 4V8Z"></path>
              <rect width="14" height="12" x="2" y="2" rx="2" ry="2"></rect>
            </svg>Creator Dashboard
          </a>
          <a href="/settings" class="btn btn-outline-secondary text-start mobile-nav-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-settings me-2" style="width: 1rem; height: 1rem;">
              <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
              <circle cx="12" cy="12" r="3"></circle>
            </svg>Settings
          </a>
          <button class="btn btn-outline-danger text-start mobile-nav-btn" id="mobileLogoutBtn">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-log-out me-2" style="width: 1rem; height: 1rem;">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16,17 21,12 16,7"></polyline>
              <line x1="21" y1="12" x2="9" y2="12"></line>
            </svg>Log Out
          </button>
        </div>
      </div>
    </div>
    <script src="/static/js/main.js?v=1758642000"></script>
  </body>
</html>
