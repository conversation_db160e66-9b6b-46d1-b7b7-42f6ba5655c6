<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - {{.siteName}}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
    <link href="/static/css/admin.css" rel="stylesheet">
</head>
<body class="admin-body">
    <!-- Alert Container -->
    <div id="alertContainer" class="alert-container"></div>

    <!-- Admin Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin/dashboard">
                <i class="bi bi-shield-lock-fill me-2"></i>
                {{.siteName}} Admin
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        <span id="adminUserName">Admin</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/" target="_blank">
                            <i class="bi bi-house me-2"></i>View Site
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><button class="dropdown-item" id="adminLogout">
                            <i class="bi bi-box-arrow-right me-2"></i>Logout
                        </button></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="admin-container">
        <div class="row">
            <!-- Admin Sidebar -->
            <div class="col-lg-2 col-md-3">
                <div class="admin-sidebar">
                    <div class="nav flex-column nav-pills" id="adminTab" role="tablist" aria-orientation="vertical">
                        <button class="nav-link active" id="dashboard-tab" data-bs-toggle="pill" data-bs-target="#dashboard" type="button" role="tab">
                            <i class="bi bi-speedometer2 me-2"></i>Dashboard
                        </button>
                        <button class="nav-link" id="users-tab" data-bs-toggle="pill" data-bs-target="#users" type="button" role="tab">
                            <i class="bi bi-people me-2"></i>Users
                        </button>
                        <button class="nav-link" id="categories-tab" data-bs-toggle="pill" data-bs-target="#categories" type="button" role="tab">
                            <i class="bi bi-grid-3x3-gap me-2"></i>Categories
                        </button>
                        <button class="nav-link" id="streams-tab" data-bs-toggle="pill" data-bs-target="#streams" type="button" role="tab">
                            <i class="bi bi-play-circle me-2"></i>Streams
                        </button>
                        <button class="nav-link" id="videos-tab" data-bs-toggle="pill" data-bs-target="#videos" type="button" role="tab">
                            <i class="bi bi-camera-video me-2"></i>Videos
                        </button>
                    </div>
                </div>
            </div>

            <!-- Admin Content -->
            <div class="col-lg-10 col-md-9">
                <div class="admin-content">
                    <div class="tab-content" id="adminTabContent">
                        <!-- Dashboard Tab -->
                        <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                            <div class="admin-section">
                                <h4 class="admin-section-title">Dashboard Overview</h4>
                                
                                <div class="row mb-4">
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="admin-stat-card">
                                            <div class="stat-icon bg-primary">
                                                <i class="bi bi-people"></i>
                                            </div>
                                            <div class="stat-info">
                                                <h5 id="totalUsers">0</h5>
                                                <p>Total Users</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="admin-stat-card">
                                            <div class="stat-icon bg-success">
                                                <i class="bi bi-grid-3x3-gap"></i>
                                            </div>
                                            <div class="stat-info">
                                                <h5 id="totalCategories">0</h5>
                                                <p>Categories</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="admin-stat-card">
                                            <div class="stat-icon bg-warning">
                                                <i class="bi bi-play-circle"></i>
                                            </div>
                                            <div class="stat-info">
                                                <h5 id="totalStreams">0</h5>
                                                <p>Active Streams</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="admin-stat-card">
                                            <div class="stat-icon bg-info">
                                                <i class="bi bi-camera-video"></i>
                                            </div>
                                            <div class="stat-info">
                                                <h5 id="totalVideos">0</h5>
                                                <p>Videos</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="admin-chart-card">
                                            <h6>Recent Activity</h6>
                                            <div class="activity-list" id="recentActivity">
                                                <div class="activity-item">
                                                    <i class="bi bi-person-plus text-success"></i>
                                                    <span>New user registered</span>
                                                    <small class="text-muted">2 hours ago</small>
                                                </div>
                                                <div class="activity-item">
                                                    <i class="bi bi-play-circle text-primary"></i>
                                                    <span>New stream started</span>
                                                    <small class="text-muted">4 hours ago</small>
                                                </div>
                                                <div class="activity-item">
                                                    <i class="bi bi-grid-3x3-gap text-warning"></i>
                                                    <span>Category updated</span>
                                                    <small class="text-muted">1 day ago</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="admin-chart-card">
                                            <h6>System Status</h6>
                                            <div class="status-list">
                                                <div class="status-item">
                                                    <span class="status-indicator bg-success"></span>
                                                    <span>Database Connection</span>
                                                    <span class="badge bg-success">Online</span>
                                                </div>
                                                <div class="status-item">
                                                    <span class="status-indicator bg-success"></span>
                                                    <span>AWS Cognito</span>
                                                    <span class="badge bg-success">Connected</span>
                                                </div>
                                                <div class="status-item">
                                                    <span class="status-indicator bg-success"></span>
                                                    <span>File Storage</span>
                                                    <span class="badge bg-success">Available</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Users Tab -->
                        <div class="tab-pane fade" id="users" role="tabpanel">
                            <div class="admin-section">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h4 class="admin-section-title">User Management</h4>
                                    <div class="admin-actions">
                                        <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#createUserModal">
                                            <i class="bi bi-person-plus me-1"></i>Create User
                                        </button>
                                        <button class="btn btn-outline-primary" id="refreshUsers">
                                            <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                                        </button>
                                    </div>
                                </div>

                                <div class="admin-table-container">
                                    <table class="table table-striped admin-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Username</th>
                                                <th>Email</th>
                                                <th>Status</th>
                                                <th>Joined</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="usersTableBody">
                                            <tr>
                                                <td colspan="6" class="text-center text-muted">Loading users...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Categories Tab -->
                        <div class="tab-pane fade" id="categories" role="tabpanel">
                            <div class="admin-section">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h4 class="admin-section-title">Category Management</h4>
                                    <div class="admin-actions">
                                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                            <i class="bi bi-plus-lg me-1"></i>Add Category
                                        </button>
                                    </div>
                                </div>

                                <div class="admin-table-container">
                                    <table class="table table-striped admin-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Photo</th>
                                                <th>Name</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="categoriesTableBody">
                                            <tr>
                                                <td colspan="5" class="text-center text-muted">Loading categories...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Streams Tab -->
                        <div class="tab-pane fade" id="streams" role="tabpanel">
                            <div class="admin-section">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h4 class="admin-section-title">Stream Management</h4>
                                    <div class="admin-actions">
                                        <button class="btn btn-outline-primary" id="refreshStreams">
                                            <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                                        </button>
                                    </div>
                                </div>

                                <div class="admin-table-container">
                                    <table class="table table-striped admin-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Title</th>
                                                <th>Streamer</th>
                                                <th>Category</th>
                                                <th>Viewers</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="streamsTableBody">
                                            <tr>
                                                <td colspan="7" class="text-center text-muted">Loading streams...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Videos Tab -->
                        <div class="tab-pane fade" id="videos" role="tabpanel">
                            <div class="admin-section">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h4 class="admin-section-title">Video Management</h4>
                                    <div class="admin-actions">
                                        <button class="btn btn-outline-primary" id="refreshVideos">
                                            <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                                        </button>
                                    </div>
                                </div>

                                <div class="admin-table-container">
                                    <table class="table table-striped admin-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Title</th>
                                                <th>Creator</th>
                                                <th>Category</th>
                                                <th>Duration</th>
                                                <th>Views</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="videosTableBody">
                                            <tr>
                                                <td colspan="7" class="text-center text-muted">Loading videos...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Category Modal -->
    <div class="modal fade" id="addCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addCategoryForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="categoryName" class="form-label">Category Name</label>
                            <input type="text" class="form-control" id="categoryName" required>
                        </div>
                        <div class="mb-3">
                            <label for="categoryDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="categoryDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="categoryPhoto" class="form-label">Category Photo</label>
                            <input type="file" class="form-control" id="categoryPhoto" accept="image/*">
                            <div class="form-text">Upload an image to represent this category (optional)</div>
                            <div id="s3Warning" class="alert alert-warning mt-2" style="display: none;">
                                <small><strong>Note:</strong> Photo upload requires S3 configuration. Categories can be created without photos.</small>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="categoryEnabled" checked>
                                <label class="form-check-label" for="categoryEnabled">
                                    Enable this category
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveCategoryBtn">Save Category</button>
                </div>
            </div>
        </div>
    </div>

    <!-- User Details Modal -->
    <div class="modal fade" id="userDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">User Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userDetailsForm">
                        <!-- Profile Images Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-muted mb-3">Profile Images</h6>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Profile Picture</label>
                                    <div class="profile-image-upload">
                                        <div id="adminUserAvatarPreview" class="avatar-preview">
                                            <img src="/static/images/default-avatar.png" alt="Profile Picture" class="profile-img">
                                            <div class="upload-overlay">
                                                <i class="bi bi-camera"></i>
                                            </div>
                                        </div>
                                        <input type="file" id="adminUserAvatarUpload" accept="image/*" style="display: none;">
                                        <div class="mt-2">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="document.getElementById('adminUserAvatarUpload').click()">
                                                <i class="bi bi-upload me-1"></i>Upload Avatar
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="removeUserAvatar()">
                                                <i class="bi bi-trash me-1"></i>Remove
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Banner Image</label>
                                    <div class="banner-image-upload">
                                        <div id="adminUserBannerPreview" class="banner-preview">
                                            <div class="banner-placeholder">
                                                <i class="bi bi-image"></i>
                                                <p>Click to upload banner</p>
                                            </div>
                                        </div>
                                        <input type="file" id="adminUserBannerUpload" accept="image/*" style="display: none;">
                                        <div class="mt-2">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="document.getElementById('adminUserBannerUpload').click()">
                                                <i class="bi bi-upload me-1"></i>Upload Banner
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="removeUserBanner()">
                                                <i class="bi bi-trash me-1"></i>Remove
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- User Information Section -->
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-muted mb-3">User Information</h6>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="userUsername" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="userUsername" readonly>
                                </div>
                                <div class="mb-3">
                                    <label for="userEmail" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="userEmail" name="email">
                                </div>
                                <div class="mb-3">
                                    <label for="userDisplayName" class="form-label">Display Name</label>
                                    <input type="text" class="form-control" id="userDisplayName" name="display_name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="userPhoneNumber" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="userPhoneNumber" name="phone_number">
                                </div>
                                <div class="mb-3">
                                    <label for="userStatus" class="form-label">Status</label>
                                    <input type="text" class="form-control" id="userStatus" readonly>
                                </div>
                                <div class="mb-3">
                                    <label for="userCreatedAt" class="form-label">Joined</label>
                                    <input type="text" class="form-control" id="userCreatedAt" readonly>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="userBio" class="form-label">Bio</label>
                                    <textarea class="form-control" id="userBio" name="bio" rows="3" maxlength="300" placeholder="User bio..."></textarea>
                                    <div class="form-text">
                                        <span id="userBioCounter">0</span>/300 characters
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveUserDetails()">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Category Modal -->
    <div class="modal fade" id="editCategoryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editCategoryForm" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editCategoryName" class="form-label">Category Name</label>
                                    <input type="text" class="form-control" id="editCategoryName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="editCategoryDescription" class="form-label">Description</label>
                                    <textarea class="form-control" id="editCategoryDescription" rows="3"></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editCategoryPhoto" class="form-label">Category Photo</label>
                                    <input type="file" class="form-control" id="editCategoryPhoto" accept="image/*">
                                    <div class="form-text">Upload a new image to replace the current photo (optional)</div>
                                    <div id="editS3Warning" class="alert alert-warning mt-2" style="display: none;">
                                        <small><strong>Note:</strong> Photo upload requires S3 configuration. Other changes can be saved without photos.</small>
                                    </div>
                                </div>
                                <div class="mb-3" id="currentPhotoContainer" style="display: none;">
                                    <label class="form-label">Current Photo</label>
                                    <div>
                                        <img id="currentPhoto" src="" alt="Current category photo"
                                             style="max-width: 150px; max-height: 150px; object-fit: cover; border-radius: 8px;">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="editCategoryEnabled">
                                        <label class="form-check-label" for="editCategoryEnabled">
                                            Enable this category
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveEditCategoryBtn">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create User Modal -->
    <div class="modal fade" id="createUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createUserForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="newUserUsername" class="form-label">Username *</label>
                                    <input type="text" class="form-control" id="newUserUsername" required>
                                    <div class="form-text">Must be unique and contain only letters, numbers, and underscores</div>
                                </div>
                                <div class="mb-3">
                                    <label for="newUserEmail" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="newUserEmail" required>
                                </div>
                                <div class="mb-3">
                                    <label for="newUserPassword" class="form-label">Temporary Password *</label>
                                    <input type="password" class="form-control" id="newUserPassword" required>
                                    <div class="form-text">User will be required to change this on first login</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="newUserDisplayName" class="form-label">Display Name</label>
                                    <input type="text" class="form-control" id="newUserDisplayName">
                                </div>
                                <div class="mb-3">
                                    <label for="newUserPhoneNumber" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="newUserPhoneNumber">
                                    <div class="form-text">Format: +1234567890</div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="newUserSendWelcome" checked>
                                        <label class="form-check-label" for="newUserSendWelcome">
                                            Send welcome email
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveNewUserBtn">Create User</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload VOD Modal -->
    <div class="modal fade" id="uploadVodModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Upload VOD for User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="uploadVodForm" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vodUsername" class="form-label">Upload for User *</label>
                                    <input type="text" class="form-control" id="vodUsername" readonly>
                                    <div class="form-text">VOD will be uploaded on behalf of this user</div>
                                </div>
                                <div class="mb-3">
                                    <label for="vodTitle" class="form-label">Video Title *</label>
                                    <input type="text" class="form-control" id="vodTitle" required>
                                </div>
                                <div class="mb-3">
                                    <label for="vodDescription" class="form-label">Description</label>
                                    <textarea class="form-control" id="vodDescription" rows="3"></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vodCategory" class="form-label">Category *</label>
                                    <select class="form-select" id="vodCategory" required>
                                        <option value="">Select a category...</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="vodFile" class="form-label">Video File *</label>
                                    <input type="file" class="form-control" id="vodFile" accept="video/*" required>
                                    <div class="form-text">Max file size: 2GB. Supported formats: MP4, AVI, MOV, etc.</div>
                                </div>
                                <div class="mb-3">
                                    <label for="vodThumbnail" class="form-label">Thumbnail Image</label>
                                    <input type="file" class="form-control" id="vodThumbnail" accept="image/*">
                                    <div class="form-text">Optional. Max file size: 10MB. Supported formats: JPG, PNG, GIF, etc.</div>
                                    <div id="thumbnailPreview" class="mt-2" style="display: none;">
                                        <img id="thumbnailPreviewImg" src="" alt="Thumbnail Preview" style="max-width: 200px; max-height: 120px; border-radius: 8px; border: 2px solid #e9ecef;">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="vodPublic" checked>
                                        <label class="form-check-label" for="vodPublic">
                                            Make video public
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div id="vodUploadProgress" class="progress mb-3" style="display: none;">
                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <div id="vodUploadStatus" class="alert" style="display: none;"></div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="cancelVodBtn">Cancel</button>
                    <button type="button" class="btn btn-danger" id="abortVodBtn" style="display: none;">Cancel Upload</button>
                    <button type="button" class="btn btn-primary" id="uploadVodBtn">Upload VOD</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/admin.js"></script>
</body>
</html>
