
:root {
    --primary-color: #f72c42;
    --primary-hover: #d62438;
    --light-bg: #f8f9fa;
    --dark-text: #212529;
    --border-color: #dee2e6;
    --sidebar-bg: #ffffff;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Global Styles */
body {
    font-size: .8rem;
}

h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
}

/* Logo and Branding */
.logo-svg {
    height: 2rem;
    width: auto;
}

.logo-text {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    letter-spacing: -0.5px;
    font-size: 1.25rem;
    transform: translateY(2px);
}

/* Pulse Animation */
.pulse-dot {
    width: 0.5rem;
    height: 0.5rem;
    background-color: var(--primary-color);
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

header .pulse-dot {
    position: absolute;
    top: 0.25rem;
    right: -0.5rem;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.5); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

/* Button Overrides */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-following {
    background-color: rgba(247, 44, 66, 0.1);
    color: var(--primary-color);
}

.btn-following:hover {
    background-color: rgba(247, 44, 66, 0.2);
}

.btn-sm {
    --bs-btn-padding-y: .25rem;
    --bs-btn-padding-x: .5rem;
    --bs-btn-font-size: .8rem;
    --bs-btn-font-weight: 600;
}
.btn {
   --bs-btn-font-size: 0.8rem; 
}
.form-control, .form-select, .dropdown-item {
    --bs-form-control-font-size: 0.8rem;
    --bs-form-input-font-size: 0.8rem;
    font-size: 0.8rem;
}
.streamingvideo {
    width: calc(100vw - 380px);
}
/* Sidebar Styles */
.sidebar {
    width: 15rem;
    background-color: var(--light-bg);
    border-right: 1px solid var(--border-color);
    height: 100vh;
    position: fixed;
    top: 49px;
    left: 0;
    transform: translateX(-100%);
    transition: transform 0.2s ease-in-out;
    z-index: 1000;
}

.sidebar.show {
    transform: translateX(0);
}

@media (min-width: 1200px) {
    .sidebar {
        transform: translateX(0);
    }
    .streamingvideo {
        width: calc(100vw - 620px);
    }
    main {
        margin-left: 240px;
    }
}

.category-list {
    height: calc(100vh - 165px);
    overflow-y: auto;
    padding-bottom: 1rem;
}

/* Typography */
.text-xs {
    font-size: .75rem;
}

/* Utility Classes */
.rounded-lg {
    border-radius: 15px;
}

.maincount {
    width: 25px;
    height: 25px;
}

/* Status and Interactive Elements */
.status-dot {
    width: 0.75rem;
    height: 0.75rem;
    background-color: #28a745;
    border-radius: 50%;
    position: absolute;
    bottom: -0.125rem;
    right: -0.125rem;
}

.channel-item:hover {
    background-color: #e9ecef;
}

/* Badge Styles */
.badge {
    --bs-badge-font-size: 0.9em;
    --bs-badge-font-weight: 400;
}

/* Video Slideshow Banner */
.video-slideshow-container {
    height: 20rem;
    position: relative;
    overflow: hidden;
    background: #000;
}

@media (min-width: 768px) {
    .video-slideshow-container {
        height: 24rem;
    }
}

@media (min-width: 992px) {
    .video-slideshow-container {
        height: 28rem;
    }
}

.video-slideshow {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    transition: transform 0.5s ease-in-out;
}

.slideshow-slide {
    min-width: 100%;
    height: 100%;
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
}

.slideshow-slide::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.3));
    z-index: 1;
}

.slideshow-content {
    position: relative;
    z-index: 2;
    color: white;
    padding: 2rem;
    max-width: 50%;
}

.slideshow-content h1 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.slideshow-content p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.slideshow-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.slideshow-live-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.slideshow-pulse-dot {
    width: 8px;
    height: 8px;
    background: #dc3545;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.slideshow-tags {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.slideshow-btn-primary {
    background: #f72c42;
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 0.375rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.2s;
}

.slideshow-btn-primary:hover {
    background: #d91e36;
    transform: translateY(-1px);
}

/* Navigation Controls */
.slideshow-controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 1rem;
    z-index: 3;
    pointer-events: none;
}

.slideshow-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    pointer-events: auto;
    backdrop-filter: blur(10px);
}

.slideshow-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.slideshow-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Slide Indicators */
.slideshow-indicators {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
    z-index: 3;
}

.slideshow-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    border: none;
    cursor: pointer;
    transition: all 0.2s;
}

.slideshow-indicator.active {
    background: #f72c42;
    transform: scale(1.2);
}

.slideshow-indicator:hover {
    background: rgba(255, 255, 255, 0.6);
}

/* Loading State */
.slideshow-loading {
    position: absolute;
    inset: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #000;
    color: white;
    z-index: 4;
}

.slideshow-loading.hidden {
    display: none;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #f72c42;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .slideshow-content {
        max-width: 80%;
        padding: 1.5rem;
    }

    .slideshow-content h1 {
        font-size: 2rem;
    }

    .slideshow-content p {
        font-size: 1rem;
    }

    .slideshow-controls {
        padding: 0 0.5rem;
    }

    .slideshow-btn {
        width: 40px;
        height: 40px;
    }
}

/* Category Cards */
.category-card {
    transition: transform 0.3s ease;
}

.category-card:hover {
    transform: scale(1.05);
}

.category-img {
    aspect-ratio: 3/4;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.category-card:hover .category-img {
    transform: scale(1.1);
}

.category-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.2), transparent);
}

/* Stream Cards */
.stream-card {
    transition: transform 0.2s ease;
}

.stream-card:hover {
    transform: scale(1.05);
}

.stream-img {
    aspect-ratio: 16/9;
    object-fit: cover;
    transition: transform 0.2s ease;
}

.stream-card:hover .stream-img {
    transform: scale(1.05);
}

/* Live badge */
.live-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 4px;
    z-index: 2;
}

/* User's own stream badge */
.user-stream-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 4px;
    z-index: 2;
}

/* Current user stream card styling */
.current-user-stream {
    border: 2px solid #28a745;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

.current-user-stream:hover {
    border-color: #218838;
    box-shadow: 0 6px 16px rgba(40, 167, 69, 0.3);
}

/* VOD badge */
.vod-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #6c757d;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 4px;
    z-index: 2;
}

/* Duration badge */
.duration-badge {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: 500;
    z-index: 2;
}

/* Live dot animation */
.live-dot {
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Streaming Styles */
.stream-container {
    height: calc(100vh - 50px);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

@media (min-width: 992px) {
    .stream-container {
        flex-direction: row;
    }
}

.stream-video {
    aspect-ratio: 16/9;
    background-color: #000;
    position: relative;
    overflow: hidden;
}

.stream-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
}

/* Chat Styles */
.chat-sidebar {
    min-width: 380px;
    width: 380px;
    height: calc(100vh - 50px);
    border-left: 1px solid var(--border-color);
    background-color: #fff;
}

.chat-mobile {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30vh;
    background-color: var(--light-bg);
    border-top: 1px solid var(--border-color);
    transition: height 0.3s ease;
}

.chat-mobile.expanded {
    height: 50vh;
}

.nav-tabs .nav-link {
    border-radius: 0.75rem;
    margin: 0 0.1rem;
}

.nav-tabs .nav-link.active {
    background-color: #fff;
    border-color: var(--border-color);
    color: var(--dark-text);
}

.chat-scroll {
    max-height: calc(100% - 8rem);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.chat-scroll::-webkit-scrollbar {
    display: none;
}

/* Alert Container */
.alert-container {
    position: fixed;
    top: 70px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

/* Settings Page Styles */
.settings-sidebar {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.settings-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

/* Horizontal Settings Tabs */
#settingsTab .nav-link {
    color: #666;
    border: 1px solid transparent;
    border-radius: 8px 8px 0 0;
    padding: 0.75rem 1.5rem;
    margin-right: 0.25rem;
    transition: all 0.2s ease;
    font-weight: 500;
}

#settingsTab .nav-link:hover {
    background-color: #f8f9fa;
    color: #333;
    border-color: #dee2e6;
}

#settingsTab .nav-link.active {
    background-color: white;
    color: var(--primary-color);
    border-color: #dee2e6 #dee2e6 white;
    border-bottom-color: white;
}

/* Settings tabs container */
#settingsTab {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 2rem;
}

.settings-content {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-top: none;
    border-top-left-radius: 0;
}

.settings-section {
    max-width: none;
}

.section-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 2rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #f8f9fa;
}

.subsection-title {
    color: #555;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.security-subsection,
.notification-subsection {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 1.5rem;
}

.security-subsection:last-child,
.notification-subsection:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

/* Profile Banner Upload */
.banner-upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    transition: border-color 0.2s ease;
}

.banner-upload-area:hover {
    border-color: var(--primary-color);
}

.banner-preview {
    height: 200px;
    background: #f8f9fa;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    overflow: hidden;
}

.banner-placeholder {
    color: #999;
    text-align: center;
}

.banner-placeholder i {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    display: block;
}

/* Smaller banner placeholder for admin interface */
#adminUserBannerPreview .banner-placeholder i {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
}

#adminUserBannerPreview .banner-placeholder p {
    font-size: 0.8rem;
    margin: 0;
}

.banner-controls {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

/* Profile Picture Upload */
.profile-picture-preview {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.profile-picture-preview:hover {
    transform: scale(1.05);
}

.profile-picture-preview .profile-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
    color: white;
    font-size: 1.5rem;
}

.profile-picture-preview:hover .profile-overlay {
    opacity: 1;
}

/* Admin Panel Image Uploads */
.profile-image-upload {
    text-align: center;
}

.avatar-preview {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    margin: 0 auto 1rem;
    border: 2px solid #e9ecef;
    transition: transform 0.2s ease;
}

.avatar-preview:hover {
    transform: scale(1.05);
}

.avatar-preview .profile-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.avatar-preview:hover .upload-overlay {
    opacity: 1;
}

.upload-overlay i {
    color: white;
    font-size: 1.2rem;
}

.banner-image-upload {
    text-align: center;
}

.banner-preview {
    height: 80px;
    background: #f8f9fa;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    overflow: hidden;
    border: 2px dashed #ddd;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.banner-preview:hover {
    border-color: var(--primary-color);
}

.banner-preview.has-image {
    border: 2px solid #e9ecef;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

/* Form Enhancements */
.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(247, 44, 66, 0.25);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(247, 44, 66, 0.25);
}

/* Privacy Settings */
.privacy-setting,
.notification-setting {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 1rem;
}

.privacy-setting:last-child,
.notification-setting:last-child {
    margin-bottom: 0;
}

/* Blocked Users List */
.blocked-users-list {
    min-height: 100px;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive Design for Settings */
@media (max-width: 768px) {
    #settingsTab .nav-link {
        padding: 0.5rem 1rem;
        margin-right: 0.125rem;
        font-size: 0.875rem;
    }

    .settings-content {
        padding: 1.5rem;
    }

    .banner-controls {
        flex-direction: column;
        align-items: center;
    }

    .banner-controls .btn {
        width: 100%;
        max-width: 200px;
    }
}

@media (max-width: 576px) {
    #settingsTab .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    #settingsTab .nav-link i {
        display: none;
    }
}

/* VOD Clickable Styles */
.vod-clickable {
    transition: all 0.2s ease;
    border-radius: 8px;
}

.vod-clickable:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.vod-clickable:hover .bi-play-circle {
    color: #0d6efd !important;
    transform: scale(1.1);
}

.vod-clickable .bi-play-circle {
    transition: all 0.2s ease;
    font-size: 1.2em;
}

/* VOD Status Styles */
.vod-clickable[style*="cursor: not-allowed"] {
    opacity: 0.6;
}

.vod-clickable[style*="cursor: not-allowed"]:hover {
    transform: none;
    box-shadow: none;
    background-color: transparent;
}

/* Quick Action Cards */
.quick-action-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.quick-action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: var(--primary-color);
}

.quick-action-icon {
    transition: all 0.3s ease;
}

.quick-action-card:hover .quick-action-icon i {
    transform: scale(1.1);
}

.quick-action-card .btn {
    transition: all 0.2s ease;
}

.quick-action-card:hover .btn-primary {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}
.profile-banner {
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: cover;
    background-position: center;
    position: relative;
    border-radius: 0 0 15px 15px;
}

.profile-banner.has-banner {
    background-image: var(--banner-url);
}
a#logo {
    text-decoration: none;
    color: #000;
}

.profile-info {
    position: relative;
    margin-top: -60px;
    z-index: 10;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid white;
    background: #f8f9fa;
    object-fit: cover;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.profile-details {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-top: 15px;
}

.profile-stats {
    display: flex;
    gap: 30px;
    margin-top: 15px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
}

.vod-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

/* Category Title Styles */
.category-title:hover {
    color: #f72c42 !important;
    text-decoration: underline;
}

.vod-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
}

.vod-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.vod-thumbnail {
    width: 100%;
    height: 160px;
    object-fit: cover;
    background: #f8f9fa;
}

.vod-info {
    padding: 15px;
}

.vod-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.vod-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #6c757d;
}

.vod-category {
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
}

.no-vods {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.no-vods i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}


@media (max-width: 768px) {
    .profile-banner {
        height: 150px;
    }
    
    .profile-avatar {
        width: 80px;
        height: 80px;
    }
    
    .profile-info {
        margin-top: -40px;
    }
    
    .profile-stats {
        gap: 20px;
    }
    
    .vod-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }
}

/* Recommended Users Styles */
.recommended-user-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 8px;
    border-radius: 8px;
    background: #f8f9fa;
    transition: all 0.2s ease;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
}

.recommended-user-item:hover {
    background: #e9ecef;
    transform: translateX(4px);
    text-decoration: none;
    color: inherit;
}

.recommended-user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 10px;
    border: 2px solid #dee2e6;
}

.recommended-user-info {
    flex: 1;
    min-width: 0;
}

.recommended-user-name {
    font-size: 0.875rem;
    font-weight: 500;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.recommended-user-stats {
    font-size: 0.75rem;
    color: #6c757d;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.live-indicator {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    color: #dc3545;
    font-weight: 500;
}

.live-dot {
    width: 6px;
    height: 6px;
    background: #dc3545;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.viewer-count {
    color: #6c757d;
}