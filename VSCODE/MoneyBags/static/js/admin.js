// Admin Panel JavaScript

let adminSessionToken = null;

document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on admin login page
    if (document.getElementById('adminLoginForm')) {
        initAdminLogin();
    }
    
    // Check if we're on admin dashboard
    if (document.getElementById('adminTab')) {
        initAdminDashboard();
    }
});

// Initialize admin login page
function initAdminLogin() {
    const loginForm = document.getElementById('adminLoginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleAdminLogin);
    }
}

// Handle admin login
async function handleAdminLogin(e) {
    e.preventDefault();
    
    const username = document.getElementById('adminUsername').value;
    const password = document.getElementById('adminPassword').value;
    
    if (!username || !password) {
        showError('Please fill in all fields');
        return;
    }
    
    try {
        const response = await fetch('/api/admin/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            // Store session token
            adminSessionToken = result.sessionToken;
            localStorage.setItem('adminSessionToken', adminSessionToken);
            localStorage.setItem('adminUser', JSON.stringify(result.user));
            
            showSuccess('Login successful! Redirecting...');
            
            // Redirect to dashboard
            setTimeout(() => {
                window.location.href = '/admin/dashboard';
            }, 1000);
        } else {
            showError(result.error || 'Login failed');
        }
    } catch (error) {
        console.error('Admin login error:', error);
        showError('Login failed. Please try again.');
    }
}

// Initialize admin dashboard
function initAdminDashboard() {
    // Check if user is logged in
    adminSessionToken = localStorage.getItem('adminSessionToken');
    const adminUser = JSON.parse(localStorage.getItem('adminUser') || '{}');
    
    if (!adminSessionToken) {
        window.location.href = '/admin';
        return;
    }
    
    // Update admin username in navbar
    const adminUserName = document.getElementById('adminUserName');
    if (adminUserName && adminUser.username) {
        adminUserName.textContent = adminUser.username;
    }
    
    // Setup event listeners
    setupAdminEventListeners();
    
    // Load initial data
    loadDashboardStats();
    loadUsers();
    loadAdminCategories();
    loadAdminStreams();
}

// Setup admin event listeners
function setupAdminEventListeners() {
    // Logout button
    const logoutBtn = document.getElementById('adminLogout');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleAdminLogout);
    }
    
    // Tab change events
    const tabButtons = document.querySelectorAll('#adminTab button[data-bs-toggle="pill"]');
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(e) {
            const target = e.target.getAttribute('data-bs-target');
            handleTabChange(target);
        });
    });
    
    // Refresh buttons
    const refreshUsers = document.getElementById('refreshUsers');
    if (refreshUsers) {
        refreshUsers.addEventListener('click', loadUsers);
    }
    
    const refreshStreams = document.getElementById('refreshStreams');
    if (refreshStreams) {
        refreshStreams.addEventListener('click', loadAdminStreams);
    }
    
    const refreshVideos = document.getElementById('refreshVideos');
    if (refreshVideos) {
        refreshVideos.addEventListener('click', loadAdminVideos);
    }
    
    // Add category button
    const saveCategoryBtn = document.getElementById('saveCategoryBtn');
    if (saveCategoryBtn) {
        saveCategoryBtn.addEventListener('click', handleAddCategory);
    }

    // Initialize new modals
    initCreateUserModal();
    initUploadVodModal();

    // Initialize admin user profile management
    initAdminUserProfileManagement();
}

// Handle admin logout
function handleAdminLogout() {
    localStorage.removeItem('adminSessionToken');
    localStorage.removeItem('adminUser');
    adminSessionToken = null;
    
    showSuccess('Logged out successfully');
    setTimeout(() => {
        window.location.href = '/admin';
    }, 1000);
}

// Handle tab changes
function handleTabChange(target) {
    switch(target) {
        case '#users':
            loadUsers();
            break;
        case '#categories':
            loadAdminCategories();
            break;
        case '#streams':
            loadAdminStreams();
            break;
        case '#videos':
            loadAdminVideos();
            break;
        case '#dashboard':
            loadDashboardStats();
            break;
    }
}

// Load dashboard statistics
async function loadDashboardStats() {
    try {
        // Load real user count from Cognito
        const usersResponse = await fetch('/api/admin/users', {
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });

        if (usersResponse.ok) {
            const users = await usersResponse.json();
            const totalUsersEl = document.getElementById('totalUsers');
            if (totalUsersEl) {
                if (users && Array.isArray(users)) {
                    totalUsersEl.textContent = users.length.toLocaleString();
                } else {
                    totalUsersEl.textContent = '0';
                }
            }
        } else {
            const totalUsersEl = document.getElementById('totalUsers');
            if (totalUsersEl) totalUsersEl.textContent = 'N/A';
        }

        // Load categories count
        const categoriesResponse = await fetch('/api/admin/categories', {
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });

        if (categoriesResponse.ok) {
            const categories = await categoriesResponse.json();
            const totalCategoriesEl = document.getElementById('totalCategories');
            if (totalCategoriesEl) {
                if (categories && Array.isArray(categories)) {
                    totalCategoriesEl.textContent = categories.length.toLocaleString();
                } else {
                    totalCategoriesEl.textContent = '0';
                }
            }
        } else {
            const totalCategoriesEl = document.getElementById('totalCategories');
            if (totalCategoriesEl) totalCategoriesEl.textContent = 'N/A';
        }

        // Load streams count
        const streamsResponse = await fetch('/api/admin/streams', {
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });

        if (streamsResponse.ok) {
            const streams = await streamsResponse.json();
            const totalStreamsEl = document.getElementById('totalStreams');
            if (totalStreamsEl) {
                if (streams && Array.isArray(streams)) {
                    totalStreamsEl.textContent = streams.length.toLocaleString();
                } else {
                    totalStreamsEl.textContent = '0';
                }
            }
        } else {
            const totalStreamsEl = document.getElementById('totalStreams');
            if (totalStreamsEl) totalStreamsEl.textContent = 'N/A';
        }

        // Videos count
        try {
            const videosResponse = await fetch('/api/admin/videos', {
                headers: {
                    'Authorization': `Bearer ${adminSessionToken}`
                }
            });
            if (videosResponse.ok) {
                const videos = await videosResponse.json();
                const totalVideosEl = document.getElementById('totalVideos');
                if (totalVideosEl) {
                    totalVideosEl.textContent = videos.length.toString();
                }
            } else {
                const totalVideosEl = document.getElementById('totalVideos');
                if (totalVideosEl) totalVideosEl.textContent = 'N/A';
            }
        } catch (error) {
            const totalVideosEl = document.getElementById('totalVideos');
            if (totalVideosEl) totalVideosEl.textContent = 'Error';
        }

    } catch (error) {
        console.error('Error loading dashboard stats:', error);
        // Set fallback values with null checks
        const totalUsersEl = document.getElementById('totalUsers');
        const totalCategoriesEl = document.getElementById('totalCategories');
        const totalStreamsEl = document.getElementById('totalStreams');
        const totalVideosEl = document.getElementById('totalVideos');

        if (totalUsersEl) totalUsersEl.textContent = 'Error';
        if (totalCategoriesEl) totalCategoriesEl.textContent = 'Error';
        if (totalStreamsEl) totalStreamsEl.textContent = 'Error';
        if (totalVideosEl) totalVideosEl.textContent = 'Error';
    }
}

// Load users
async function loadUsers() {
    const tbody = document.getElementById('usersTableBody');
    if (!tbody) return;

    tbody.innerHTML = '<tr><td colspan="6" class="text-center">Loading users from AWS Cognito...</td></tr>';

    try {
        const response = await fetch('/api/admin/users', {
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const users = await response.json();

        tbody.innerHTML = '';

        if (users.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No users found in AWS Cognito</td></tr>';
            return;
        }

        users.forEach(user => {
            const row = document.createElement('tr');
            const joinedDate = new Date(user.created_at).toLocaleDateString();
            const displayName = user.display_name || user.username;

            row.innerHTML = `
                <td>${user.id}</td>
                <td>
                    <div>
                        <strong>${user.username}</strong>
                        ${displayName !== user.username ? `<br><small class="text-muted">${displayName}</small>` : ''}
                    </div>
                </td>
                <td>${user.email || 'N/A'}</td>
                <td><span class="badge status-badge status-${user.status}">${user.status.toUpperCase()}</span></td>
                <td>${joinedDate}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary" onclick="viewUser('${user.id}')">View</button>
                        <button class="btn btn-sm btn-outline-info" onclick="openUploadVodModal('${user.username}')" title="Upload VOD for this user">
                            <i class="bi bi-camera-video"></i> VOD
                        </button>
                        ${user.status === 'active' ?
                            `<button class="btn btn-sm btn-outline-danger" onclick="banUser('${user.id}')">Ban</button>` :
                            `<button class="btn btn-sm btn-outline-success" onclick="unbanUser('${user.id}')">Unban</button>`
                        }
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });

        // Update dashboard stats
        document.getElementById('totalUsers').textContent = users.length;

    } catch (error) {
        console.error('Error loading users:', error);
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading users from Cognito</td></tr>';
        showError('Failed to load users from AWS Cognito: ' + error.message);
    }
}

// Load admin categories
async function loadAdminCategories() {
    const tbody = document.getElementById('categoriesTableBody');
    if (!tbody) return;

    tbody.innerHTML = '<tr><td colspan="5" class="text-center">Loading categories...</td></tr>';

    try {
        const response = await fetch('/api/admin/categories', {
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const categories = await response.json();

        tbody.innerHTML = '';

        if (categories.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">No categories found</td></tr>';
            return;
        }

        categories.forEach(category => {
            const row = document.createElement('tr');
            const photoCell = category.photo_url ?
                `<img src="${category.photo_url}" alt="${category.name}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 6px;">` :
                '<span class="text-muted">No photo</span>';
            const statusBadge = category.is_enabled ?
                '<span class="badge bg-success">Enabled</span>' :
                '<span class="badge bg-secondary">Disabled</span>';

            row.innerHTML = `
                <td><strong>${category.id}</strong></td>
                <td>${photoCell}</td>
                <td><strong>${category.name}</strong></td>
                <td>${statusBadge}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editCategory(${category.id})">Edit</button>
                    <button class="btn btn-sm ${category.is_enabled ? 'btn-outline-warning' : 'btn-outline-success'} me-1"
                            onclick="toggleCategory(${category.id}, ${category.is_enabled})">
                        ${category.is_enabled ? 'Disable' : 'Enable'}
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteCategory(${category.id})">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });

        // Update dashboard stats
        document.getElementById('totalCategories').textContent = categories.length;

    } catch (error) {
        console.error('Error loading categories:', error);
        tbody.innerHTML = '<tr><td colspan="8" class="text-center text-danger">Error loading categories</td></tr>';
        showError('Failed to load categories: ' + error.message);
    }
}

// Load admin streams
async function loadAdminStreams() {
    const tbody = document.getElementById('streamsTableBody');
    if (!tbody) return;

    tbody.innerHTML = '<tr><td colspan="7" class="text-center">Loading streams...</td></tr>';

    try {
        const response = await fetch('/api/admin/streams', {
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const streams = await response.json();

        tbody.innerHTML = '';

        // Check if streams is an array
        if (!streams || !Array.isArray(streams)) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No streams found</td></tr>';
            return;
        }

        if (streams.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No streams available</td></tr>';
            return;
        }

        streams.forEach(stream => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${stream.id}</td>
                <td>${stream.title}</td>
                <td>${stream.name}</td>
                <td>${stream.category}</td>
                <td>${formatViewers(stream.viewers)}</td>
                <td><span class="badge bg-success">Live</span></td>
                <td>
                    <button class="btn btn-outline-danger action-btn" onclick="deleteStream('${stream.id}')">End Stream</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('Error loading streams:', error);
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">Error loading streams</td></tr>';
    }
}

// Load admin videos
async function loadAdminVideos() {
    const tbody = document.getElementById('videosTableBody');
    if (!tbody) return;

    tbody.innerHTML = '<tr><td colspan="7" class="text-center">Loading videos...</td></tr>';

    try {
        const response = await fetch('/api/admin/videos', {
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const videos = await response.json();

        tbody.innerHTML = '';

        if (videos.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No videos found</td></tr>';
            return;
        }

        videos.forEach(video => {
            const row = document.createElement('tr');

            // Truncate title if too long
            const displayTitle = video.title.length > 30 ? video.title.substring(0, 30) + '...' : video.title;

            // Format status badge
            const statusBadge = `<span class="badge ${getStatusBadgeClass(video.status)}">${video.status.toUpperCase()}</span>`;

            row.innerHTML = `
                <td>
                    <div class="d-flex align-items-center">
                        ${video.thumbnail_url ?
                            `<img src="${video.thumbnail_url}" alt="Thumbnail" style="width: 40px; height: 24px; object-fit: cover; border-radius: 4px; margin-right: 8px;">` :
                            '<div style="width: 40px; height: 24px; background: #f8f9fa; border-radius: 4px; margin-right: 8px; display: flex; align-items: center; justify-content: center;"><i class="bi bi-camera-video text-muted"></i></div>'
                        }
                        <small class="text-muted">${video.id}</small>
                    </div>
                </td>
                <td>
                    <div>
                        <div class="fw-medium">${displayTitle}</div>
                        <small class="text-muted">${statusBadge}</small>
                    </div>
                </td>
                <td>${video.creator}</td>
                <td>${video.category}</td>
                <td>${video.duration}</td>
                <td>${formatViewers(video.views)}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteVideo('${video.id}')" title="Delete video">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('Error loading videos:', error);
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">Error loading videos</td></tr>';
    }
}

// Helper function to get status badge class
function getStatusBadgeClass(status) {
    switch (status.toLowerCase()) {
        case 'active':
        case 'published':
            return 'bg-success';
        case 'processing':
            return 'bg-warning';
        case 'failed':
        case 'error':
            return 'bg-danger';
        case 'draft':
            return 'bg-secondary';
        default:
            return 'bg-secondary';
    }
}

// Handle add category
async function handleAddCategory() {
    const name = document.getElementById('categoryName').value;
    const description = document.getElementById('categoryDescription').value;
    
    if (!name.trim()) {
        showError('Category name is required');
        return;
    }
    
    try {
        const response = await fetch('/api/admin/categories', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminSessionToken}`
            },
            body: JSON.stringify({
                name: name.trim(),
                description: description.trim()
            })
        });
        
        if (response.ok) {
            showSuccess('Category added successfully');
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addCategoryModal'));
            modal.hide();
            
            // Clear form
            document.getElementById('categoryName').value = '';
            document.getElementById('categoryDescription').value = '';
            
            // Reload categories
            loadAdminCategories();
        } else {
            const error = await response.json();
            showError(error.message || 'Failed to add category');
        }
    } catch (error) {
        console.error('Add category error:', error);
        showError('Failed to add category');
    }
}

// User management functions
async function banUser(userId) {
    if (!confirm(`Are you sure you want to ban user "${userId}"? This will disable their account in AWS Cognito.`)) return;

    try {
        const response = await fetch(`/api/admin/users/${encodeURIComponent(userId)}/ban`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });

        const result = await response.json();

        if (response.ok) {
            showSuccess('User banned successfully in AWS Cognito');
            loadUsers();
        } else {
            showError(result.error || 'Failed to ban user');
        }
    } catch (error) {
        console.error('Ban user error:', error);
        showError('Failed to ban user: ' + error.message);
    }
}

async function unbanUser(userId) {
    if (!confirm(`Are you sure you want to unban user "${userId}"? This will re-enable their account in AWS Cognito.`)) return;

    try {
        const response = await fetch(`/api/admin/users/${encodeURIComponent(userId)}/unban`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });

        const result = await response.json();

        if (response.ok) {
            showSuccess('User unbanned successfully in AWS Cognito');
            loadUsers();
        } else {
            showError(result.error || 'Failed to unban user');
        }
    } catch (error) {
        console.error('Unban user error:', error);
        showError('Failed to unban user: ' + error.message);
    }
}

// View user details
async function viewUser(userId) {
    try {
        const response = await fetch(`/api/admin/users/${encodeURIComponent(userId)}`, {
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const user = await response.json();

        // Populate the modal with user data
        document.getElementById('userUsername').value = user.username || '';
        document.getElementById('userEmail').value = user.email || '';
        document.getElementById('userDisplayName').value = user.display_name || '';
        document.getElementById('userPhoneNumber').value = user.phone_number || '';
        document.getElementById('userStatus').value = user.status ? user.status.toUpperCase() : '';
        document.getElementById('userCreatedAt').value = user.created_at ? new Date(user.created_at).toLocaleString() : '';
        document.getElementById('userBio').value = user.bio || '';

        // Update bio character counter
        updateBioCounter();

        // Load avatar image
        const avatarPreview = document.querySelector('#adminUserAvatarPreview .profile-img');
        if (avatarPreview) {
            avatarPreview.src = user.avatar_url || '/static/images/default-avatar.png';
        }

        // Load banner image
        const bannerPreview = document.getElementById('adminUserBannerPreview');
        if (bannerPreview) {
            if (user.banner_url) {
                bannerPreview.style.backgroundImage = `url(${user.banner_url})`;
                bannerPreview.style.backgroundSize = 'cover';
                bannerPreview.style.backgroundPosition = 'center';
                bannerPreview.classList.add('has-image');
                bannerPreview.innerHTML = '';
            } else {
                bannerPreview.style.backgroundImage = '';
                bannerPreview.classList.remove('has-image');
                bannerPreview.innerHTML = '<div class="banner-placeholder"><i class="bi bi-image"></i><p>Click to upload banner</p></div>';
            }
        }

        // Store current user ID for saving
        document.getElementById('userDetailsForm').dataset.userId = userId;

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
        modal.show();

    } catch (error) {
        console.error('View user error:', error);
        showError('Failed to load user details: ' + error.message);
    }
}

// Save user details
async function saveUserDetails() {
    const form = document.getElementById('userDetailsForm');
    const userId = form.dataset.userId;

    if (!userId) {
        showError('No user selected');
        return;
    }

    const formData = {
        email: document.getElementById('userEmail').value,
        display_name: document.getElementById('userDisplayName').value,
        phone_number: document.getElementById('userPhoneNumber').value,
        bio: document.getElementById('userBio').value
    };

    // Remove empty fields
    Object.keys(formData).forEach(key => {
        if (!formData[key]) {
            delete formData[key];
        }
    });

    if (Object.keys(formData).length === 0) {
        showError('No changes to save');
        return;
    }

    try {
        const response = await fetch(`/api/admin/users/${encodeURIComponent(userId)}/profile`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminSessionToken}`
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (response.ok) {
            showSuccess('User details updated successfully');

            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('userDetailsModal'));
            modal.hide();

            // Refresh the users list
            loadUsers();
        } else {
            showError(result.error || 'Failed to update user details');
        }
    } catch (error) {
        console.error('Save user error:', error);
        showError('Failed to save user details: ' + error.message);
    }
}

// Admin user avatar upload
async function handleAdminUserAvatarUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    const form = document.getElementById('userDetailsForm');
    const userId = form.dataset.userId;

    if (!userId) {
        showError('No user selected');
        return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
        showError('Please select a valid image file');
        return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
        showError('Image size must be less than 5MB');
        return;
    }

    // Preview image
    const reader = new FileReader();
    reader.onload = function(e) {
        const avatarPreview = document.querySelector('#adminUserAvatarPreview .profile-img');
        if (avatarPreview) {
            avatarPreview.src = e.target.result;
        }
    };
    reader.readAsDataURL(file);

    // Upload to server
    try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch(`/api/admin/users/${encodeURIComponent(userId)}/avatar`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            },
            body: formData
        });

        const result = await response.json();

        if (response.ok) {
            showSuccess('Avatar uploaded successfully');
            // Update preview with server URL
            const avatarPreview = document.querySelector('#adminUserAvatarPreview .profile-img');
            if (avatarPreview && result.url) {
                avatarPreview.src = result.url;
            }
        } else {
            showError(result.error || 'Failed to upload avatar');
        }
    } catch (error) {
        console.error('Avatar upload error:', error);
        showError('Failed to upload avatar: ' + error.message);
    }
}

// Admin user banner upload
async function handleAdminUserBannerUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    const form = document.getElementById('userDetailsForm');
    const userId = form.dataset.userId;

    if (!userId) {
        showError('No user selected');
        return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
        showError('Please select a valid image file');
        return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
        showError('Image size must be less than 10MB');
        return;
    }

    // Preview image
    const reader = new FileReader();
    reader.onload = function(e) {
        const bannerPreview = document.getElementById('adminUserBannerPreview');
        if (bannerPreview) {
            bannerPreview.style.backgroundImage = `url(${e.target.result})`;
            bannerPreview.style.backgroundSize = 'cover';
            bannerPreview.style.backgroundPosition = 'center';
            bannerPreview.classList.add('has-image');
            bannerPreview.innerHTML = ''; // Remove placeholder
        }
    };
    reader.readAsDataURL(file);

    // Upload to server
    try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch(`/api/admin/users/${encodeURIComponent(userId)}/banner`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            },
            body: formData
        });

        const result = await response.json();

        if (response.ok) {
            showSuccess('Banner uploaded successfully');
            // Update preview with server URL
            const bannerPreview = document.getElementById('adminUserBannerPreview');
            if (bannerPreview && result.url) {
                bannerPreview.style.backgroundImage = `url(${result.url})`;
                bannerPreview.style.backgroundSize = 'cover';
                bannerPreview.style.backgroundPosition = 'center';
                bannerPreview.classList.add('has-image');
                bannerPreview.innerHTML = ''; // Remove placeholder
            }
        } else {
            showError(result.error || 'Failed to upload banner');
        }
    } catch (error) {
        console.error('Banner upload error:', error);
        showError('Failed to upload banner: ' + error.message);
    }
}

// Remove user avatar
function removeUserAvatar() {
    const avatarPreview = document.querySelector('#adminUserAvatarPreview .profile-img');
    if (avatarPreview) {
        avatarPreview.src = '/static/images/default-avatar.png';
    }
    // Note: Actual removal will be handled when saving the form
}

// Remove user banner
function removeUserBanner() {
    const bannerPreview = document.getElementById('adminUserBannerPreview');
    if (bannerPreview) {
        bannerPreview.style.backgroundImage = '';
        bannerPreview.classList.remove('has-image');
        bannerPreview.innerHTML = '<div class="banner-placeholder"><i class="bi bi-image"></i><p>Click to upload banner</p></div>';
    }
    // Note: Actual removal will be handled when saving the form
}

// Update bio character counter
function updateBioCounter() {
    const bioField = document.getElementById('userBio');
    const counter = document.getElementById('userBioCounter');
    if (bioField && counter) {
        counter.textContent = bioField.value.length;
    }
}

// Initialize admin user profile management
function initAdminUserProfileManagement() {
    // Avatar upload
    const avatarUpload = document.getElementById('adminUserAvatarUpload');
    if (avatarUpload) {
        avatarUpload.addEventListener('change', handleAdminUserAvatarUpload);
    }

    // Banner upload
    const bannerUpload = document.getElementById('adminUserBannerUpload');
    if (bannerUpload) {
        bannerUpload.addEventListener('change', handleAdminUserBannerUpload);
    }

    // Bio character counter
    const bioField = document.getElementById('userBio');
    if (bioField) {
        bioField.addEventListener('input', updateBioCounter);
    }

    // Click handlers for upload areas
    const bannerPreview = document.getElementById('adminUserBannerPreview');
    if (bannerPreview) {
        bannerPreview.addEventListener('click', function() {
            document.getElementById('adminUserBannerUpload').click();
        });
    }

    const avatarPreview = document.getElementById('adminUserAvatarPreview');
    if (avatarPreview) {
        avatarPreview.addEventListener('click', function() {
            document.getElementById('adminUserAvatarUpload').click();
        });
    }
}

// Category management functions
async function editCategory(categoryId) {
    try {
        // Fetch category details
        const response = await fetch(`/api/admin/categories/${categoryId}`, {
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const category = await response.json();

        // Populate the edit modal with category data
        document.getElementById('editCategoryName').value = category.name || '';
        document.getElementById('editCategoryDescription').value = category.description || '';
        document.getElementById('editCategoryEnabled').checked = category.is_enabled;

        // Show current photo if exists
        const currentPhotoContainer = document.getElementById('currentPhotoContainer');
        const currentPhoto = document.getElementById('currentPhoto');

        if (category.photo_url) {
            currentPhoto.src = category.photo_url;
            currentPhotoContainer.style.display = 'block';
        } else {
            currentPhotoContainer.style.display = 'none';
        }

        // Store category ID for saving
        document.getElementById('editCategoryForm').dataset.categoryId = categoryId;

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
        modal.show();

    } catch (error) {
        console.error('Edit category error:', error);
        showError('Failed to load category details: ' + error.message);
    }
}

async function toggleCategory(categoryId, currentStatus) {
    if (!confirm(`Are you sure you want to ${currentStatus ? 'disable' : 'enable'} this category?`)) return;

    try {
        const response = await fetch(`/api/admin/categories/${categoryId}/toggle`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });

        const result = await response.json();

        if (response.ok) {
            showSuccess(result.message);
            loadAdminCategories();
        } else {
            showError(result.error || 'Failed to toggle category');
        }
    } catch (error) {
        console.error('Toggle category error:', error);
        showError('Failed to toggle category: ' + error.message);
    }
}

async function deleteCategory(categoryId) {
    if (!confirm('Are you sure you want to delete this category? This action cannot be undone.')) return;

    try {
        const response = await fetch(`/api/admin/categories/${categoryId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });

        const result = await response.json();

        if (response.ok) {
            showSuccess(result.message);
            loadAdminCategories();
        } else {
            showError(result.error || 'Failed to delete category');
        }
    } catch (error) {
        console.error('Delete category error:', error);
        showError('Failed to delete category: ' + error.message);
    }
}

// Check S3 configuration and show warnings
async function checkS3Configuration() {
    try {
        const response = await fetch('/api/admin/upload', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            },
            body: new FormData() // Empty form data to test
        });

        if (response.status === 503 || response.status === 400) {
            // S3 not configured or bad request (empty upload), show warnings
            const s3Warning = document.getElementById('s3Warning');
            const editS3Warning = document.getElementById('editS3Warning');
            if (s3Warning) s3Warning.style.display = 'block';
            if (editS3Warning) editS3Warning.style.display = 'block';
        }
    } catch (error) {
        // Ignore errors, this is just for UI enhancement
        console.log('S3 configuration check failed (this is normal):', error.message);
    }
}

// Handle category form submission
document.addEventListener('DOMContentLoaded', function() {
    // Check S3 configuration on page load
    checkS3Configuration();
    const saveCategoryBtn = document.getElementById('saveCategoryBtn');
    if (saveCategoryBtn) {
        saveCategoryBtn.addEventListener('click', async function() {
            const form = document.getElementById('addCategoryForm');
            const formData = new FormData();

            const name = document.getElementById('categoryName').value.trim();
            const description = document.getElementById('categoryDescription').value.trim();
            const photoFile = document.getElementById('categoryPhoto').files[0];
            const isEnabled = document.getElementById('categoryEnabled').checked;

            if (!name) {
                showError('Category name is required');
                return;
            }

            let photoUrl = '';

            // Upload photo first if provided
            if (photoFile) {
                try {
                    const uploadFormData = new FormData();
                    uploadFormData.append('file', photoFile);

                    const uploadResponse = await fetch('/api/admin/upload', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${adminSessionToken}`
                        },
                        body: uploadFormData
                    });

                    if (uploadResponse.ok) {
                        const uploadResult = await uploadResponse.json();
                        photoUrl = uploadResult.url;
                    } else {
                        const uploadError = await uploadResponse.json();
                        if (uploadResponse.status === 503) {
                            // S3 not configured, continue without photo
                            console.log('S3 not configured, creating category without photo');
                            photoUrl = ''; // Continue without photo
                        } else {
                            showError('Failed to upload photo: ' + (uploadError.error || 'Unknown error'));
                            return;
                        }
                    }
                } catch (error) {
                    console.error('Photo upload error:', error);
                    showError('Failed to upload photo: ' + error.message);
                    return;
                }
            }

            // Create category
            try {
                const categoryData = {
                    name: name,
                    description: description,
                    photo_url: photoUrl,
                    is_enabled: isEnabled
                };

                const response = await fetch('/api/admin/categories', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${adminSessionToken}`
                    },
                    body: JSON.stringify(categoryData)
                });

                const result = await response.json();

                if (response.ok) {
                    showSuccess('Category created successfully');

                    // Close modal and reset form
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addCategoryModal'));
                    modal.hide();
                    form.reset();

                    // Refresh categories list
                    loadAdminCategories();
                } else {
                    showError(result.error || 'Failed to create category');
                }
            } catch (error) {
                console.error('Create category error:', error);
                showError('Failed to create category: ' + error.message);
            }
        });
    }

    // Handle edit category form submission
    const saveEditCategoryBtn = document.getElementById('saveEditCategoryBtn');
    if (saveEditCategoryBtn) {
        saveEditCategoryBtn.addEventListener('click', async function() {
            const form = document.getElementById('editCategoryForm');
            const categoryId = form.dataset.categoryId;

            if (!categoryId) {
                showError('No category selected for editing');
                return;
            }

            const name = document.getElementById('editCategoryName').value.trim();
            const description = document.getElementById('editCategoryDescription').value.trim();
            const photoFile = document.getElementById('editCategoryPhoto').files[0];
            const isEnabled = document.getElementById('editCategoryEnabled').checked;

            if (!name) {
                showError('Category name is required');
                return;
            }

            let photoUrl = '';

            // Upload new photo if provided
            if (photoFile) {
                try {
                    const uploadFormData = new FormData();
                    uploadFormData.append('file', photoFile);

                    const uploadResponse = await fetch('/api/admin/upload', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${adminSessionToken}`
                        },
                        body: uploadFormData
                    });

                    if (uploadResponse.ok) {
                        const uploadResult = await uploadResponse.json();
                        photoUrl = uploadResult.url;
                    } else {
                        const uploadError = await uploadResponse.json();
                        if (uploadResponse.status === 503) {
                            // S3 not configured, continue without photo update
                            console.log('S3 not configured, updating category without photo change');
                            // Don't return, continue with other updates
                        } else {
                            showError('Failed to upload photo: ' + (uploadError.error || 'Unknown error'));
                            return;
                        }
                    }
                } catch (error) {
                    console.error('Photo upload error:', error);
                    showError('Failed to upload photo: ' + error.message);
                    return;
                }
            }

            // Update category
            try {
                const categoryData = {
                    name: name,
                    description: description,
                    is_enabled: isEnabled
                };

                // Only include photo_url if a new photo was uploaded
                if (photoUrl) {
                    categoryData.photo_url = photoUrl;
                }

                const response = await fetch(`/api/admin/categories/${categoryId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${adminSessionToken}`
                    },
                    body: JSON.stringify(categoryData)
                });

                const result = await response.json();

                if (response.ok) {
                    showSuccess('Category updated successfully');

                    // Close modal and reset form
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editCategoryModal'));
                    modal.hide();
                    form.reset();

                    // Refresh categories list
                    loadAdminCategories();
                } else {
                    showError(result.error || 'Failed to update category');
                }
            } catch (error) {
                console.error('Update category error:', error);
                showError('Failed to update category: ' + error.message);
            }
        });
    }
});

// Category management functions (moved to async implementation above)

async function deleteCategory(categoryId) {
    if (!confirm('Are you sure you want to delete this category?')) return;
    
    try {
        const response = await fetch(`/api/admin/categories/${categoryId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });
        
        if (response.ok) {
            showSuccess('Category deleted successfully');
            loadAdminCategories();
        } else {
            showError('Failed to delete category');
        }
    } catch (error) {
        console.error('Delete category error:', error);
        showError('Failed to delete category');
    }
}

// Stream management functions
async function deleteStream(streamId) {
    if (!confirm('Are you sure you want to end this stream?')) return;
    
    try {
        const response = await fetch(`/api/admin/streams/${streamId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });
        
        if (response.ok) {
            showSuccess('Stream ended successfully');
            loadAdminStreams();
        } else {
            showError('Failed to end stream');
        }
    } catch (error) {
        console.error('Delete stream error:', error);
        showError('Failed to end stream');
    }
}

// Video management functions
async function deleteVideo(videoId) {
    if (!confirm('Are you sure you want to delete this video? This action cannot be undone.')) return;

    try {
        const response = await fetch(`/api/admin/videos/${videoId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${adminSessionToken}`
            }
        });

        if (response.ok) {
            showSuccess('Video deleted successfully');
            loadAdminVideos();
        } else {
            const error = await response.json();
            showError(error.error || 'Failed to delete video');
        }
    } catch (error) {
        console.error('Delete video error:', error);
        showError('Failed to delete video');
    }
}

// Utility function for formatting viewers (reuse from main.js)
function formatViewers(viewers) {
    if (viewers >= 1000000) {
        return (viewers / 1000000).toFixed(1) + 'M';
    } else if (viewers >= 1000) {
        return (viewers / 1000).toFixed(1) + 'K';
    }
    return viewers.toString();
}

// User creation functions
async function initCreateUserModal() {
    console.log('initCreateUserModal called');
    const saveBtn = document.getElementById('saveNewUserBtn');
    console.log('Save button found:', saveBtn);
    if (saveBtn) {
        // Remove any existing event listeners by cloning the button
        const newSaveBtn = saveBtn.cloneNode(true);
        saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);

        // Add the event listener to the new button
        newSaveBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            createNewUser();
        });
        console.log('Event listener attached to save button');
    } else {
        console.error('Save button not found!');
    }
}

async function createNewUser() {
    console.log('createNewUser function called');

    const form = document.getElementById('createUserForm');
    const formData = new FormData(form);

    const userData = {
        username: document.getElementById('newUserUsername').value,
        email: document.getElementById('newUserEmail').value,
        password: document.getElementById('newUserPassword').value,
        display_name: document.getElementById('newUserDisplayName').value,
        phone_number: document.getElementById('newUserPhoneNumber').value,
        send_welcome: document.getElementById('newUserSendWelcome').checked
    };

    console.log('User data:', userData);
    console.log('Admin session token:', adminSessionToken);

    // Validate required fields
    if (!userData.username || !userData.email || !userData.password) {
        console.log('Validation failed: missing required fields');
        showError('Please fill in all required fields');
        return;
    }

    // Validate username format
    if (!/^[a-zA-Z0-9_]+$/.test(userData.username)) {
        showError('Username can only contain letters, numbers, and underscores');
        return;
    }

    // Validate password length
    if (userData.password.length < 8) {
        showError('Password must be at least 8 characters long');
        return;
    }

    try {
        const response = await fetch('/api/admin/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminSessionToken}`
            },
            body: JSON.stringify(userData)
        });

        const result = await response.json();

        if (response.ok) {
            console.log('User creation successful:', result);
            showSuccess('User created successfully');
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('createUserModal'));
            if (modal) {
                modal.hide();
            }
            // Reset form
            form.reset();
            // Refresh users list
            loadUsers();
        } else {
            console.log('User creation failed:', result);
            showError(result.error || 'Failed to create user');
        }
    } catch (error) {
        console.error('Create user error:', error);
        showError('Failed to create user');
    }
}

// VOD upload functions
let currentVodUsername = '';

function openUploadVodModal(username) {
    currentVodUsername = username;
    document.getElementById('vodUsername').value = username;

    // Load categories for the dropdown
    loadCategoriesForVod();

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('uploadVodModal'));
    modal.show();
}

async function loadCategoriesForVod() {
    try {
        const response = await fetch('/api/categories');
        const categories = await response.json();

        const select = document.getElementById('vodCategory');
        select.innerHTML = '<option value="">Select a category...</option>';

        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('Error loading categories:', error);
        showError('Failed to load categories');
    }
}

async function initUploadVodModal() {
    const uploadBtn = document.getElementById('uploadVodBtn');
    if (uploadBtn) {
        uploadBtn.addEventListener('click', uploadVodForUser);
    }

    // Add thumbnail preview functionality
    const thumbnailInput = document.getElementById('vodThumbnail');
    if (thumbnailInput) {
        thumbnailInput.addEventListener('change', handleThumbnailPreview);
    }
}

function handleThumbnailPreview(event) {
    const file = event.target.files[0];
    const previewDiv = document.getElementById('thumbnailPreview');
    const previewImg = document.getElementById('thumbnailPreviewImg');

    if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            previewDiv.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        previewDiv.style.display = 'none';
    }
}

async function uploadVodForUser() {
    const form = document.getElementById('uploadVodForm');
    const formData = new FormData();

    const title = document.getElementById('vodTitle').value;
    const description = document.getElementById('vodDescription').value;
    const categoryId = document.getElementById('vodCategory').value;
    const fileInput = document.getElementById('vodFile');
    const thumbnailInput = document.getElementById('vodThumbnail');
    const isPublic = document.getElementById('vodPublic').checked;

    // Validate required fields
    if (!title || !categoryId || !fileInput.files[0]) {
        showError('Please fill in all required fields and select a video file');
        return;
    }

    // Validate video file size (2GB limit)
    const file = fileInput.files[0];
    if (file.size > 2 * 1024 * 1024 * 1024) {
        showError('Video file size must be less than 2GB');
        return;
    }

    // Validate thumbnail file size (10MB limit) if provided
    if (thumbnailInput.files[0]) {
        const thumbnailFile = thumbnailInput.files[0];
        if (thumbnailFile.size > 10 * 1024 * 1024) {
            showError('Thumbnail file size must be less than 10MB');
            return;
        }

        // Validate thumbnail file type
        if (!thumbnailFile.type.startsWith('image/')) {
            showError('Thumbnail must be an image file');
            return;
        }
    }

    // Prepare form data
    formData.append('video', file);
    formData.append('title', title);
    formData.append('description', description);
    formData.append('category_id', categoryId);
    formData.append('is_public', isPublic);

    // Add thumbnail if provided
    if (thumbnailInput.files[0]) {
        formData.append('thumbnail', thumbnailInput.files[0]);
    }

    // Show progress
    const progressDiv = document.getElementById('vodUploadProgress');
    const statusDiv = document.getElementById('vodUploadStatus');
    const uploadBtn = document.getElementById('uploadVodBtn');
    const cancelBtn = document.getElementById('cancelVodBtn');
    const abortBtn = document.getElementById('abortVodBtn');

    progressDiv.style.display = 'block';
    statusDiv.style.display = 'none';
    uploadBtn.disabled = true;
    uploadBtn.textContent = 'Uploading...';
    uploadBtn.style.display = 'none';
    cancelBtn.style.display = 'none';
    abortBtn.style.display = 'inline-block';

    try {
        const xhr = new XMLHttpRequest();

        // Set timeout to 15 minutes (900 seconds) for large file uploads
        xhr.timeout = 900000; // 15 minutes in milliseconds

        // Helper function to restore button states
        const restoreButtonStates = () => {
            uploadBtn.disabled = false;
            uploadBtn.textContent = 'Upload VOD';
            uploadBtn.style.display = 'inline-block';
            cancelBtn.style.display = 'inline-block';
            abortBtn.style.display = 'none';
        };

        // Add abort functionality
        abortBtn.addEventListener('click', () => {
            xhr.abort();
        });

        // Track upload progress
        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                const progressBar = progressDiv.querySelector('.progress-bar');
                progressBar.style.width = percentComplete + '%';
                progressBar.textContent = Math.round(percentComplete) + '%';

                // Update status text based on progress
                if (percentComplete < 100) {
                    statusDiv.className = 'alert alert-info';
                    statusDiv.textContent = `Uploading... ${Math.round(percentComplete)}%`;
                    statusDiv.style.display = 'block';
                } else {
                    statusDiv.className = 'alert alert-info';
                    statusDiv.textContent = 'Upload complete, processing video...';
                    statusDiv.style.display = 'block';
                }
            }
        });

        // Handle completion
        xhr.addEventListener('load', () => {
            console.log('XHR Load event - Status:', xhr.status, 'Response:', xhr.responseText);

            if (xhr.status === 200) {
                try {
                    const result = JSON.parse(xhr.responseText);
                    statusDiv.className = 'alert alert-success';
                    statusDiv.textContent = result.message || 'VOD uploaded successfully';
                    statusDiv.style.display = 'block';

                    showSuccess('VOD uploaded successfully for ' + currentVodUsername);

                    // Reset form after delay
                    setTimeout(() => {
                        const modal = bootstrap.Modal.getInstance(document.getElementById('uploadVodModal'));
                        modal.hide();
                        form.reset();
                        progressDiv.style.display = 'none';
                        statusDiv.style.display = 'none';
                    }, 3000); // Increased delay to 3 seconds
                } catch (parseError) {
                    console.error('Failed to parse response:', parseError);
                    statusDiv.className = 'alert alert-danger';
                    statusDiv.textContent = 'Upload completed but response was invalid';
                    statusDiv.style.display = 'block';
                    showError('Upload completed but response was invalid');
                }
            } else {
                try {
                    const error = JSON.parse(xhr.responseText);
                    statusDiv.className = 'alert alert-danger';
                    statusDiv.textContent = error.error || 'Upload failed';
                    statusDiv.style.display = 'block';
                    showError('Failed to upload VOD: ' + (error.error || 'Unknown error'));
                } catch (parseError) {
                    statusDiv.className = 'alert alert-danger';
                    statusDiv.textContent = `Upload failed with status ${xhr.status}`;
                    statusDiv.style.display = 'block';
                    showError(`Upload failed with status ${xhr.status}`);
                }
            }

            restoreButtonStates();
        });

        // Handle network errors
        xhr.addEventListener('error', () => {
            console.error('XHR Error event');
            statusDiv.className = 'alert alert-danger';
            statusDiv.textContent = 'Upload failed due to network error';
            statusDiv.style.display = 'block';
            showError('Upload failed due to network error');

            restoreButtonStates();
        });

        // Handle timeout
        xhr.addEventListener('timeout', () => {
            console.error('XHR Timeout event');
            statusDiv.className = 'alert alert-warning';
            statusDiv.textContent = 'Upload timed out. Large files may still be processing in the background.';
            statusDiv.style.display = 'block';
            showError('Upload timed out after 15 minutes. Please check if the video appears in the system.');

            restoreButtonStates();
        });

        // Handle abort
        xhr.addEventListener('abort', () => {
            console.error('XHR Abort event');
            statusDiv.className = 'alert alert-warning';
            statusDiv.textContent = 'Upload was cancelled';
            statusDiv.style.display = 'block';
            showError('Upload was cancelled');

            restoreButtonStates();
        });

        // Send request
        xhr.open('POST', `/api/admin/upload-vod/${currentVodUsername}`);
        xhr.setRequestHeader('Authorization', `Bearer ${adminSessionToken}`);

        console.log('Starting VOD upload for user:', currentVodUsername);
        xhr.send(formData);

    } catch (error) {
        console.error('Upload VOD error:', error);
        showError('Failed to upload VOD');

        // Restore button states
        uploadBtn.disabled = false;
        uploadBtn.textContent = 'Upload VOD';
        uploadBtn.style.display = 'inline-block';
        cancelBtn.style.display = 'inline-block';
        abortBtn.style.display = 'none';
        progressDiv.style.display = 'none';
    }
}
