// Main JavaScript for StreamSite

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 DOM Content Loaded');
    console.log('🔧 JavaScript version: 2025-09-18 11:33 - FOLLOWING BUTTON FIX');

    // Quick test to see if Following button exists
    setTimeout(function() {
        const followingBtn = document.getElementById('followingNavItem');
        console.log('🔍 Quick test - Following button exists:', !!followingBtn);
        if (followingBtn) {
            console.log('🔍 Following button data-authenticated:', followingBtn.getAttribute('data-authenticated'));
        } else {
            console.error('❌ Following button NOT FOUND in quick test!');
        }
    }, 500);

    // Load initial data
    loadCategories();
    loadChannels();
    loadFeaturedStreams();
    loadBrowseCategories();
    loadVODCategories();

    // Set up event listeners
    setupEventListeners();

    // Set up logout button immediately and with retries
    setupLogoutButton();

    // Also try to set up logout button after a delay to catch any dynamically loaded content
    setTimeout(setupLogoutButton, 1000);
    setTimeout(setupLogoutButton, 2000);

    // Add global event delegation for logout button clicks
    document.addEventListener('click', function(e) {
        if (e.target && (e.target.id === 'logoutBtn' || e.target.closest('#logoutBtn'))) {
            e.preventDefault();
            console.log('Logout button clicked via event delegation');
            logout();
        }
    });

    // Check if login modal exists after a delay
    setTimeout(function() {
        const loginModal = document.getElementById('loginModal');
        const loginForm = document.getElementById('loginForm');
        console.log('Login modal found:', !!loginModal);
        console.log('Login form found:', !!loginForm);

        if (loginForm) {
            console.log('Login form HTML:', loginForm.outerHTML.substring(0, 200));
            console.log('Login form action:', loginForm.action);
            console.log('Login form method:', loginForm.method);
        } else {
            console.error('Login form not found! Login functionality will not work.');
        }
    }, 1000);
});
async function loadRecommendedUsers() {
    try {
        const response = await fetch('/api/recommended-users');
        const users = await response.json();

        const container = document.getElementById('recommendedUsers');
        if (!container) return;

        if (users.length === 0) {
            container.innerHTML = '<div class="text-center text-muted py-3">No users found</div>';
            return;
        }

        container.innerHTML = users.map(user => {
            // If user is live and has a stream_id, link to their stream page
            // Otherwise, link to their profile page
            const linkUrl = user.is_live && user.stream_id ?
                `/stream/${user.stream_id}` :
                `/user/${user.username}`;

            return `
                <a href="${linkUrl}" class="recommended-user-item">
                    <img src="${user.avatar_url}" alt="${user.username}" class="recommended-user-avatar"
                            onerror="this.src='/static/images/default-avatar.png'">
                    <div class="recommended-user-info">
                        <div class="recommended-user-name">${user.username}</div>
                        <div class="recommended-user-stats">
                            ${user.is_live ?
                                '<span class="live-indicator"><span class="live-dot"></span>LIVE</span>' :
                                '<span class="viewer-count">' + (user.total_viewers || 0) + ' total views</span>'
                            }
                        </div>
                    </div>
                </a>
            `;
        }).join('');

    } catch (error) {
        console.error('Error loading recommended users:', error);
        const container = document.getElementById('recommendedUsers');
        if (container) {
            container.innerHTML = '<div class="text-center text-muted py-3">Failed to load users</div>';
        }
    }
}

// Load recommended users when page loads
document.addEventListener('DOMContentLoaded', loadRecommendedUsers);
// Handle login form submission
async function handleLogin() {
    const username = document.getElementById('loginUsername').value;
    const password = document.getElementById('loginPassword').value;

    if (!username || !password) {
        showError('Please enter both username and password');
        return;
    }

    try {
        console.log('Attempting login...');
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });

        const result = await response.json();
        console.log('Login response:', {
            status: response.status,
            ok: response.ok,
            result: result
        });

        if (response.ok) {
            console.log('✅ Login successful!');
            showSuccess('Login successful!');

            // Close the login modal
            const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            if (loginModal) {
                loginModal.hide();
            }

            // Redirect to refresh page with server-side authentication state
            window.location.reload();

        } else {
            console.log('❌ Login failed:', result.error);
            showError(result.error || 'Login failed');
        }
    } catch (error) {
        console.error('❌ Login error:', error);
        showError('Login error: ' + error.message);
    }
}

// Handle signup form submission (old version - not used)
async function handleSignupOld() {
    const username = document.getElementById('signupUsername').value;
    const email = document.getElementById('signupEmail').value;
    const password = document.getElementById('signupPassword').value;
    const confirmPassword = document.getElementById('signupConfirmPassword').value;

    if (!username || !email || !password || !confirmPassword) {
        showError('Please fill in all fields');
        return;
    }

    if (password !== confirmPassword) {
        showError('Passwords do not match');
        return;
    }

    try {
        console.log('Attempting signup...');
        const response = await fetch('/api/auth/signup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                email: email,
                password: password,
                birthDate: '1990-01-01' // Default birth date
            })
        });

        const result = await response.json();
        console.log('Signup response:', {
            status: response.status,
            ok: response.ok,
            result: result
        });

        if (response.ok) {
            console.log('✅ Signup successful!');
            showSuccess('Account created successfully! Please check your email for verification.');

            // Close the signup modal
            const signupModal = bootstrap.Modal.getInstance(document.getElementById('signupModal'));
            if (signupModal) {
                signupModal.hide();
            }

        } else {
            console.log('❌ Signup failed:', result.error);
            showError(result.error || 'Signup failed');
        }
    } catch (error) {
        console.error('❌ Signup error:', error);
        showError('Signup error: ' + error.message);
    }
}

// Load categories for sidebar
async function loadCategories() {
    try {
        const response = await fetch('/api/categories');
        const categories = await response.json();

        const categoriesList = document.getElementById('categoriesList');
        if (!categoriesList) return;
        categoriesList.innerHTML = '';

        // Check if categories is an array
        if (!categories || !Array.isArray(categories)) {
            console.log('No categories data received or invalid format');
            return;
        }

        categories.forEach(category => {
            const categoryItem = document.createElement('div');
            categoryItem.className = 'category-item';

            // Add small thumbnail for sidebar if image exists
            let thumbnailHtml = '';
            if (category.photo_url && category.photo_url.trim() !== '') {
                thumbnailHtml = `<img src="${category.photo_url}" alt="${category.name}" class="category-sidebar-image"
                                     onerror="this.style.display='none';">`;
            }

            categoryItem.innerHTML = `
                ${thumbnailHtml}
                <div class="category-text">
                    <span class="category-name">${category.name}</span>
                    <span class="category-viewers">${formatViewers(category.viewers)}</span>
                </div>
            `;
            categoryItem.addEventListener('click', () => {
                window.location.href = `/category/${category.id}`;
            });
            categoriesList.appendChild(categoryItem);
        });
    } catch (error) {
        console.error('Error loading categories:', error);
    }
}

// Load live channels for sidebar
async function loadChannels() {
    try {
        const response = await fetch('/api/channels');
        const channels = await response.json();

        const channelsList = document.getElementById('channelsList');
        if (!channelsList) return;
        channelsList.innerHTML = '';

        // Check if channels is an array
        if (!channels || !Array.isArray(channels)) {
            console.log('No channels data received or invalid format');
            return;
        }

        channels.forEach(channel => {
            const channelItem = document.createElement('div');
            channelItem.className = 'channel-item';
            channelItem.innerHTML = `
                <div class="channel-avatar">${channel.name.charAt(0).toUpperCase()}</div>
                <div class="channel-info">
                    <div class="channel-name">${channel.name}</div>
                    <div class="channel-title">${channel.title}</div>
                    <div class="channel-viewers">${formatViewers(channel.viewers)} viewers</div>
                </div>
                <div class="live-indicator"></div>
            `;
            channelItem.addEventListener('click', () => {
                window.location.href = `/channel/${channel.id}`;
            });
            channelsList.appendChild(channelItem);
        });
    } catch (error) {
        console.error('Error loading channels:', error);
    }
}

// Video Slideshow Variables
let currentSlideIndex = 0;
let slideshowContent = [];
let slideshowInterval = null;
const SLIDESHOW_INTERVAL = 8000; // 8 seconds

// Load featured content (live streams first, then VODs)
async function loadFeaturedStreams() {
    try {
        // Check if we're on the browse page by looking for pageType in the body data attribute
        // or by checking the URL path
        const isBrowsePage = window.location.pathname === '/browse';

        // Get category filter from URL parameters if on browse page
        let apiEndpoint = isBrowsePage ? '/api/browse-content' : '/api/content';
        if (isBrowsePage) {
            const urlParams = new URLSearchParams(window.location.search);
            const categoryFilter = urlParams.get('category');
            if (categoryFilter) {
                apiEndpoint += `?category=${encodeURIComponent(categoryFilter)}`;
            }
        }

        console.log('Loading content from:', apiEndpoint, 'isBrowsePage:', isBrowsePage);

        const response = await fetch(apiEndpoint);
        const data = await response.json();
        const content = data.content || [];

        const featuredStreams = document.getElementById('featuredStreams');
        if (!featuredStreams) return;
        featuredStreams.innerHTML = '';

        // Check if content is an array
        if (!content || !Array.isArray(content)) {
            console.log('No content data received or invalid format');
            featuredStreams.innerHTML = `
                <div class="col-12 text-center">
                    <div class="no-streams-message">
                        <i class="bi bi-camera-video-off fs-1 text-muted"></i>
                        <h5 class="mt-3 text-muted">No Content Available</h5>
                        <p class="text-muted">Be the first to go live!</p>
                        <a href="/creator/dashboard" class="btn btn-primary">Start Streaming</a>
                    </div>
                </div>
            `;
            return;
        }

        if (content.length === 0) {
            if (isBrowsePage) {
                // Browse page empty state (profile page style)
                featuredStreams.innerHTML = `
                    <div class="no-vods">
                        <div class="text-center py-5">
                            <i class="bi bi-play-circle display-1 text-muted mb-3"></i>
                            <h5 class="text-muted">No videos available</h5>
                            <p class="text-muted">No recorded streams found. Check back later for new content!</p>
                            <a href="/" class="btn btn-primary">
                                <i class="bi bi-house me-1"></i>Go to Home
                            </a>
                        </div>
                    </div>
                `;
            } else {
                // Home page empty state
                featuredStreams.innerHTML = `
                    <div class="col-12 text-center">
                        <div class="no-streams-message">
                            <i class="bi bi-camera-video-off fs-1 text-muted"></i>
                            <h5 class="mt-3 text-muted">No Content Available</h5>
                            <p class="text-muted">Be the first to go live!</p>
                            <a href="/creator/dashboard" class="btn btn-primary">Start Streaming</a>
                        </div>
                    </div>
                `;
            }
            return;
        }

        // Initialize video slideshow for home page (not browse page)
        if (!isBrowsePage) {
            initializeVideoSlideshow(content, data.has_live);
        }

        if (isBrowsePage) {
            // Browse page: Use profile-style VOD grid layout
            content.forEach(item => {
                const vodCard = document.createElement('div');
                vodCard.className = 'vod-card';

                // Format date
                const date = new Date(item.started_at);
                const formattedDate = date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });

                vodCard.innerHTML = `
                    <img src="${item.thumbnail_url || '/static/images/default-thumbnail.jpg'}"
                         alt="${item.title}" class="vod-thumbnail">
                    <div class="vod-info">
                        <h6 class="vod-title">${item.title}</h6>
                        <div class="vod-meta">
                            <span>
                                <i class="bi bi-person me-1"></i>${item.streamer_name}
                            </span>
                            <span class="vod-category">${item.category_name}</span>
                        </div>
                        <div class="vod-meta">
                            <span>
                                <i class="bi bi-eye me-1"></i>${formatViewers(item.views)} views
                            </span>
                            ${item.duration ? `<span>${item.duration}</span>` : ''}
                        </div>
                        <div class="vod-meta mt-2">
                            <small class="text-muted">
                                <i class="bi bi-clock me-1"></i>${formattedDate}
                            </small>
                        </div>
                    </div>
                `;

                vodCard.addEventListener('click', () => {
                    window.location.href = `/vod/${item.id}`;
                });

                featuredStreams.appendChild(vodCard);
            });
        } else {
            // Home page: Use original card layout for featured content
            const itemsToShow = content.slice(0, 4);
            itemsToShow.forEach(item => {
                const contentCard = document.createElement('div');
                contentCard.className = 'col-lg-6 col-md-6 col-sm-6 mb-4';

                if (item.type === 'live') {
                    // Live stream card
                    const isCurrentUserStream = item.is_current_user_stream;
                    const userStreamBadge = isCurrentUserStream ?
                        '<div class="user-stream-badge"><i class="bi bi-person-fill"></i> YOUR STREAM</div>' : '';

                    contentCard.innerHTML = `
                        <div class="stream-card ${isCurrentUserStream ? 'current-user-stream' : ''}">
                            <div class="stream-thumbnail">
                                <div class="live-badge">
                                    <span class="live-dot"></span>
                                    LIVE
                                </div>
                                ${userStreamBadge}
                                <i class="bi bi-play-circle"></i>
                            </div>
                            <div class="stream-info">
                                <div class="stream-title">${item.title}</div>
                                <div class="stream-streamer">${item.streamer_name}${isCurrentUserStream ? ' (You)' : ''}</div>
                                <div class="stream-category">${item.category_name}</div>
                                <div class="stream-viewers">${formatViewers(item.viewers)} viewers</div>
                            </div>
                        </div>
                    `;
                    contentCard.addEventListener('click', () => {
                        window.location.href = `/stream/${item.id}`;
                    });
                } else {
                    // VOD card
                    contentCard.innerHTML = `
                        <div class="stream-card">
                            <div class="stream-thumbnail">
                                <div class="vod-badge">
                                    <i class="bi bi-play-fill"></i>
                                    VOD
                                </div>
                                <div class="duration-badge">${item.duration}</div>
                                <i class="bi bi-play-circle"></i>
                            </div>
                            <div class="stream-info">
                                <div class="stream-title">${item.title}</div>
                                <div class="stream-streamer">${item.streamer_name}</div>
                                <div class="stream-category">${item.category_name}</div>
                                <div class="stream-viewers">${formatViewers(item.views)} views</div>
                            </div>
                        </div>
                    `;
                    contentCard.addEventListener('click', () => {
                        window.location.href = `/vod/${item.id}`;
                    });
                }

                featuredStreams.appendChild(contentCard);
            });
        }
    } catch (error) {
        console.error('Error loading featured content:', error);
        const featuredStreams = document.getElementById('featuredStreams');
        if (featuredStreams) {
            featuredStreams.innerHTML = `
                <div class="col-12 text-center">
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle fs-1 text-warning"></i>
                        <h5 class="mt-3">Failed to Load Content</h5>
                        <p class="text-muted">Please try refreshing the page</p>
                    </div>
                </div>
            `;
        }
    }
}

// Update hero section with featured content
function updateHeroSection(content, hasLive, isBrowsePage = false) {
    const heroTitle = document.getElementById('heroTitle');
    const heroDescription = document.getElementById('heroDescription');
    const heroViewerCount = document.getElementById('heroViewerCount');
    const heroWatchBtn = document.getElementById('heroWatchBtn');
    const heroTags = document.getElementById('heroTags');

    if (!heroTitle || !heroDescription || !heroViewerCount || !heroWatchBtn) return;

    // If this is the browse page, customize the hero section
    if (isBrowsePage) {
        if (content) {
            // Show the top VOD on browse page
            heroTitle.textContent = content.title;
            heroDescription.textContent = content.description || 'Browse our collection of recorded streams and highlights from top streamers.';
            heroViewerCount.textContent = `${formatViewers(content.views)} views • ${content.duration}`;
            heroWatchBtn.textContent = 'Watch VOD';
            heroWatchBtn.onclick = () => window.location.href = `/vod/${content.id}`;

            // Update live indicator to show Browse
            const liveIndicator = document.querySelector('.hero-section .text-danger');
            if (liveIndicator) {
                liveIndicator.textContent = 'Browse VODs';
                liveIndicator.className = liveIndicator.className.replace('text-danger', 'text-primary');
            }

            // Update tags for browse page
            if (heroTags) {
                heroTags.innerHTML = `
                    <span class="badge bg-secondary">${content.category_name}</span>
                    <span class="badge bg-outline-light">${content.streamer_name}</span>
                    <span class="badge bg-info">VOD</span>
                `;
            }
        } else {
            // No content available on browse page
            heroTitle.textContent = 'Browse All VODs';
            heroDescription.textContent = 'Discover recorded streams and highlights from our community of streamers.';
            heroViewerCount.textContent = 'No VODs available yet';
            heroWatchBtn.textContent = 'Start Streaming';
            heroWatchBtn.onclick = () => window.location.href = '/creator/dashboard';

            const liveIndicator = document.querySelector('.hero-section .text-danger');
            if (liveIndicator) {
                liveIndicator.textContent = 'Browse VODs';
                liveIndicator.className = liveIndicator.className.replace('text-danger', 'text-primary');
            }

            if (heroTags) {
                heroTags.innerHTML = '<span class="badge bg-secondary">All Categories</span>';
            }
        }
        return;
    }

    if (content.type === 'live') {
        // Live stream hero
        const isCurrentUserStream = content.is_current_user_stream;

        heroTitle.textContent = content.title;
        heroDescription.textContent = content.description || 'Join the excitement with live betting streams, expert analysis, and thousands of viewers.';
        heroViewerCount.textContent = `${formatViewers(content.viewers)} watching`;

        if (isCurrentUserStream) {
            heroWatchBtn.textContent = 'Manage Your Stream';
            heroWatchBtn.onclick = () => window.location.href = `/stream/${content.id}`;
        } else {
            heroWatchBtn.textContent = 'Watch Live';
            heroWatchBtn.onclick = () => window.location.href = `/stream/${content.id}`;
        }

        // Update live indicator
        const liveIndicator = document.querySelector('.hero-section .text-danger');
        if (liveIndicator) {
            liveIndicator.textContent = isCurrentUserStream ? 'Your Stream Live' : 'Live Now';
        }
    } else {
        // VOD hero
        heroTitle.textContent = content.title;
        heroDescription.textContent = content.description || 'Watch recorded streams and highlights from our top streamers.';
        heroViewerCount.textContent = `${formatViewers(content.views)} views • ${content.duration}`;
        heroWatchBtn.textContent = 'Watch VOD';
        heroWatchBtn.onclick = () => window.location.href = `/vod/${content.id}`;

        // Update live indicator to show VOD
        const liveIndicator = document.querySelector('.hero-section .text-danger');
        if (liveIndicator) {
            liveIndicator.textContent = 'Recent VOD';
            liveIndicator.className = liveIndicator.className.replace('text-danger', 'text-info');
        }
    }

    // Update tags
    if (heroTags) {
        const isCurrentUserStream = content.is_current_user_stream;
        const streamerBadge = isCurrentUserStream ?
            `<span class="badge bg-success">${content.streamer_name} (You)</span>` :
            `<span class="badge bg-outline-light">${content.streamer_name}</span>`;

        heroTags.innerHTML = `
            <span class="badge bg-secondary">${content.category_name}</span>
            ${streamerBadge}
        `;
    }
}

// Initialize Video Slideshow
function initializeVideoSlideshow(content, hasLive) {
    const slideshowContainer = document.getElementById('videoSlideshowContainer');
    const slideshow = document.getElementById('videoSlideshow');
    const indicators = document.getElementById('slideshowIndicators');
    const loading = document.getElementById('slideshowLoading');

    if (!slideshowContainer || !slideshow || !indicators || !loading) return;

    // Store content for slideshow
    slideshowContent = content.slice(0, 5); // Show up to 5 slides
    currentSlideIndex = 0;

    // Hide loading
    loading.classList.add('hidden');

    if (slideshowContent.length === 0) {
        // Show empty state
        slideshow.innerHTML = `
            <div class="slideshow-slide" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="slideshow-content">
                    <h1>Welcome to MoneyBags</h1>
                    <p>Join the excitement with live betting streams, expert analysis, and thousands of viewers.</p>
                    <div class="slideshow-meta">
                        <div class="slideshow-live-indicator">
                            <span class="badge bg-primary">Getting Started</span>
                        </div>
                    </div>
                    <a href="/creator/dashboard" class="slideshow-btn-primary">Start Streaming</a>
                </div>
            </div>
        `;
        return;
    }

    // Create slides
    createSlides();

    // Create indicators
    createIndicators();

    // Set up navigation
    setupSlideshowNavigation();

    // Start auto-play
    startSlideshow();

    // Setup hover functionality
    setupSlideshowHover();
}

// Create slideshow slides
function createSlides() {
    const slideshow = document.getElementById('videoSlideshow');
    if (!slideshow) return;

    slideshow.innerHTML = '';

    slideshowContent.forEach((item, index) => {
        const slide = document.createElement('div');
        slide.className = 'slideshow-slide';

        // Set background image
        const backgroundImage = item.thumbnail_url || 'https://images.unsplash.com/photo-1633629544357-14223c9837d2?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxwb2tlciUyMGNhcmRzJTIwY2FzaW5vfGVufDF8fHx8MTc1NzY3MjU2M3ww&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral';
        slide.style.backgroundImage = `url('${backgroundImage}')`;

        // Create slide content
        const isLive = item.type === 'live';
        const isCurrentUserStream = item.is_current_user_stream;

        let actionUrl, actionText;
        if (isLive) {
            actionUrl = `/stream/${item.id}`;
            actionText = isCurrentUserStream ? 'Manage Your Stream' : 'Watch Live';
        } else {
            actionUrl = `/vod/${item.id}`;
            actionText = 'Watch VOD';
        }

        let metaInfo;
        if (isLive) {
            metaInfo = `${formatViewers(item.viewers)} watching`;
        } else {
            metaInfo = `${formatViewers(item.views)} views • ${item.duration}`;
        }

        slide.innerHTML = `
            <div class="slideshow-content">
                <h1>${item.title}</h1>
                <p>${item.description || (isLive ? 'Join the excitement with live betting streams and expert analysis.' : 'Watch recorded streams and highlights from top streamers.')}</p>
                <div class="slideshow-meta">
                    <div class="slideshow-live-indicator">
                        ${isLive ? `
                            <div class="slideshow-pulse-dot"></div>
                            <span class="text-danger text-uppercase fw-medium">${isCurrentUserStream ? 'Your Stream Live' : 'Live Now'}</span>
                        ` : `
                            <span class="badge bg-info">Recent VOD</span>
                        `}
                        <span class="text-white-50">${metaInfo}</span>
                    </div>
                </div>
                <div class="slideshow-tags">
                    <span class="badge bg-secondary">${item.category_name}</span>
                    <span class="badge bg-outline-light">${item.streamer_name}${isCurrentUserStream ? ' (You)' : ''}</span>
                </div>
                <a href="${actionUrl}" class="slideshow-btn-primary">${actionText}</a>
            </div>
        `;

        slideshow.appendChild(slide);
    });
}

// Create slideshow indicators
function createIndicators() {
    const indicators = document.getElementById('slideshowIndicators');
    if (!indicators) return;

    indicators.innerHTML = '';

    slideshowContent.forEach((_, index) => {
        const indicator = document.createElement('button');
        indicator.className = `slideshow-indicator ${index === 0 ? 'active' : ''}`;
        indicator.setAttribute('data-slide', index);
        indicator.addEventListener('click', () => goToSlide(index));
        indicators.appendChild(indicator);
    });
}

// Setup slideshow navigation
function setupSlideshowNavigation() {
    const prevBtn = document.getElementById('prevSlide');
    const nextBtn = document.getElementById('nextSlide');

    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            stopSlideshow();
            previousSlide();
            startSlideshow();
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            stopSlideshow();
            nextSlide();
            startSlideshow();
        });
    }

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
            stopSlideshow();
            previousSlide();
            startSlideshow();
        } else if (e.key === 'ArrowRight') {
            stopSlideshow();
            nextSlide();
            startSlideshow();
        }
    });
}

// Navigate to specific slide
function goToSlide(index) {
    if (index < 0 || index >= slideshowContent.length) return;

    currentSlideIndex = index;
    updateSlidePosition();
    updateIndicators();
}

// Go to next slide
function nextSlide() {
    currentSlideIndex = (currentSlideIndex + 1) % slideshowContent.length;
    updateSlidePosition();
    updateIndicators();
}

// Go to previous slide
function previousSlide() {
    currentSlideIndex = currentSlideIndex === 0 ? slideshowContent.length - 1 : currentSlideIndex - 1;
    updateSlidePosition();
    updateIndicators();
}

// Update slide position
function updateSlidePosition() {
    const slideshow = document.getElementById('videoSlideshow');
    if (!slideshow) return;

    const translateX = -currentSlideIndex * 100;
    slideshow.style.transform = `translateX(${translateX}%)`;
}

// Update indicators
function updateIndicators() {
    const indicators = document.querySelectorAll('.slideshow-indicator');
    indicators.forEach((indicator, index) => {
        indicator.classList.toggle('active', index === currentSlideIndex);
    });
}

// Start slideshow auto-play
function startSlideshow() {
    if (slideshowContent.length <= 1) return;

    stopSlideshow(); // Clear any existing interval
    slideshowInterval = setInterval(() => {
        nextSlide();
    }, SLIDESHOW_INTERVAL);
}

// Stop slideshow auto-play
function stopSlideshow() {
    if (slideshowInterval) {
        clearInterval(slideshowInterval);
        slideshowInterval = null;
    }
}

// Pause slideshow on hover
function setupSlideshowHover() {
    const container = document.getElementById('videoSlideshowContainer');
    if (!container) return;

    container.addEventListener('mouseenter', stopSlideshow);
    container.addEventListener('mouseleave', startSlideshow);
}

// Load browse categories for main content
async function loadBrowseCategories() {
    try {
        const response = await fetch('/api/categories');
        const categories = await response.json();

        const browseCategories = document.getElementById('topCategories');
        if (!browseCategories) return;
        browseCategories.innerHTML = '';

        // Check if categories is an array
        if (!categories || !Array.isArray(categories)) {
            console.log('No categories data received or invalid format');
            return;
        }

        // Show only top 6 categories
        const topCategories = categories.slice(0, 6);

        topCategories.forEach((category, index) => {
            const categoryCard = document.createElement('div');
            categoryCard.className = 'col';

            // Debug logging
            console.log(`Category: ${category.name}, Photo URL: '${category.photo_url}'`);

            // Use category image if available, otherwise use a default placeholder
            let imageUrl = category.photo_url && category.photo_url.trim() !== ''
                ? category.photo_url
                : 'https://images.unsplash.com/photo-1743824521065-48d394eafcda?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxibGFja2phY2slMjBjYXNpbm8lMjB2ZXJ0aWNhbHxlbnwxfHx8fDE3NTgwNzI0MTJ8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral';

            // Format viewer count
            const viewerCount = formatViewers(category.viewers || Math.floor(Math.random() * 20000) + 1000);

            categoryCard.innerHTML = `
                <div class="category-card position-relative rounded-lg overflow-hidden">
                    <img src="${imageUrl}" alt="${category.name}" class="category-img w-100 h-100">
                    <div class="category-overlay"></div>
                    <div class="position-absolute top-0 start-0 m-3 w-8 h-8 bg-danger rounded-circle d-flex align-items-center justify-content-center maincount">
                        <span class="text-white fw-bold">${index + 1}</span>
                    </div>
                    <!--<div class="position-absolute top-0 end-0 m-3 bg-black bg-opacity-50 rounded-pill px-2 py-1 d-flex align-items-center gap-1">
                        <div class="pulse-dot"></div>
                        <span class="text-white text-xs">LIVE</span>
                    </div>-->
                    <div class="position-absolute bottom-0 start-0 end-0 m-3">
                        <h3 class="text-white fw-medium text-sm mb-1 text-truncate">${category.name}</h3>
                        <p class="text-white-50 text-xs">${viewerCount} viewers</p>
                    </div>
                </div>
            `;

            categoryCard.addEventListener('click', () => {
                window.location.href = `/browse?category=${encodeURIComponent(category.name)}`;
            });
            browseCategories.appendChild(categoryCard);
        });
    } catch (error) {
        console.error('Error loading browse categories:', error);
    }
}

// Load VOD categories for home page
async function loadVODCategories() {
    try {
        // Only load on home page, not browse page
        if (window.location.pathname === '/browse') {
            return;
        }

        const response = await fetch('/api/vod-categories');
        const data = await response.json();
        const categories = data.categories || [];

        const vodCategoriesContainer = document.getElementById('vodCategories');
        if (!vodCategoriesContainer) return;

        vodCategoriesContainer.innerHTML = '';

        if (categories.length === 0) {
            vodCategoriesContainer.innerHTML = `
                <div class="text-center py-4">
                    <i class="bi bi-play-circle display-4 text-muted mb-3"></i>
                    <h5 class="text-muted">No VODs Available</h5>
                    <p class="text-muted">No recorded streams found. Check back later for new content!</p>
                </div>
            `;
            return;
        }

        // Create category sections
        categories.forEach(category => {
            const categorySection = document.createElement('div');
            categorySection.className = 'mb-5';

            categorySection.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3 class="h6 mb-0 category-title" style="cursor: pointer;" onclick="window.location.href='/browse?category=${encodeURIComponent(category.name)}'">${category.name}</h3>
                    <a href="/browse?category=${encodeURIComponent(category.name)}" class="btn btn-sm btn-outline-primary">
                        View All <i class="bi bi-arrow-right ms-1"></i>
                    </a>
                </div>
                <div class="vod-grid" id="category-${category.id}">
                    <!-- VODs will be populated here -->
                </div>
            `;

            vodCategoriesContainer.appendChild(categorySection);

            // Populate VODs for this category
            const categoryVODsContainer = document.getElementById(`category-${category.id}`);
            category.vods.forEach(vod => {
                const vodCard = document.createElement('div');
                vodCard.className = 'vod-card';

                // Format date
                const date = new Date(vod.created_at);
                const formattedDate = date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });

                vodCard.innerHTML = `
                    <img src="${vod.thumbnail_url || '/static/images/default-thumbnail.jpg'}"
                         alt="${vod.title}" class="vod-thumbnail">
                    <div class="vod-info">
                        <h6 class="vod-title">${vod.title}</h6>
                        <div class="vod-meta">
                            <span>
                                <i class="bi bi-person me-1"></i>${vod.streamer_name}
                            </span>
                        </div>
                        <div class="vod-meta">
                            <span>
                                <i class="bi bi-eye me-1"></i>${formatViewers(vod.views)} views
                            </span>
                            ${vod.duration ? `<span>${vod.duration}</span>` : ''}
                        </div>
                        <div class="vod-meta mt-2">
                            <small class="text-muted">
                                <i class="bi bi-clock me-1"></i>${formattedDate}
                            </small>
                        </div>
                    </div>
                `;

                vodCard.addEventListener('click', () => {
                    window.location.href = `/vod/${vod.id}`;
                });

                categoryVODsContainer.appendChild(vodCard);
            });
        });

    } catch (error) {
        console.error('Error loading VOD categories:', error);
        const vodCategoriesContainer = document.getElementById('vodCategories');
        if (vodCategoriesContainer) {
            vodCategoriesContainer.innerHTML = `
                <div class="text-center py-4">
                    <i class="bi bi-exclamation-triangle display-4 text-muted mb-3"></i>
                    <h5 class="text-muted">Error Loading VODs</h5>
                    <p class="text-muted">Unable to load VOD categories. Please try again later.</p>
                </div>
            `;
        }
    }
}

// Set up event listeners
function setupEventListeners() {
    // Search functionality
    const searchBtn = document.getElementById('searchBtn');
    const searchInput = document.getElementById('searchInput');

    if (searchBtn) {
        searchBtn.addEventListener('click', performSearch);
    }
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }

    // Form submission using event delegation - CRITICAL FIX
    document.addEventListener('submit', function(e) {
        console.log('🔥 FORM SUBMITTED:', e.target.id, e.target.tagName);
        console.log('🔥 Form action:', e.target.action);
        console.log('🔥 Form method:', e.target.method);

        if (e.target && e.target.id === 'loginForm') {
            console.log('🚀 LOGIN FORM INTERCEPTED - PREVENTING DEFAULT!');
            e.preventDefault();
            e.stopPropagation();
            handleLogin();
            return false;
        } else if (e.target && e.target.id === 'signupForm') {
            console.log('🚀 SIGNUP FORM INTERCEPTED - PREVENTING DEFAULT!');
            e.preventDefault();
            e.stopPropagation();
            handleSignup(e);
            return false;
        } else if (e.target && e.target.id === 'verificationForm') {
            console.log('🚀 VERIFICATION FORM INTERCEPTED - PREVENTING DEFAULT!');
            e.preventDefault();
            e.stopPropagation();
            handleVerification(e);
            return false;
        } else {
            console.log('❌ Unknown form submitted:', e.target.id, 'allowing default behavior');
        }
    }, true); // Use capture phase to ensure we catch it first

    // Also try direct approach
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        console.log('Login form found, adding event listener');
        loginForm.addEventListener('submit', function(e) {
            console.log('Login form submitted directly!');
            e.preventDefault();
            handleLogin();
        });
    } else {
        console.log('Login form NOT found!');
    }

    // Also support old loginSubmit button if it exists
    const loginSubmit = document.getElementById('loginSubmit');
    if (loginSubmit) {
        loginSubmit.addEventListener('click', handleLogin);
    }

    // Signup form submission
    const signupForm = document.getElementById('signupForm');
    if (signupForm) {
        console.log('Signup form found, adding event listener');
        signupForm.addEventListener('submit', function(e) {
            console.log('Signup form submitted directly!');
            e.preventDefault();
            handleSignup(e);
        });
    } else {
        console.log('Signup form not found, will use event delegation');
    }

    // Add modal event listeners to ensure forms are ready
    const loginModal = document.getElementById('loginModal');
    if (loginModal) {
        console.log('✅ Login modal found, adding event listeners');
        loginModal.addEventListener('shown.bs.modal', function() {
            console.log('🔥 Login modal shown, setting up form...');
            const form = document.getElementById('loginForm');
            if (form) {
                console.log('🔥 Login form found in modal, adding submit listener');
                // Remove any existing listeners first
                form.removeEventListener('submit', handleLoginSubmit);
                // Add new listener
                form.addEventListener('submit', handleLoginSubmit);
                form.setAttribute('data-listener-ready', 'true');
            } else {
                console.error('❌ Login form not found in modal!');
            }
        });
    } else {
        console.error('❌ Login modal not found!');
    }

    const signupModal = document.getElementById('signupModal');
    if (signupModal) {
        console.log('✅ Signup modal found, adding event listeners');
        signupModal.addEventListener('shown.bs.modal', function() {
            console.log('🔥 Signup modal shown, setting up form...');
            const form = document.getElementById('signupForm');
            if (form) {
                console.log('🔥 Signup form found in modal, adding submit listener');
                // Remove any existing listeners first
                form.removeEventListener('submit', handleSignupSubmit);
                // Add new listener
                form.addEventListener('submit', handleSignupSubmit);
                form.setAttribute('data-listener-ready', 'true');
            } else {
                console.error('❌ Signup form not found in modal!');
            }
        });
    } else {
        console.error('❌ Signup modal not found!');
    }

    // Create dedicated submit handlers
    function handleLoginSubmit(e) {
        console.log('🚀 LOGIN FORM SUBMIT INTERCEPTED!');
        e.preventDefault();
        e.stopPropagation();
        handleLogin();
        return false;
    }

    function handleSignupSubmit(e) {
        console.log('🚀 SIGNUP FORM SUBMIT INTERCEPTED!');
        e.preventDefault();
        e.stopPropagation();
        handleSignup(e);
        return false;
    }

    // Verification form submission is handled by global form handler

    // Resend code button
    const resendCodeBtn = document.getElementById('resendCodeBtn');
    if (resendCodeBtn) {
        resendCodeBtn.addEventListener('click', handleResendCode);
    }

    // Back to signup button
    const backToSignupBtn = document.getElementById('backToSignupBtn');
    if (backToSignupBtn) {
        backToSignupBtn.addEventListener('click', backToSignup);
    }

    // Proceed to login button
    const proceedToLoginBtn = document.getElementById('proceedToLoginBtn');
    if (proceedToLoginBtn) {
        proceedToLoginBtn.addEventListener('click', proceedToLogin);
    }

    // Reset signup modal when closed - use existing signupModal variable
    if (signupModal) {
        signupModal.addEventListener('hidden.bs.modal', resetSignupModal);

        // Add verification form event listeners
        signupModal.addEventListener('shown.bs.modal', function() {
            // Set up resend code button
            const resendBtn = document.getElementById('resendCodeBtn');
            if (resendBtn) {
                resendBtn.addEventListener('click', handleResendCode);
            }

            // Set up back to signup button
            const backBtn = document.getElementById('backToSignupBtn');
            if (backBtn) {
                backBtn.addEventListener('click', function() {
                    resetSignupModal();
                });
            }
        });
    }

    // Note: Logout button event listeners are set up in updateUIForLoggedInUser function

    // Set up logout button (authentication state handled server-side)
    setupLogoutButton();

    // Social login buttons
    const googleLogin = document.getElementById('googleLogin');
    const facebookLogin = document.getElementById('facebookLogin');
    const appleLogin = document.getElementById('appleLogin');

    if (googleLogin) googleLogin.addEventListener('click', () => handleSocialLogin('google'));
    if (facebookLogin) facebookLogin.addEventListener('click', () => handleSocialLogin('facebook'));
    if (appleLogin) appleLogin.addEventListener('click', () => handleSocialLogin('apple'));

    // Notification button
    const notificationBtn = document.getElementById('notificationBtn');
    if (notificationBtn) {
        notificationBtn.addEventListener('click', function(e) {
            e.preventDefault();
            // TODO: Show notifications dropdown
            showInfo('Notifications feature coming soon!');
        });
    }

    // Birth date dropdowns
    setupBirthDateDropdowns();

    // Following button handler - with delay to ensure DOM is ready
    setTimeout(function() {
        console.log('🔍 Setting up Following button handler...');

        // Try multiple ways to find the button
        let followingNavItem = document.getElementById('followingNavItem');
        console.log('🔍 Following button by ID:', !!followingNavItem);

        if (!followingNavItem) {
            // Try finding by text content
            const buttons = document.querySelectorAll('button');
            for (let btn of buttons) {
                if (btn.textContent.trim().includes('Following')) {
                    followingNavItem = btn;
                    console.log('🔍 Found Following button by text content');
                    break;
                }
            }
        }

        if (followingNavItem) {
            const isAuthenticated = followingNavItem.getAttribute('data-authenticated');
            console.log('🔍 Following button data-authenticated:', isAuthenticated);
            console.log('🔍 Following button HTML:', followingNavItem.outerHTML);

            followingNavItem.addEventListener('click', function(e) {
                console.log('🚀 Following button clicked!');
                e.preventDefault();
                const currentAuth = followingNavItem.getAttribute('data-authenticated') === 'true';
                console.log('🔍 Current authentication status:', currentAuth);

                if (currentAuth) {
                    console.log('✅ User authenticated, navigating to /following');
                    // Navigate to following page for authenticated users
                    window.location.href = '/following';
                } else {
                    console.log('❌ User not authenticated, showing login modal');
                    // Show login modal for non-authenticated users
                    showLoginModal();
                }
            });
            console.log('✅ Following button event listener attached');
        } else {
            console.error('❌ Following button not found even after delay!');
            // Debug: show all elements with 'following' in text
            const allElements = document.querySelectorAll('*');
            let foundElements = [];
            allElements.forEach(el => {
                if (el.textContent && el.textContent.toLowerCase().includes('following')) {
                    foundElements.push({
                        tag: el.tagName,
                        id: el.id,
                        class: el.className,
                        text: el.textContent.trim()
                    });
                }
            });
            console.log('🔍 Elements containing "following":', foundElements);

            // Try to find all buttons in sidebar for debugging
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                const buttons = sidebar.querySelectorAll('button');
                console.log('🔍 Buttons found in sidebar:', buttons.length);
                buttons.forEach((btn, index) => {
                    console.log(`🔍 Button ${index}:`, btn.id, btn.textContent.trim());
                });
            } else {
                console.error('❌ Sidebar not found!');
            }
        }
    }, 1000);

    // Check authentication status
    checkAuthStatus();
}

// Search functionality
function performSearch() {
    const searchInput = document.getElementById('searchInput');
    const query = searchInput.value.trim();
    
    if (query) {
        window.location.href = `/search?q=${encodeURIComponent(query)}`;
    }
}

// Handle login form submission
async function handleLogin() {
    console.log('handleLogin called');
    const username = document.getElementById('loginUsername').value;
    const password = document.getElementById('loginPassword').value;

    console.log('Login attempt:', { username: username, hasPassword: !!password });

    if (!username || !password) {
        showError('Please fill in all fields');
        return;
    }

    try {
        console.log('Sending login request...');
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password })
        });

        const result = await response.json();
        console.log('Login response:', { status: response.status, result });

        if (response.ok) {
            console.log('Login successful!');
            // Save user session with new session management
            const userData = {
                username: username,
                // Add more user data as needed
            };

            const tokens = {
                accessToken: result.accessToken,
                idToken: result.idToken,
                refreshToken: result.refreshToken
            };

            console.log('Saving user session...');
            saveUserSession(userData, tokens);

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            if (modal) {
                modal.hide();
            }

            showSuccess('Login successful!');
            console.log('Login process completed - refreshing page to update authentication state');

            // Refresh page to get updated server-side authentication state
            setTimeout(() => {
                window.location.reload();
            }, 1000); // Small delay to show success message
        } else {
            console.log('Login failed:', result.error);
            showError(result.error || 'Login failed');
        }
    } catch (error) {
        console.error('Login error:', error);
        showError('Login failed. Please try again.');
    }
}

// Global variables for signup flow
let currentSignupUsername = '';
let currentSignupEmail = '';

// Global variables for user session
let currentUser = null;
let authTokens = null;

// Alert utility functions
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) return;

    // Create alert element
    const alertId = 'alert-' + Date.now();
    const alertElement = document.createElement('div');
    alertElement.id = alertId;
    alertElement.className = `alert alert-${type} alert-dismissible`;
    alertElement.innerHTML = `
        <div class="container-fluid d-flex align-items-center">
            <div class="flex-grow-1">
                <strong>${getAlertIcon(type)}</strong> ${message}
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    // Add to container
    alertContainer.appendChild(alertElement);

    // Add alert-visible class to main container
    const mainContainer = document.querySelector('.main-container');
    if (mainContainer) {
        mainContainer.classList.add('alert-visible');
    }

    // Auto-dismiss after duration
    if (duration > 0) {
        setTimeout(() => {
            dismissAlert(alertId);
        }, duration);
    }

    // Handle manual dismiss
    const closeBtn = alertElement.querySelector('.btn-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            dismissAlert(alertId);
        });
    }

    return alertId;
}

function dismissAlert(alertId) {
    const alertElement = document.getElementById(alertId);
    if (!alertElement) return;

    // Add dismissing animation class
    alertElement.classList.add('alert-dismissing');

    // Remove after animation
    setTimeout(() => {
        if (alertElement.parentNode) {
            alertElement.parentNode.removeChild(alertElement);
        }

        // Remove alert-visible class if no more alerts
        const alertContainer = document.getElementById('alertContainer');
        if (alertContainer && alertContainer.children.length === 0) {
            const mainContainer = document.querySelector('.main-container');
            if (mainContainer) {
                mainContainer.classList.remove('alert-visible');
            }
        }
    }, 300);
}

function getAlertIcon(type) {
    switch (type) {
        case 'success':
            return '<i class="bi bi-check-circle-fill me-2"></i>';
        case 'danger':
        case 'error':
            return '<i class="bi bi-exclamation-triangle-fill me-2"></i>';
        case 'warning':
            return '<i class="bi bi-exclamation-triangle me-2"></i>';
        case 'info':
        default:
            return '<i class="bi bi-info-circle-fill me-2"></i>';
    }
}

// Convenience functions for different alert types
function showSuccess(message, duration = 5000) {
    return showAlert(message, 'success', duration);
}

function showError(message, duration = 7000) {
    return showAlert(message, 'danger', duration);
}

function showWarning(message, duration = 6000) {
    return showAlert(message, 'warning', duration);
}

function showInfo(message, duration = 5000) {
    return showAlert(message, 'info', duration);
}

// Handle signup form submission
async function handleSignup(e) {
    e.preventDefault();

    console.log('🔍 DEBUG: handleSignup called');

    const usernameEl = document.getElementById('signupUsername');
    const emailEl = document.getElementById('signupEmail');
    const passwordEl = document.getElementById('signupPassword');

    console.log('🔍 DEBUG: Elements found:', {
        usernameEl: usernameEl,
        emailEl: emailEl,
        passwordEl: passwordEl
    });

    console.log('🔍 DEBUG: Element details:', {
        usernameEl: usernameEl ? 'FOUND' : 'NULL',
        emailEl: emailEl ? 'FOUND' : 'NULL',
        passwordEl: passwordEl ? 'FOUND' : 'NULL'
    });

    if (!usernameEl || !emailEl || !passwordEl) {
        console.error('❌ DEBUG: One or more form elements not found!');
        console.error('❌ DEBUG: Missing elements:', {
            username: !usernameEl ? 'MISSING signupUsername' : 'OK',
            email: !emailEl ? 'MISSING signupEmail' : 'OK',
            password: !passwordEl ? 'MISSING signupPassword' : 'OK'
        });
        showError('Form elements not found. Please try again.');
        return;
    }

    const username = usernameEl.value;
    const email = emailEl.value;
    const password = passwordEl.value;

    console.log('🔍 DEBUG: Form values captured:', {
        username: username,
        email: email,
        password: password ? '[HIDDEN]' : 'EMPTY'
    });

    if (!username || !email || !password) {
        showError('Please fill in all required fields');
        return;
    }

    try {
        const requestBody = {
            username,
            email,
            password
        };

        console.log('🔍 DEBUG: Sending to server:', {
            username: requestBody.username,
            email: requestBody.email,
            password: '[HIDDEN]'
        });

        const response = await fetch('/api/auth/signup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        const result = await response.json();

        if (response.ok && result.needsVerification) {
            // Store user info for verification step
            currentSignupUsername = username;
            currentSignupEmail = email;
            currentUserSub = result.userSub;

            console.log('✅ Signup successful, showing verification step');

            // Show verification step
            document.getElementById('signupStep1').style.display = 'none';
            document.getElementById('signupStep2').style.display = 'block';
            document.getElementById('verificationEmail').textContent = email;

            // Update modal title
            document.querySelector('#signupModal .modal-title').textContent = 'Verify Your Email';

            showSuccess('Account created! Please check your email for the verification code.');
        } else if (response.ok) {
            // Close modal and show success message
            const modal = bootstrap.Modal.getInstance(document.getElementById('signupModal'));
            modal.hide();
            showSuccess('Account created successfully!');
        } else {
            console.error('❌ Signup failed:', result.error);
            showError(result.error || 'Signup failed');
        }
    } catch (error) {
        console.error('Signup error:', error);
        showError('Signup failed. Please try again.');
    }
}

// Global variable for user sub
let currentUserSub = '';

// Handle verification form submission
async function handleVerification(e) {
    e.preventDefault();

    const code = document.getElementById('verificationCode').value;

    if (!code) {
        showError('Please enter the verification code');
        return;
    }

    try {
        const response = await fetch('/api/auth/confirm', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: currentSignupUsername,
                code: code
            }),
        });

        const data = await response.json();

        if (response.ok) {
            console.log('✅ Email verification successful');

            // Close modal and show success message
            const modal = bootstrap.Modal.getInstance(document.getElementById('signupModal'));
            modal.hide();

            // Note: resetSignupModal() will be called automatically by the modal's hidden.bs.modal event

            showSuccess('Email verified successfully! You can now sign in.');
        } else {
            console.error('❌ Verification failed:', data.error);
            showError(data.error || 'Verification failed. Please try again.');
        }
    } catch (error) {
        console.error('Verification error:', error);
        showError('An error occurred during verification');
    }
}

// Reset signup modal to initial state
function resetSignupModal() {
    console.log('🔄 Resetting signup modal');

    const step1 = document.getElementById('signupStep1');
    const step2 = document.getElementById('signupStep2');
    const modalTitle = document.querySelector('#signupModal .modal-title');
    const signupForm = document.getElementById('signupForm');
    const verificationCode = document.getElementById('verificationCode');

    if (step1) step1.style.display = 'block';
    if (step2) step2.style.display = 'none';
    if (modalTitle) modalTitle.textContent = 'Join BetCenter.Live';

    // Clear form fields
    if (signupForm) signupForm.reset();
    if (verificationCode) verificationCode.value = '';

    // Clear stored data
    currentSignupUsername = '';
    currentSignupEmail = '';
    currentUserSub = '';
}

// Handle resend code button
async function handleResendCode() {
    if (!currentSignupUsername) {
        showError('No signup session found. Please try signing up again.');
        return;
    }

    try {
        const response = await fetch('/api/auth/resend', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: currentSignupUsername
            }),
        });

        const data = await response.json();

        if (response.ok) {
            showSuccess('Verification code sent to your email!');
        } else {
            console.error('❌ Resend failed:', data.error);
            showError(data.error || 'Failed to resend verification code');
        }
    } catch (error) {
        console.error('Resend error:', error);
        showError('An error occurred while resending code');
    }
}

// Handle back to signup button
function backToSignup() {
    document.getElementById('signupStep2').style.display = 'none';
    document.getElementById('signupStep1').style.display = 'block';
    document.querySelector('#signupModal .modal-title').textContent = 'Join MoneyBags today';
}

// Handle proceed to login button
function proceedToLogin() {
    const signupModal = bootstrap.Modal.getInstance(document.getElementById('signupModal'));
    signupModal.hide();

    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
    loginModal.show();
}

// Show login modal for non-authenticated users
function showLoginModal() {
    console.log('🚀 showLoginModal() called');
    const loginModal = document.getElementById('loginModal');
    console.log('🔍 Login modal element found:', !!loginModal);

    if (loginModal) {
        console.log('✅ Creating Bootstrap modal...');
        const modal = new bootstrap.Modal(loginModal);
        console.log('✅ Showing login modal...');
        modal.show();
    } else {
        console.error('❌ Login modal not found! Cannot show login modal.');
        console.log('🔄 Fallback: redirecting to /login page');
        // Fallback: redirect to login page
        window.location.href = '/login';
    }
}

// Duplicate resetSignupModal function removed - using the safer version above

// User session management functions
function saveUserSession(userData, tokens) {
    currentUser = userData;
    authTokens = tokens;

    // Store in localStorage for persistence
    localStorage.setItem('currentUser', JSON.stringify(userData));
    localStorage.setItem('authTokens', JSON.stringify(tokens));

    // Server-side handles authentication state
}

function loadUserSession() {
    try {
        const storedUser = localStorage.getItem('currentUser');
        const storedTokens = localStorage.getItem('authTokens');

        if (storedUser && storedTokens) {
            currentUser = JSON.parse(storedUser);
            authTokens = JSON.parse(storedTokens);

            // Check if tokens are still valid (basic check)
            if (authTokens.accessToken) {
                setupLogoutButton();
                return true;
            }
        }
    } catch (error) {
        console.error('Error loading user session:', error);
    }

    // Clear invalid session
    clearUserSession();
    return false;
}

function clearUserSession() {
    console.log('Clearing user session...');
    currentUser = null;
    authTokens = null;

    // Clear localStorage
    localStorage.removeItem('currentUser');
    localStorage.removeItem('authTokens');
    console.log('LocalStorage cleared');

    // Redirect to refresh page with server-side authentication state
    console.log('Redirecting to refresh authentication state...');
    window.location.reload();
}

function isUserLoggedIn() {
    return currentUser !== null && authTokens !== null;
}

// Handle logout button click
function handleLogout(e) {
    e.preventDefault();
    console.log('Logout button clicked');
    logout();
}

// Utility function to format viewer numbers
function formatViewers(viewers) {
    if (viewers >= 1000000) {
        return (viewers / 1000000).toFixed(1) + 'M';
    } else if (viewers >= 1000) {
        return (viewers / 1000).toFixed(1) + 'K';
    }
    return viewers.toString();
}

// Utility function to show loading state
function showLoading(elementId) {
    const element = document.getElementById(elementId);
    element.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
}

// Utility function to show error state in specific element
function showElementError(elementId, message) {
    const element = document.getElementById(elementId);
    element.innerHTML = `<div class="alert alert-danger" role="alert">${message}</div>`;
}

// Handle social login
async function handleSocialLogin(provider) {
    showInfo(`${provider.charAt(0).toUpperCase() + provider.slice(1)} login will be implemented once AWS Cognito is configured with social providers.`);
}

// Setup birth date dropdowns
function setupBirthDateDropdowns() {
    const daySelect = document.getElementById('birthDay');
    const yearSelect = document.getElementById('birthYear');

    if (!daySelect || !yearSelect) return;

    // Populate days (1-31)
    for (let i = 1; i <= 31; i++) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = i;
        daySelect.appendChild(option);
    }

    // Populate years (current year - 100 to current year - 13)
    const currentYear = new Date().getFullYear();
    for (let i = currentYear - 13; i >= currentYear - 100; i--) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = i;
        yearSelect.appendChild(option);
    }
}

// Check authentication status - now handled server-side
function checkAuthStatus() {
    // Server-side authentication via JWT cookies
    setupLogoutButton();
}

// UI state is now handled server-side - just set up logout button listener
function setupLogoutButton() {
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        console.log('Logout button found, adding event listener');
        // Remove any existing listeners to avoid duplicates
        logoutBtn.removeEventListener('click', handleLogout);
        logoutBtn.addEventListener('click', handleLogout);
    } else {
        // Try again after a short delay in case the DOM isn't fully loaded
        setTimeout(function() {
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                console.log('Logout button found on retry, adding event listener');
                logoutBtn.removeEventListener('click', handleLogout);
                logoutBtn.addEventListener('click', handleLogout);
            } else {
                console.log('Logout button not found - user may not be authenticated');
            }
        }, 500);
    }
}

// Logout function
async function logout() {
    console.log('Logout function called');

    try {
        console.log('Calling logout API...');
        const response = await fetch('/api/auth/logout', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({}) // Empty body since we use JWT cookies
        });

        if (!response.ok) {
            console.warn('Logout API call failed');
        } else {
            console.log('Logout API call successful');
        }
    } catch (error) {
        console.error('Logout API error:', error);
    }

    // Clear any local storage
    console.log('Clearing local storage...');
    localStorage.removeItem('currentUser');
    localStorage.removeItem('authTokens');

    console.log('Logout completed - refreshing page to update authentication state');

    // Refresh page to get updated server-side authentication state
    window.location.reload();
}

// Make logout function available globally for creator dashboard
window.logout = logout;

// JWT authentication handles creator dashboard access automatically via secure cookies

// UI state is now handled server-side - no client-side updates needed

// Handle mobile search and navigation
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle functionality
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const sidebar = document.getElementById('sidebar');

    if (mobileMenuToggle && sidebar) {
        mobileMenuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            console.log('Mobile menu toggled, sidebar classes:', sidebar.className);
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth < 1200 && sidebar.classList.contains('show')) {
                if (!sidebar.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
    }

    // Mobile notification button
    const mobileNotificationBtn = document.getElementById('mobileNotificationBtn');
    if (mobileNotificationBtn) {
        mobileNotificationBtn.addEventListener('click', function(e) {
            e.preventDefault();
            // TODO: Show notifications functionality
            showInfo('Notifications feature coming soon!');
        });
    }

    // Mobile search functionality
    const mobileSearchBtn = document.getElementById('mobileSearchBtn');
    const mobileSearchInput = document.getElementById('mobileSearchInput');

    if (mobileSearchBtn && mobileSearchInput) {
        mobileSearchBtn.addEventListener('click', function() {
            performMobileSearch();
        });

        mobileSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performMobileSearch();
            }
        });
    }

    // Add direct button click listeners as backup - more aggressive approach
    function setupButtonListeners() {
        const loginButton = document.querySelector('#loginForm button[type="submit"]');
        if (loginButton && !loginButton.hasAttribute('data-click-listener')) {
            console.log('🔧 Adding direct click listener to login button');
            loginButton.addEventListener('click', function(e) {
                console.log('🔥 LOGIN BUTTON CLICKED DIRECTLY!');
                e.preventDefault();
                e.stopPropagation();

                // Get form data manually
                const username = document.getElementById('loginUsername').value;
                const password = document.getElementById('loginPassword').value;

                if (!username || !password) {
                    console.error('❌ Username or password missing');
                    showError('Please enter both username and password');
                    return false;
                }

                console.log('🚀 Calling handleLogin with:', username);
                handleLogin();
                return false;
            });
            loginButton.setAttribute('data-click-listener', 'true');
        }

        const signupButton = document.querySelector('#signupForm button[type="submit"]');
        if (signupButton && !signupButton.hasAttribute('data-click-listener')) {
            console.log('🔧 Adding direct click listener to signup button');
            signupButton.addEventListener('click', function(e) {
                console.log('🔥 SIGNUP BUTTON CLICKED DIRECTLY!');
                e.preventDefault();
                e.stopPropagation();
                handleSignup(e);
                return false;
            });
            signupButton.setAttribute('data-click-listener', 'true');
        }
    }

    // Try to setup button listeners multiple times
    setTimeout(setupButtonListeners, 500);
    setTimeout(setupButtonListeners, 1000);
    setTimeout(setupButtonListeners, 2000);
});

// Perform mobile search
function performMobileSearch() {
    const query = document.getElementById('mobileSearchInput').value.trim();
    if (query) {
        // TODO: Implement search functionality
        console.log('Mobile search query:', query);
        showInfo(`Searching for: ${query}`);
    }
}

// Test function for Following button - can be called from browser console
window.testFollowingButton = function() {
    console.log('🧪 Testing Following button...');

    // Try to find the button
    const followingBtn = document.getElementById('followingNavItem');
    console.log('🔍 Following button found:', !!followingBtn);

    if (followingBtn) {
        console.log('🔍 Button HTML:', followingBtn.outerHTML);
        console.log('🔍 Button data-authenticated:', followingBtn.getAttribute('data-authenticated'));

        // Simulate click
        console.log('🚀 Simulating click...');
        followingBtn.click();
    } else {
        console.error('❌ Following button not found!');

        // Show all buttons for debugging
        const allButtons = document.querySelectorAll('button');
        console.log('🔍 All buttons on page:', allButtons.length);
        allButtons.forEach((btn, index) => {
            console.log(`Button ${index}:`, {
                id: btn.id,
                text: btn.textContent.trim(),
                classes: btn.className
            });
        });
    }
};

// Social functionality - Follow/Unfollow users
window.toggleFollow = async function(username) {
    console.log('🚀 Toggle follow for user:', username);

    try {
        // Check if currently following by looking at button state
        const followBtn = document.getElementById('followBtn') ||
                         document.getElementById('mobileFollowBtn') ||
                         document.getElementById('profileFollowBtn');

        if (!followBtn) {
            console.error('❌ Follow button not found');
            return;
        }

        const isCurrentlyFollowing = followBtn.innerHTML.includes('Following') ||
                                   followBtn.innerHTML.includes('Unfollow');

        const endpoint = isCurrentlyFollowing ? '/api/social/unfollow' : '/api/social/follow';
        const action = isCurrentlyFollowing ? 'Unfollowing' : 'Following';

        console.log(`🔄 ${action} user:`, username);

        // Disable button during request
        followBtn.disabled = true;
        followBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Loading...';

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username
            })
        });

        const data = await response.json();

        if (response.ok && data.success) {
            console.log(`✅ ${action} successful:`, data);

            // Update button state
            if (data.is_following) {
                followBtn.innerHTML = '<i class="bi bi-heart-fill me-1"></i>Following';
                followBtn.classList.remove('btn-primary');
                followBtn.classList.add('btn-success');
            } else {
                followBtn.innerHTML = '<i class="bi bi-heart me-1"></i>Follow';
                followBtn.classList.remove('btn-success');
                followBtn.classList.add('btn-primary');
            }

            // Show success message
            showInfo(data.message);

        } else {
            console.error(`❌ ${action} failed:`, data);
            showError(data.error || `Failed to ${action.toLowerCase()} user`);

            // Reset button
            if (isCurrentlyFollowing) {
                followBtn.innerHTML = '<i class="bi bi-heart-fill me-1"></i>Following';
            } else {
                followBtn.innerHTML = '<i class="bi bi-heart me-1"></i>Follow';
            }
        }

    } catch (error) {
        console.error('❌ Error in toggleFollow:', error);
        showError('Network error. Please try again.');

        // Reset button
        const followBtn = document.getElementById('followBtn') ||
                         document.getElementById('mobileFollowBtn') ||
                         document.getElementById('profileFollowBtn');
        if (followBtn) {
            followBtn.innerHTML = '<i class="bi bi-heart me-1"></i>Follow';
        }
    } finally {
        // Re-enable button
        const followBtn = document.getElementById('followBtn') ||
                         document.getElementById('mobileFollowBtn') ||
                         document.getElementById('profileFollowBtn');
        if (followBtn) {
            followBtn.disabled = false;
        }
    }
};

// Social functionality - Like/Unlike videos
window.toggleLike = async function(videoId) {
    console.log('🚀 Toggle like for video:', videoId);

    try {
        const likeBtn = document.getElementById('likeBtn');
        const likeIcon = document.getElementById('likeIcon');
        const likeText = document.getElementById('likeText');
        const likeCount = document.getElementById('likeCount');

        if (!likeBtn) {
            console.error('❌ Like button not found');
            return;
        }

        const isCurrentlyLiked = likeIcon.classList.contains('bi-hand-thumbs-up-fill');
        const endpoint = isCurrentlyLiked ? '/api/social/unlike' : '/api/social/like';
        const action = isCurrentlyLiked ? 'Unliking' : 'Liking';

        console.log(`🔄 ${action} video:`, videoId);

        // Disable button during request
        likeBtn.disabled = true;

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                video_id: videoId
            })
        });

        const data = await response.json();

        if (response.ok && data.success) {
            console.log(`✅ ${action} successful:`, data);

            // Update button state
            if (data.is_liked) {
                likeIcon.className = 'bi bi-hand-thumbs-up-fill me-1';
                likeText.textContent = 'Liked';
                likeBtn.classList.remove('btn-outline-secondary');
                likeBtn.classList.add('btn-secondary');
            } else {
                likeIcon.className = 'bi bi-hand-thumbs-up me-1';
                likeText.textContent = 'Like';
                likeBtn.classList.remove('btn-secondary');
                likeBtn.classList.add('btn-outline-secondary');
            }

            // Update like count
            if (likeCount) {
                likeCount.textContent = data.like_count;
            }

            // Show success message
            showInfo(data.message);

        } else {
            console.error(`❌ ${action} failed:`, data);
            showError(data.error || `Failed to ${action.toLowerCase()} video`);
        }

    } catch (error) {
        console.error('❌ Error in toggleLike:', error);
        showError('Network error. Please try again.');
    } finally {
        // Re-enable button
        const likeBtn = document.getElementById('likeBtn');
        if (likeBtn) {
            likeBtn.disabled = false;
        }
    }
};

// Make showLoginModal available globally for testing
window.testLoginModal = function() {
    console.log('🧪 Testing login modal...');
    showLoginModal();
};
