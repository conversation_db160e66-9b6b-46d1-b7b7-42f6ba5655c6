#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FUTU API Top Mover Monitor
Monitors top 20 movers every minute, calculates moving averages, and alerts for buy opportunities.

Features:
- Fetches top 20 movers every minute
- Records stock codes in daily list
- Monitors stocks continuously every minute
- Gets 3-minute position data for analysis
- Calculates MA7, MA14, and MA28 moving averages
- Auto-buys when price is below BOLL middle, above MA28, and trend confirmed
- Trend confirmation: price above MA14 for 12+ out of last 20 intervals (60min)
- Auto-sells when price drops below BOLL lower or rises 2% above buy price
- Interactive buy/sell decision prompts
- Data persistence and logging

Requirements:
- FUTU OpenD must be running on localhost:18121
- Valid FUTU account with market data access
- Python 3.7+ with required packages
"""

import time
import logging
import json
import pandas as pd
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Set
import futu as ft
from collections import defaultdict
import threading
import signal
import sys

# Configuration
FUTU_HOST = '127.0.0.1'
FUTU_PORT = 18121
DEFAULT_MARKET = ft.Market.HK  # Hong Kong market
TOP_MOVERS_COUNT = 20
MA_PERIODS = [7, 14, 28]
DATA_INTERVAL_MINUTES = 3
CHECK_INTERVAL_SECONDS = 60
MOVER_UPDATE_INTERVAL_SECONDS = 60
SAVE_INTERVAL_MINUTES = 10
HISTORICAL_DAYS = 30
LOG_LEVEL = 'INFO'
LOG_FILE = 'top_mover_monitor.log'
DATA_DIR = 'data'

# Create directories if they don't exist
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs('logs', exist_ok=True)

# Configure logging with error handling
log_handlers = [logging.StreamHandler()]  # Always have console output

# Try to add file handler
try:
    os.makedirs('logs', exist_ok=True)
    log_handlers.append(logging.FileHandler(os.path.join('logs', LOG_FILE)))
except (PermissionError, OSError) as e:
    print(f"⚠️  Warning: Cannot create log file: {e}")
    print(f"📝 Logging to console only")

logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=log_handlers
)
logger = logging.getLogger(__name__)

class TopMoverMonitor:
    def __init__(self, host=FUTU_HOST, port=FUTU_PORT):
        """Initialize the top mover monitor with FUTU API connection."""
        self.host = host
        self.port = port
        self.quote_ctx = None
        
        # Data storage
        self.daily_movers: Set[str] = set()  # Today's top movers
        self.monitoring_stocks: Set[str] = set()  # Currently monitoring stocks
        self.stock_data: Dict[str, List] = defaultdict(list)  # Historical data
        self.buy_decisions: List[Dict] = []  # Track user decisions

        # Position management
        self.held_positions: Dict[str, Dict] = {}  # {stock_code: {'buy_price': price, 'buy_time': timestamp}}
        self.closed_positions: List[Dict] = []  # [{'code': code, 'buy_price': price, 'sell_price': price, 'profit': profit, 'reason': reason}]
        
        # Configuration
        self.ma_periods = MA_PERIODS
        self.data_interval = DATA_INTERVAL_MINUTES
        self.check_interval = CHECK_INTERVAL_SECONDS
        self.mover_update_interval = MOVER_UPDATE_INTERVAL_SECONDS
        self.top_movers_count = TOP_MOVERS_COUNT
        self.historical_days = HISTORICAL_DAYS
        self.market = DEFAULT_MARKET
        
        # Control flags
        self.running = False
        self.paused = False
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Load existing daily data if available
        self.load_daily_data()

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False
        
    def connect(self) -> bool:
        """Establish connection to FUTU OpenD."""
        try:
            logger.info(f"Attempting to connect to FUTU OpenD at {self.host}:{self.port}")
            self.quote_ctx = ft.OpenQuoteContext(host=self.host, port=self.port)
            
            # Test the connection by getting global state
            ret, data = self.quote_ctx.get_global_state()
            if ret != ft.RET_OK:
                logger.error(f"Connection test failed: {data}")
                return False
            
            logger.info("✅ Connected to FUTU OpenD successfully")
            logger.info(f"Market state: {data}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to connect to FUTU OpenD: {e}")
            logger.error("Please ensure:")
            logger.error("1. FUTU OpenD is running")
            logger.error(f"2. OpenD is listening on {self.host}:{self.port}")
            logger.error("3. You have proper market data permissions")
            return False
    
    def disconnect(self):
        """Close FUTU API connections."""
        if self.quote_ctx:
            self.quote_ctx.close()
        logger.info("Disconnected from FUTU OpenD")
    
    def get_top_movers(self, market=None, count=None) -> List[str]:
        """Get top movers using stock filter sorted by change_rate."""
        if market is None:
            market = self.market
        if count is None:
            count = self.top_movers_count

        try:
            logger.info("Getting top movers using stock filter with change_rate sorting...")

            # Method 1: Use get_stock_filter with AccumulateFilter for change_rate
            try:
                # Create AccumulateFilter for change_rate (based on your working test)
                accumulate_filter = ft.AccumulateFilter()
                accumulate_filter.filter_min = -100  # Minimum change rate -100%
                accumulate_filter.filter_max = 1000   # Maximum change rate 1000%
                accumulate_filter.stock_field = ft.StockField.CHANGE_RATE  # Sort by change rate
                accumulate_filter.is_no_filter = False
                accumulate_filter.sort = ft.SortDir.DESCEND  # Descending order (biggest gainers first)

                accumulate_filter2 = ft.AccumulateFilter()
                accumulate_filter2.filter_min = 10000000  # Minimum change rate -100%
                accumulate_filter2.filter_max = 15000000000   # Maximum change rate 1000%
                accumulate_filter2.stock_field = ft.StockField.TURNOVER  # Sort by change rate
                accumulate_filter2.is_no_filter = False

                ret, data = self.quote_ctx.get_stock_filter(
                    market=market,
                    filter_list=[accumulate_filter, accumulate_filter2],
                    plate_code="HK.Motherboard",
                    begin=0,
                    num=count
                )

                if ret == ft.RET_OK:
                    logger.info(f"✅ AccumulateFilter API call successful")

                    # Handle FUTU API tuple format: (success, total_count, stock_list)
                    top_movers = []
                    

                    if isinstance(data, tuple) and len(data) >= 3:
                        # FUTU API returns (success, total_count, stock_list)
                        success_flag, total_count, stock_list = data[0], data[1], data[2]
                        logger.info(f"FUTU API result: success={success_flag}, total={total_count}, stocks={len(stock_list)}")

                        if stock_list and len(stock_list) > 0:
                            logger.info(f"✅ Found {len(stock_list)} top movers by change rate:")

                            for i, stock in enumerate(stock_list[:count], 1):
                                try:
                                    # Extract stock_code and change_rate from stock object
                                    if hasattr(stock, 'stock_code'):
                                        code = stock.stock_code
                                        change_rate = stock[accumulate_filter]
                                        trade_vol = stock[accumulate_filter2]
                                        stock_name = getattr(stock, 'stock_name', '')

                                        top_movers.append(code)
                                        logger.info(f"  {i}. {code} ({stock_name}): {change_rate:+.2f}% [{trade_vol}]")
                                    else:
                                        # Fallback: try to extract from string representation
                                        stock_str = str(stock)
                                        logger.debug(f"Parsing stock string: {stock_str}")

                                        if 'stock_code:' in stock_str:
                                            # Extract code from string like "stock_code:HK.00352"
                                            code_start = stock_str.find('stock_code:') + len('stock_code:')
                                            code_end = stock_str.find(' ', code_start)
                                            if code_end == -1:
                                                code_end = len(stock_str)
                                            code = stock_str[code_start:code_end].strip()

                                            # Extract change_rate from "change_rate(1):180.0"
                                            change_rate = 0
                                            if 'change_rate(1):' in stock_str:
                                                rate_start = stock_str.find('change_rate(1):') + len('change_rate(1):')
                                                # Look for the end - could be space, comma, or end of string
                                                rate_end = len(stock_str)  # Default to end
                                                for delimiter in [' ', ',', '\n', '\t']:
                                                    pos = stock_str.find(delimiter, rate_start)
                                                    if pos != -1 and pos < rate_end:
                                                        rate_end = pos

                                                rate_str = stock_str[rate_start:rate_end].strip()
                                                logger.debug(f"Extracted rate string: '{rate_str}'")

                                                try:
                                                    change_rate = float(rate_str)
                                                    logger.debug(f"Parsed change_rate: {change_rate}")
                                                except ValueError as e:
                                                    logger.warning(f"Could not parse change_rate '{rate_str}': {e}")
                                                    change_rate = 0

                                            # Extract stock name for better logging
                                            stock_name = ""
                                            if 'stock_name:' in stock_str:
                                                name_start = stock_str.find('stock_name:') + len('stock_name:')
                                                name_end = stock_str.find('change_rate(1):', name_start)
                                                if name_end == -1:
                                                    name_end = len(stock_str)
                                                stock_name = stock_str[name_start:name_end].strip()

                                            top_movers.append(code)
                                            logger.info(f"  {i}. {code} ({stock_name}): {change_rate:+.2f}%")
                                        else:
                                            logger.warning(f"Could not parse stock: {stock}")

                                except Exception as e:
                                    logger.warning(f"Error processing stock {i}: {e}")
                                    continue
                        else:
                            logger.warning("Stock list is empty")
                    else:
                        logger.warning(f"Unexpected data format: {type(data)}, expected tuple with 3 elements")

                    if top_movers:
                        logger.info(f"✅ Found {len(top_movers)} top movers using AccumulateFilter")
                        return top_movers
                    else:
                        logger.warning("No stock codes extracted from AccumulateFilter data")
                else:
                    logger.warning(f"AccumulateFilter failed: ret={ret}, data={data}")

            except Exception as e1:
                logger.warning(f"AccumulateFilter method failed: {e1}")

            # Method 1b: Try AccumulateFilter without sort parameter
            try:
                accumulate_filter = ft.AccumulateFilter()
                accumulate_filter.filter_min = -100
                accumulate_filter.filter_max = 1000
                accumulate_filter.stock_field = ft.StockField.CHANGE_RATE
                accumulate_filter.is_no_filter = False
                # Don't set sort parameter

                ret, data = self.quote_ctx.get_stock_filter(
                    market=market,
                    filter_list=[accumulate_filter],
                    begin=0,
                    num=count * 2  # Get more to sort manually
                )

                if ret == ft.RET_OK:
                    logger.info(f"Got AccumulateFilter data (no sort): {type(data)}")

                    # Handle different data formats
                    top_movers = []

                    if hasattr(data, 'empty') and not data.empty:
                        # DataFrame format
                        logger.info(f"Got {len(data)} stocks (DataFrame format)")
                        if 'change_rate' in data.columns:
                            data_sorted = data.sort_values('change_rate', ascending=False)
                            top_movers = data_sorted['code'].head(count).tolist()

                            logger.info(f"✅ Found {len(top_movers)} top movers (manual sort):")
                            for i, (_, row) in enumerate(data_sorted.head(count).iterrows(), 1):
                                code = row['code']
                                change_rate = row.get('change_rate', 0)
                                logger.info(f"  {i}. {code}: {change_rate:+.2f}%")
                        else:
                            logger.warning("No change_rate column in DataFrame")

                    elif isinstance(data, (list, tuple)) and len(data) > 0:
                        # List/Tuple format - extract stock codes
                        logger.info(f"Got {len(data)} stocks (List/Tuple format)")
                        for i, item in enumerate(data[:count], 1):
                            if hasattr(item, 'get'):
                                code = item.get('code', f'STOCK_{i}')
                            elif hasattr(item, '__getitem__') and len(item) > 0:
                                code = str(item[0])
                            else:
                                code = str(item)

                            top_movers.append(code)
                            logger.info(f"  {i}. {code}")

                    elif hasattr(data, '__len__') and len(data) == 0:
                        logger.warning("AccumulateFilter returned empty data")
                    else:
                        logger.warning(f"Unexpected data format: {type(data)}")

                    if top_movers:
                        logger.info(f"✅ Found {len(top_movers)} stocks from AccumulateFilter (no sort)")
                        return top_movers
                else:
                    logger.warning(f"AccumulateFilter without sort failed: ret={ret}, data={data}")

            except Exception as e1b:
                logger.warning(f"AccumulateFilter without sort failed: {e1b}")

            # Method 2: Try basic stock filter without sorting (fallback)
            try:
                logger.info("Trying basic stock filter as fallback...")
                ret, data = self.quote_ctx.get_stock_filter(
                    market=market,
                    filter_list=[],
                    plate_code_list=[],
                    begin=0,
                    num=count * 2  # Get more to have options
                )

                if ret == ft.RET_OK and not data.empty:
                    # Get market snapshots to sort by change rate manually
                    stock_codes = data['code'].tolist()
                    logger.info(f"Got {len(stock_codes)} stocks, getting market data to sort...")

                    ret_snap, snap_data = self.quote_ctx.get_market_snapshot(stock_codes)
                    if ret_snap == ft.RET_OK and not snap_data.empty:
                        # Sort by change_rate descending
                        snap_data_sorted = snap_data.sort_values('change_rate', ascending=False)
                        top_movers = snap_data_sorted['code'].head(count).tolist()

                        logger.info(f"✅ Found {len(top_movers)} top movers (manual sort):")
                        for i, row in snap_data_sorted.head(count).iterrows():
                            logger.info(f"  {len(top_movers) - len(snap_data_sorted.head(count)) + list(snap_data_sorted.head(count).index).index(i) + 1}. {row['code']}: {row['change_rate']:+.2f}% (${row['last_price']:.2f})")

                        return top_movers
                    else:
                        logger.warning(f"Market snapshot for sorting failed: {snap_data}")
                        # Just return the codes without sorting
                        return stock_codes[:count]
                else:
                    logger.warning(f"Basic stock filter failed: {data}")

            except Exception as e2:
                logger.warning(f"Basic stock filter method failed: {e2}")

            # Method 3: Get from popular plates as fallback
            logger.info("Using plate stocks as fallback...")
            try:
                popular_plates = ['HK.BK1001', 'HK.BK1002', 'HK.BK1003']  # HSI, HSCEI, etc.
                all_stocks = []

                for plate in popular_plates:
                    try:
                        ret, data = self.quote_ctx.get_plate_stock(plate)
                        if ret == ft.RET_OK and not data.empty:
                            stocks = data['code'].tolist()[:10]  # Take 10 from each plate
                            all_stocks.extend(stocks)
                            if len(all_stocks) >= count:
                                break
                    except:
                        continue

                if all_stocks:
                    # Remove duplicates and limit to count
                    unique_stocks = list(dict.fromkeys(all_stocks))[:count]
                    logger.info(f"Got {len(unique_stocks)} stocks from plates")
                    return unique_stocks

            except Exception as e3:
                logger.warning(f"Plate stock method failed: {e3}")

        except Exception as e:
            logger.error(f"Error getting top movers: {e}")

        # Final fallback: Use predefined popular stocks
        fallback_stocks = [
            'HK.00700', 'HK.00005', 'HK.00941', 'HK.00388', 'HK.01299',
            'HK.02318', 'HK.01398', 'HK.00939', 'HK.01810', 'HK.00883',
            'HK.01093', 'HK.00175', 'HK.00027', 'HK.01177', 'HK.02020'
        ]

        logger.warning(f"Using fallback stock list - {len(fallback_stocks)} popular HK stocks")
        return fallback_stocks[:count]
    
    def get_stock_3min_data(self, stock_code: str, max_count=200) -> Optional[pd.DataFrame]:
        """Get 3-minute K-line data for a stock and calculate moving averages."""
        try:
            # Subscribe to 3-minute K-line data
            ret_sub, msg_sub = self.quote_ctx.subscribe([stock_code], [ft.SubType.K_3M], is_first_push=False)
            if ret_sub != ft.RET_OK:
                if "Network interruption" in str(msg_sub):
                    logger.warning(f"Network interruption for {stock_code} - market may be closed")
                else:
                    logger.error(f"Subscribe K-line failed for {stock_code}: {msg_sub}")
                return None

            # Small delay to ensure subscription is processed
            import time
            time.sleep(0.5)

            # Get current K-line data
            ret, df_k = self.quote_ctx.get_cur_kline(
                code=stock_code,
                ktype=ft.KLType.K_3M,  # 3-minute K-line
                autype=ft.AuType.QFQ,  # Forward adjusted
                num=max_count
            )

            if ret != ft.RET_OK:
                if "Network interruption" in str(df_k):
                    logger.warning(f"Network interruption getting K-line for {stock_code} - market may be closed")
                else:
                    logger.error(f"Failed to get K-line for {stock_code}: {df_k}")
                return None

            if df_k.empty:
                logger.warning(f"No K-line data returned for {stock_code}")
                return None

            # Process the data
            df_k['time_key'] = pd.to_datetime(df_k['time_key'])
            df_k.set_index('time_key', inplace=True)
            df_k.sort_index(inplace=True)

            # Calculate moving averages
            df_k['MA7'] = df_k['close'].rolling(window=7).mean()
            df_k['MA14'] = df_k['close'].rolling(window=14).mean()
            df_k['MA28'] = df_k['close'].rolling(window=28).mean()

            # Calculate Bollinger Bands (20-period, 2 standard deviations)
            df_k['BOLL_MIDDLE'] = df_k['close'].rolling(window=20).mean()
            df_k['BOLL_STD'] = df_k['close'].rolling(window=20).std()
            df_k['BOLL_UPPER'] = df_k['BOLL_MIDDLE'] + (df_k['BOLL_STD'] * 2)
            df_k['BOLL_LOWER'] = df_k['BOLL_MIDDLE'] - (df_k['BOLL_STD'] * 2)

            # Remove rows with NaN values for MA and Bollinger Band calculations
            df_k.dropna(subset=['MA7', 'MA14', 'MA28', 'BOLL_MIDDLE', 'BOLL_UPPER', 'BOLL_LOWER'], inplace=True)

            if df_k.empty:
                logger.warning(f"No data left after MA calculation for {stock_code}")
                return None

            logger.debug(f"Retrieved {len(df_k)} data points for {stock_code}")
            return df_k

        except Exception as e:
            if "Network interruption" in str(e):
                logger.warning(f"Network interruption for {stock_code} - this is normal when markets are closed")
            else:
                logger.error(f"Error getting 3-minute data for {stock_code}: {e}")
            return None
    
    def analyze_stock(self, stock_code: str) -> Dict:
        """Analyze a single stock for buy signals."""
        data = self.get_stock_3min_data(stock_code)
        if data is None or len(data) == 0:
            return {
                'code': stock_code, 
                'signal': False, 
                'reason': 'No data available',
                'timestamp': datetime.now().isoformat()
            }
        
        # Get latest values
        latest_close = data['close'].iloc[-1]
        latest_ma7 = data['MA7'].iloc[-1]
        latest_ma14 = data['MA14'].iloc[-1]
        latest_ma28 = data['MA28'].iloc[-1]
        latest_boll_middle = data['BOLL_MIDDLE'].iloc[-1]
        latest_boll_upper = data['BOLL_UPPER'].iloc[-1]
        latest_boll_lower = data['BOLL_LOWER'].iloc[-1]
        
        # Check basic buy signal: current price < BOLL_MIDDLE AND price > MA28
        basic_signal = (latest_close < latest_boll_middle) and (latest_close > latest_ma28)

        # Check trend confirmation: price above MA14 for at least 12 out of last 20 intervals
        trend_confirmed = self.check_trend_confirmation(data, stock_code)

        # Final buy signal requires both conditions
        buy_signal = basic_signal and trend_confirmed

        if buy_signal:
            reason = f"Price ({latest_close:.2f}) below BOLL middle ({latest_boll_middle:.2f}), above MA28 ({latest_ma28:.2f}), and trend confirmed"
        elif basic_signal and not trend_confirmed:
            reason = f"Price in range but trend not confirmed (need 12/20 intervals above MA14)"
        else:
            if latest_close >= latest_boll_middle:
                reason = f"Price ({latest_close:.2f}) above BOLL middle ({latest_boll_middle:.2f})"
            elif latest_close <= latest_ma28:
                reason = f"Price ({latest_close:.2f}) below MA28 ({latest_ma28:.2f})"
            else:
                reason = f"Price ({latest_close:.2f}) not in target range (MA28: {latest_ma28:.2f}, BOLL middle: {latest_boll_middle:.2f})"
        
        return {
            'code': stock_code,
            'current_price': latest_close,
            'signal': buy_signal,
            'reason': reason,
            'MA7': latest_ma7,
            'MA14': latest_ma14,
            'MA28': latest_ma28,
            'BOLL_MIDDLE': latest_boll_middle,
            'BOLL_UPPER': latest_boll_upper,
            'BOLL_LOWER': latest_boll_lower,
            'timestamp': datetime.now().isoformat()
        }

    def check_trend_confirmation(self, data: List, stock_code: str) -> bool:
        """Check if price was above MA14 for at least 12 out of last 20 intervals."""
        try:
            # We need at least 20 data points to check trend
            if len(data) < 20:
                logger.debug(f"Not enough data for trend confirmation: {len(data)} < 20")
                return False

            # Get the last 20 data points
            recent_data = data[-20:]

            # Convert to DataFrame for easier calculation
            df = pd.DataFrame(recent_data)

            # Calculate MA14 for each point
            df['MA14'] = df['close'].rolling(window=14, min_periods=14).mean()

            # Count how many times price was above MA14 in the last 20 intervals
            # Only check the last 20 points where MA14 is available
            valid_data = df.dropna()
            if len(valid_data) < 20:
                logger.debug(f"Not enough valid MA14 data for trend confirmation: {len(valid_data)} < 20")
                return False

            # Take the last 20 valid points
            last_20 = valid_data.tail(20)
            above_ma14_count = (last_20['close'] > last_20['MA14']).sum()

            trend_confirmed = above_ma14_count >= 12

            logger.debug(f"Trend confirmation for {stock_code}: {above_ma14_count}/20 intervals above MA14 (need ≥12)")

            return trend_confirmed

        except Exception as e:
            logger.error(f"Error checking trend confirmation for {stock_code}: {e}")
            return False

    def prompt_user_buy_decision(self, analysis: Dict) -> bool:
        """Prompt user for buy decision with detailed information."""
        print(f"\n{'='*70}")
        print(f"🚨 BUY ALERT for {analysis['code']} 🚨")
        print(f"{'='*70}")
        print(f"📊 Current Price: ${analysis['current_price']:.2f}")
        print(f"📈 Signal Reason: {analysis['reason']}")
        print(f"\n📉 Moving Averages:")
        print(f"  MA7:  ${analysis['MA7']:.2f}")
        print(f"  MA14: ${analysis['MA14']:.2f}")
        print(f"  MA28: ${analysis['MA28']:.2f}")

        print(f"\n📊 Bollinger Bands:")
        print(f"  Upper:  ${analysis['BOLL_UPPER']:.2f}")
        print(f"  Middle: ${analysis['BOLL_MIDDLE']:.2f}")
        print(f"  Lower:  ${analysis['BOLL_LOWER']:.2f}")

        # Calculate percentage differences
        price = analysis['current_price']
        ma7_diff = ((price - analysis['MA7']) / analysis['MA7']) * 100
        ma14_diff = ((price - analysis['MA14']) / analysis['MA14']) * 100
        ma28_diff = ((price - analysis['MA28']) / analysis['MA28']) * 100
        boll_middle_diff = ((price - analysis['BOLL_MIDDLE']) / analysis['BOLL_MIDDLE']) * 100

        print(f"\n📊 Price vs Moving Averages:")
        print(f"  vs MA7:  {ma7_diff:+.2f}% ({'🔴 Below' if ma7_diff < 0 else '🟢 Above'})")
        print(f"  vs MA14: {ma14_diff:+.2f}% ({'🔴 Below' if ma14_diff < 0 else '🟢 Above'})")
        print(f"  vs MA28: {ma28_diff:+.2f}% ({'🔴 Below' if ma28_diff < 0 else '🟢 Above'})")

        print(f"\n📊 Price vs Bollinger Bands:")
        print(f"  vs BOLL Middle: {boll_middle_diff:+.2f}% ({'🔴 Below' if boll_middle_diff < 0 else '🟢 Above'})")

        # Show position within Bollinger Bands
        boll_range = analysis['BOLL_UPPER'] - analysis['BOLL_LOWER']
        if boll_range > 0:
            boll_position = ((price - analysis['BOLL_LOWER']) / boll_range) * 100
            print(f"  Position in BOLL: {boll_position:.1f}% (0%=Lower, 50%=Middle, 100%=Upper)")

        print(f"\n⏰ Time: {analysis['timestamp']}")
        print(f"{'='*70}")

        # Add context about the buy signal
        print(f"💡 Analysis: Price is between MA28 and BOLL middle, indicating potential buying opportunity")
        print(f"📊 Signal: MA28 < Price < BOLL Middle suggests a pullback from the mean")
        print(f"🎯 Strategy: Price below Bollinger middle but above long-term MA28 support")
        print(f"⚠️  Remember: This is not financial advice. Do your own research!")

        while True:
            print(f"\n🤔 What would you like to do?")
            decision = input("(b)uy / (s)kip / (p)ause monitoring / (q)uit: ").lower().strip()

            if decision in ['b', 'buy', 'y', 'yes']:
                print(f"✅ User decided to BUY {analysis['code']}")
                self.buy_decisions.append({
                    'code': analysis['code'],
                    'price': analysis['current_price'],
                    'timestamp': analysis['timestamp'],
                    'decision': 'BUY',
                    'MA7': analysis['MA7'],
                    'MA14': analysis['MA14'],
                    'MA28': analysis['MA28']
                })
                return True
            elif decision in ['s', 'skip', 'n', 'no']:
                print(f"⏭️  User decided to SKIP {analysis['code']}")
                self.buy_decisions.append({
                    'code': analysis['code'],
                    'price': analysis['current_price'],
                    'timestamp': analysis['timestamp'],
                    'decision': 'SKIP'
                })
                return False
            elif decision in ['p', 'pause']:
                print("⏸️  Monitoring paused. Press Enter to resume...")
                input()
                print("▶️  Monitoring resumed")
                continue
            elif decision in ['q', 'quit']:
                print("👋 Quitting program...")
                self.running = False
                return False
            else:
                print("❌ Please enter 'b' for buy, 's' for skip, 'p' for pause, or 'q' to quit")

    def auto_buy_stock(self, analysis: Dict):
        """Automatically buy stock when signal is detected."""
        stock_code = analysis['code']
        buy_price = analysis['current_price']

        # Record the position
        self.held_positions[stock_code] = {
            'buy_price': buy_price,
            'buy_time': datetime.now().isoformat(),
            'analysis': analysis
        }

        logger.info(f"🟢 AUTO-BUY: {stock_code} at ${buy_price:.2f}")
        print(f"🟢 BOUGHT: {stock_code} at ${buy_price:.2f}")

    def check_sell_conditions(self, analysis: Dict):
        """Check if held position should be sold."""
        stock_code = analysis['code']
        current_price = analysis['current_price']
        position = self.held_positions[stock_code]
        buy_price = position['buy_price']

        # Sell conditions:
        # 1. Price drops below BOLL lower
        # 2. Price rises 2% above buy price

        profit_pct = ((current_price - buy_price) / buy_price) * 100

        should_sell = False
        sell_reason = ""

        if current_price < analysis['BOLL_LOWER']:
            should_sell = True
            sell_reason = f"Price ${current_price:.2f} below BOLL lower ${analysis['BOLL_LOWER']:.2f}"
        elif profit_pct >= 2.0:
            should_sell = True
            sell_reason = f"2% profit target reached: {profit_pct:+.2f}%"

        if should_sell:
            self.auto_sell_stock(stock_code, current_price, sell_reason)

    def auto_sell_stock(self, stock_code: str, sell_price: float, reason: str):
        """Automatically sell stock."""
        position = self.held_positions.pop(stock_code)
        buy_price = position['buy_price']
        profit = sell_price - buy_price
        profit_pct = ((sell_price - buy_price) / buy_price) * 100

        # Record closed position
        self.closed_positions.append({
            'code': stock_code,
            'buy_price': buy_price,
            'sell_price': sell_price,
            'profit': profit,
            'profit_pct': profit_pct,
            'reason': reason,
            'buy_time': position['buy_time'],
            'sell_time': datetime.now().isoformat()
        })

        logger.info(f"🔴 AUTO-SELL: {stock_code} at ${sell_price:.2f} | Profit: ${profit:+.2f} ({profit_pct:+.2f}%)")
        print(f"🔴 SOLD: {stock_code} at ${sell_price:.2f} | Profit: ${profit:+.2f} ({profit_pct:+.2f}%) | {reason}")

    def show_portfolio_status(self):
        """Show current holdings and recent trades."""
        print(f"\n📊 PORTFOLIO STATUS")
        print(f"{'='*50}")

        # Show held positions
        if self.held_positions:
            print(f"🟢 HELD POSITIONS ({len(self.held_positions)}):")
            for code, position in self.held_positions.items():
                buy_price = position['buy_price']
                print(f"  {code}: Bought at ${buy_price:.2f}")
        else:
            print(f"🟢 HELD POSITIONS: None")

        # Show recent closed positions (last 5)
        if self.closed_positions:
            recent_trades = self.closed_positions[-5:]
            print(f"\n🔴 RECENT TRADES ({len(recent_trades)}):")
            total_profit = sum(trade['profit'] for trade in self.closed_positions)
            for trade in recent_trades:
                print(f"  {trade['code']}: ${trade['profit']:+.2f} ({trade['profit_pct']:+.2f}%) - {trade['reason']}")
            print(f"\n💰 TOTAL P&L: ${total_profit:+.2f}")
        else:
            print(f"\n🔴 RECENT TRADES: None")

        print(f"{'='*50}")

    def monitor_stocks(self):
        """Monitor stocks for buy signals."""
        if not self.monitoring_stocks:
            logger.info("No stocks to monitor")
            return True

        logger.info(f"Monitoring {len(self.monitoring_stocks)} stocks for buy signals")

        for stock_code in self.monitoring_stocks.copy():
            if not self.running:
                break

            try:
                analysis = self.analyze_stock(stock_code)

                if analysis['signal'] and stock_code not in self.held_positions:
                    # Auto-buy the stock
                    self.auto_buy_stock(analysis)
                elif stock_code in self.held_positions:
                    # Check if we should sell held position
                    self.check_sell_conditions(analysis)
                else:
                    logger.debug(f"No buy signal for {stock_code}: {analysis['reason']}")

            except Exception as e:
                logger.error(f"Error monitoring {stock_code}: {e}")

        return True

    def update_daily_movers(self):
        """Update the list of daily top movers."""
        logger.info("Updating daily top movers...")
        new_movers = self.get_top_movers()

        if not new_movers:
            logger.warning("No top movers retrieved")
            return

        # Add new movers to monitoring list
        added_count = 0
        for stock_code in new_movers:
            if stock_code not in self.daily_movers:
                self.daily_movers.add(stock_code)
                self.monitoring_stocks.add(stock_code)
                logger.info(f"➕ Added new mover to monitoring: {stock_code}")
                added_count += 1

        if added_count > 0:
            logger.info(f"Added {added_count} new movers")

        logger.info(f"📊 Total daily movers: {len(self.daily_movers)}")
        logger.info(f"👀 Currently monitoring: {len(self.monitoring_stocks)}")

    def save_daily_data(self):
        """Save daily movers data to file."""
        today = datetime.now().strftime('%Y-%m-%d')
        filename = os.path.join(DATA_DIR, f"daily_movers_{today}.json")

        data = {
            'date': today,
            'movers': list(self.daily_movers),
            'monitoring_stocks': list(self.monitoring_stocks),
            'monitoring_count': len(self.monitoring_stocks),
            'buy_decisions': self.buy_decisions,
            'held_positions': self.held_positions,
            'closed_positions': self.closed_positions,
            'timestamp': datetime.now().isoformat()
        }

        try:
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"💾 Saved daily data to {filename}")
        except Exception as e:
            logger.error(f"Failed to save daily data: {e}")

    def load_daily_data(self):
        """Load existing daily data if available."""
        today = datetime.now().strftime('%Y-%m-%d')
        filename = os.path.join(DATA_DIR, f"daily_movers_{today}.json")

        if os.path.exists(filename):
            try:
                with open(filename, 'r') as f:
                    data = json.load(f)

                self.daily_movers = set(data.get('movers', []))
                self.monitoring_stocks = set(data.get('monitoring_stocks', []))
                self.buy_decisions = data.get('buy_decisions', [])
                self.held_positions = data.get('held_positions', {})
                self.closed_positions = data.get('closed_positions', [])

                logger.info(f"📂 Loaded daily data from {filename}")
                logger.info(f"📊 Loaded {len(self.daily_movers)} daily movers")
                logger.info(f"👀 Loaded {len(self.monitoring_stocks)} monitoring stocks")

            except Exception as e:
                logger.error(f"Failed to load daily data: {e}")
        else:
            logger.info("No existing daily data found, starting fresh")

    def display_status(self):
        """Display current monitoring status."""
        print(f"\n{'='*50}")
        print(f"📊 MONITORING STATUS")
        print(f"{'='*50}")
        print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📈 Daily movers: {len(self.daily_movers)}")
        print(f"👀 Monitoring stocks: {len(self.monitoring_stocks)}")
        print(f"💰 Buy decisions made: {len(self.buy_decisions)}")

        if self.buy_decisions:
            buy_count = sum(1 for d in self.buy_decisions if d['decision'] == 'BUY')
            skip_count = len(self.buy_decisions) - buy_count
            print(f"   ✅ Bought: {buy_count}")
            print(f"   ⏭️  Skipped: {skip_count}")

        print(f"{'='*50}")

    def run(self):
        """Main monitoring loop."""
        if not self.connect():
            return

        logger.info("🚀 Starting Top Mover Monitor...")
        print(f"\n🎯 Top Mover Monitor Started!")
        print(f"📊 Monitoring top {self.top_movers_count} movers every {self.mover_update_interval}s")
        print(f"🔍 Checking for buy signals every {self.check_interval}s")
        print(f"📈 Using MA periods: {self.ma_periods}")
        print(f"💾 Data saved to: {DATA_DIR}/")
        print(f"📝 Logs saved to: logs/{LOG_FILE}")

        self.running = True

        try:
            last_mover_update = 0
            last_save_time = datetime.now()

            # Initial update of top movers
            self.update_daily_movers()
            self.display_status()

            while self.running:
                current_time = time.time()

                # Update top movers based on configuration
                if current_time - last_mover_update >= self.mover_update_interval:
                    self.update_daily_movers()
                    last_mover_update = current_time

                # Monitor stocks for buy signals
                if self.monitoring_stocks and self.running:
                    continue_monitoring = self.monitor_stocks()
                    if not continue_monitoring or not self.running:
                        break

                    # Show portfolio status after monitoring
                    self.show_portfolio_status()

                # Save data periodically
                now = datetime.now()
                if (now - last_save_time).total_seconds() >= SAVE_INTERVAL_MINUTES * 60:
                    self.save_daily_data()
                    self.display_status()
                    last_save_time = now

                # Wait before next check
                if self.running:
                    logger.info(f"⏳ Waiting {self.check_interval}s before next check...")
                    time.sleep(self.check_interval)

        except KeyboardInterrupt:
            logger.info("⏹️  Monitoring stopped by user (Ctrl+C)")
        except Exception as e:
            logger.error(f"❌ Unexpected error in main loop: {e}")
        finally:
            logger.info("🔄 Saving final data and disconnecting...")
            self.save_daily_data()
            self.display_status()
            self.disconnect()
            logger.info("👋 Top Mover Monitor stopped")

def print_banner():
    """Print application banner."""
    banner = """
╔══════════════════════════════════════════════════════════════════════╗
║                        FUTU Top Mover Monitor                        ║
║                                                                      ║
║  📈 Monitors top 20 movers every minute                             ║
║  📊 Calculates MA7, MA14, MA28 from 3-minute data                   ║
║  � Auto-buys when MA28 < price < BOLL middle                      ║
║  � Trend: price > MA14 for 12+ of last 20 intervals (60min)      ║
║  �🔴 Auto-sells when price < BOLL lower OR profit >= 2%             ║
║  💰 Automatic position management & P&L tracking                   ║
║  💾 Automatic data persistence                                       ║
║                                                                      ║
║  ⚠️  Make sure FUTU OpenD is running on localhost:18121             ║
╚══════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """Main function to run the top mover monitor."""
    print_banner()

    # Check if data directory exists and create if needed
    if not os.path.exists(DATA_DIR):
        os.makedirs(DATA_DIR)
        print(f"📁 Created data directory: {DATA_DIR}")

    if not os.path.exists('logs'):
        os.makedirs('logs')
        print(f"📁 Created logs directory: logs")

    print(f"🔧 Configuration:")
    print(f"   Host: {FUTU_HOST}:{FUTU_PORT}")
    print(f"   Market: {DEFAULT_MARKET}")
    print(f"   Top movers count: {TOP_MOVERS_COUNT}")
    print(f"   Check interval: {CHECK_INTERVAL_SECONDS}s")
    print(f"   MA periods: {MA_PERIODS}")

    try:
        monitor = TopMoverMonitor()
        monitor.run()
    except Exception as e:
        logger.error(f"❌ Failed to start monitor: {e}")
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
